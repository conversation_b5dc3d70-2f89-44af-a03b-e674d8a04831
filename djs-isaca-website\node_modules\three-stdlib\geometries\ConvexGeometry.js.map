{"version": 3, "file": "ConvexGeometry.js", "sources": ["../../src/geometries/ConvexGeometry.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute } from 'three'\nimport { ConvexHull } from '../math/ConvexHull'\n\nclass ConvexGeometry extends BufferGeometry {\n  constructor(points = []) {\n    super()\n\n    // buffers\n\n    const vertices = []\n    const normals = []\n\n    const convexHull = new ConvexHull().setFromPoints(points)\n\n    // generate vertices and normals\n\n    const faces = convexHull.faces\n\n    for (let i = 0; i < faces.length; i++) {\n      const face = faces[i]\n      let edge = face.edge\n\n      // we move along a doubly-connected edge list to access all face points (see HalfEdge docs)\n\n      do {\n        const point = edge.head().point\n\n        vertices.push(point.x, point.y, point.z)\n        normals.push(face.normal.x, face.normal.y, face.normal.z)\n\n        edge = edge.next\n      } while (edge !== face.edge)\n    }\n\n    // build geometry\n\n    this.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n    this.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n  }\n}\n\nexport { ConvexGeometry }\n"], "names": [], "mappings": ";;AAGA,MAAM,uBAAuB,eAAe;AAAA,EAC1C,YAAY,SAAS,IAAI;AACvB,UAAO;AAIP,UAAM,WAAW,CAAE;AACnB,UAAM,UAAU,CAAE;AAElB,UAAM,aAAa,IAAI,aAAa,cAAc,MAAM;AAIxD,UAAM,QAAQ,WAAW;AAEzB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,OAAO,MAAM,CAAC;AACpB,UAAI,OAAO,KAAK;AAIhB,SAAG;AACD,cAAM,QAAQ,KAAK,KAAI,EAAG;AAE1B,iBAAS,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACvC,gBAAQ,KAAK,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC;AAExD,eAAO,KAAK;AAAA,MACpB,SAAe,SAAS,KAAK;AAAA,IACxB;AAID,SAAK,aAAa,YAAY,IAAI,uBAAuB,UAAU,CAAC,CAAC;AACrE,SAAK,aAAa,UAAU,IAAI,uBAAuB,SAAS,CAAC,CAAC;AAAA,EACnE;AACH;"}