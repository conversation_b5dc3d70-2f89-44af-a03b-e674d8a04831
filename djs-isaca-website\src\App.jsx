import React, { useState, useEffect, useRef, Suspense, lazy } from 'react';
import { gsap } from 'gsap';
import LoadingScreen from './components/LoadingScreen';
import Animated3D from './components/Animated3D';
import Navigation from './components/Navigation';
import ScrollAnimations from './components/ScrollAnimations';
import ContactSection from './components/ContactSection';
import './styles/main.css';

// Lazy load the Three.js scene for better performance
const ThreeScene = lazy(() => import('./components/ThreeScene'));

function App() {
  const [loading, setLoading] = useState(true);
  const headerRef = useRef();
  const aboutRef = useRef();
  const featuresRef = useRef();
  const footerRef = useRef();

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 2500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!loading) {
      // Animate sections on load
      const tl = gsap.timeline();

      tl.fromTo(headerRef.current,
        { opacity: 0, y: -50 },
        { opacity: 1, y: 0, duration: 1, ease: "power2.out" }
      )
      .fromTo(aboutRef.current,
        { opacity: 0, y: 50 },
        { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" },
        "-=0.5"
      )
      .fromTo(featuresRef.current,
        { opacity: 0, y: 50 },
        { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" },
        "-=0.3"
      )
      .fromTo(footerRef.current,
        { opacity: 0 },
        { opacity: 1, duration: 0.5, ease: "power2.out" },
        "-=0.2"
      );

      // Animate feature cards individually
      const featureCards = featuresRef.current.querySelectorAll('.feature-card');
      gsap.fromTo(featureCards,
        { opacity: 0, y: 30, scale: 0.9 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          stagger: 0.2,
          ease: "back.out(1.7)",
          delay: 1.5
        }
      );
    }
  }, [loading]);

  return (
    <div className="app-bg">
      {loading ? (
        <LoadingScreen />
      ) : (
        <>
          <Navigation />
          <ScrollAnimations />
          <header className="header" ref={headerRef}>
            <h1>DJS ISACA</h1>
            <p>DJSCE's Student Chapter for Cybersecurity</p>
            <span className="tagline">#securityisamyth</span>
          </header>
          <Animated3D />
          <Suspense fallback={<div style={{height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#00ffe7'}}>Loading 3D Scene...</div>}>
            <ThreeScene />
          </Suspense>
          <section className="about" ref={aboutRef}>
            <h2>About Us</h2>
            <p>
              We are poised to build a safe online community, starting innovation right here.
              DJS ISACA is the premier cybersecurity student chapter at DJSCE, dedicated to
              fostering knowledge and awareness in information security.
            </p>
          </section>

          <section className="features" ref={featuresRef}>
            <h2>What We Do</h2>
            <div className="feature-grid">
              <div className="feature-card">
                <div className="feature-icon">🔐</div>
                <h3>Security Workshops</h3>
                <p>Hands-on training in ethical hacking, penetration testing, and security tools</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">🎯</div>
                <h3>CTF Competitions</h3>
                <p>Capture The Flag events to sharpen your cybersecurity skills</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">👥</div>
                <h3>Community</h3>
                <p>Connect with like-minded security enthusiasts and industry professionals</p>
              </div>
            </div>
          </section>

          <ContactSection />

          <footer className="footer" ref={footerRef}>
            <p>&copy; 2024 DJS ISACA Student Chapter | Building Tomorrow's Cybersecurity Leaders</p>
          </footer>
        </>
      )}
    </div>
  );
}

export default App;