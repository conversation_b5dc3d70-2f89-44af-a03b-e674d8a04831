{"version": 3, "file": "WireframeGeometry2.cjs", "sources": ["../../src/lines/WireframeGeometry2.js"], "sourcesContent": ["import { WireframeGeometry } from 'three'\nimport { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\n\nclass WireframeGeometry2 extends LineSegmentsGeometry {\n  constructor(geometry) {\n    super()\n\n    this.isWireframeGeometry2 = true\n\n    this.type = 'WireframeGeometry2'\n\n    this.fromWireframeGeometry(new WireframeGeometry(geometry))\n\n    // set colors, maybe\n  }\n}\n\nexport { WireframeGeometry2 }\n"], "names": ["LineSegmentsGeometry", "WireframeGeometry"], "mappings": ";;;;AAGA,MAAM,2BAA2BA,qBAAAA,qBAAqB;AAAA,EACpD,YAAY,UAAU;AACpB,UAAO;AAEP,SAAK,uBAAuB;AAE5B,SAAK,OAAO;AAEZ,SAAK,sBAAsB,IAAIC,MAAiB,kBAAC,QAAQ,CAAC;AAAA,EAG3D;AACH;;"}