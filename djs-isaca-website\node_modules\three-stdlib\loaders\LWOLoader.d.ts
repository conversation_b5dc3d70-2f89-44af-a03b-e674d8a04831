import { Loader, LoadingManager, Material, Object3D } from 'three'

export interface LWO {
  materials: Material[]
  meshes: Object3D[]
}

export interface LWOLoaderParameters {
  /**
   * Base content delivery folder path, use when it differs from Lightwave default structure
   */
  resourcePath?: string
}

export class LWOLoader extends Loader {
  constructor(manager?: LoadingManager, parameters?: LWOLoaderParameters)

  load(
    url: string,
    onLoad: (lwo: LWO) => void,
    onProgress?: (event: ProgressEvent) => void,
    onError?: (event: ErrorEvent) => void,
  ): void
  loadAsync(url: string, onProgress?: (event: ProgressEvent) => void): Promise<LWO>
  parse(data: A<PERSON>yBuffer, path: string, modelName: string): LWO
}
