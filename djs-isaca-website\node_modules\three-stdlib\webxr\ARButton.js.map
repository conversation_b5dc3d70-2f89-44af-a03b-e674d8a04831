{"version": 3, "file": "ARButton.js", "sources": ["../../src/webxr/ARButton.ts"], "sourcesContent": ["import { WebGLRenderer } from 'three'\n\nconst ARButton = {\n  createButton(renderer: WebG<PERSON>enderer, sessionInit: XRSessionInit = {}): HTMLButtonElement | HTMLAnchorElement {\n    const button = document.createElement('button')\n\n    function showStartAR(/*device*/): void {\n      if ((sessionInit as any).domOverlay === undefined) {\n        const overlay = document.createElement('div')\n        overlay.style.display = 'none'\n        document.body.appendChild(overlay)\n\n        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')\n        svg.setAttribute('width', '38px')\n        svg.setAttribute('height', '38px')\n        svg.style.position = 'absolute'\n        svg.style.right = '20px'\n        svg.style.top = '20px'\n        svg.addEventListener('click', function () {\n          currentSession?.end()\n        })\n        overlay.appendChild(svg)\n\n        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path')\n        path.setAttribute('d', 'M 12,12 L 28,28 M 28,12 12,28')\n        path.setAttribute('stroke', '#fff')\n        path.setAttribute('stroke-width', '2px')\n        svg.appendChild(path)\n\n        if (sessionInit.optionalFeatures === undefined) {\n          sessionInit.optionalFeatures = []\n        }\n\n        sessionInit.optionalFeatures.push('dom-overlay')\n        ;(sessionInit as any).domOverlay = { root: overlay }\n      }\n\n      //\n\n      let currentSession: XRSession | null = null\n\n      async function onSessionStarted(session: XRSession): Promise<void> {\n        session.addEventListener('end', onSessionEnded)\n\n        renderer.xr.setReferenceSpaceType('local')\n\n        await renderer.xr.setSession(session as any)\n\n        button.textContent = 'STOP AR'\n        ;(sessionInit as any).domOverlay!.root.style.display = ''\n\n        currentSession = session\n      }\n\n      function onSessionEnded(/*event*/): void {\n        currentSession!.removeEventListener('end', onSessionEnded)\n\n        button.textContent = 'START AR'\n        ;(sessionInit as any).domOverlay!.root.style.display = 'none'\n\n        currentSession = null\n      }\n\n      //\n\n      button.style.display = ''\n\n      button.style.cursor = 'pointer'\n      button.style.left = 'calc(50% - 50px)'\n      button.style.width = '100px'\n\n      button.textContent = 'START AR'\n\n      button.onmouseenter = (): void => {\n        button.style.opacity = '1.0'\n      }\n\n      button.onmouseleave = (): void => {\n        button.style.opacity = '0.5'\n      }\n\n      button.onclick = (): void => {\n        if (currentSession === null) {\n          ;(navigator as Navigator).xr!.requestSession('immersive-ar', sessionInit).then(onSessionStarted)\n        } else {\n          currentSession.end()\n        }\n      }\n    }\n\n    function disableButton(): void {\n      button.style.display = ''\n\n      button.style.cursor = 'auto'\n      button.style.left = 'calc(50% - 75px)'\n      button.style.width = '150px'\n\n      button.onmouseenter = null\n      button.onmouseleave = null\n\n      button.onclick = null\n    }\n\n    function showARNotSupported(): void {\n      disableButton()\n\n      button.textContent = 'AR NOT SUPPORTED'\n    }\n\n    function stylizeElement(element: HTMLElement): void {\n      element.style.position = 'absolute'\n      element.style.bottom = '20px'\n      element.style.padding = '12px 6px'\n      element.style.border = '1px solid #fff'\n      element.style.borderRadius = '4px'\n      element.style.background = 'rgba(0,0,0,0.1)'\n      element.style.color = '#fff'\n      element.style.font = 'normal 13px sans-serif'\n      element.style.textAlign = 'center'\n      element.style.opacity = '0.5'\n      element.style.outline = 'none'\n      element.style.zIndex = '999'\n    }\n\n    if ('xr' in navigator) {\n      button.id = 'ARButton'\n      button.style.display = 'none'\n\n      stylizeElement(button)\n\n      // Query for session mode\n      ;(navigator as Navigator)\n        .xr!.isSessionSupported('immersive-ar')\n        .then(function (supported: boolean) {\n          supported ? showStartAR() : showARNotSupported()\n        })\n        .catch(showARNotSupported)\n\n      return button\n    } else {\n      const message = document.createElement('a')\n\n      if (window.isSecureContext === false) {\n        message.href = document.location.href.replace(/^http:/, 'https:')\n        message.innerHTML = 'WEBXR NEEDS HTTPS' // TODO Improve message\n      } else {\n        message.href = 'https://immersiveweb.dev/'\n        message.innerHTML = 'WEBXR NOT AVAILABLE'\n      }\n\n      message.style.left = 'calc(50% - 90px)'\n      message.style.width = '180px'\n      message.style.textDecoration = 'none'\n\n      stylizeElement(message)\n\n      return message\n    }\n  },\n}\n\nexport { ARButton }\n"], "names": [], "mappings": "AAEA,MAAM,WAAW;AAAA,EACf,aAAa,UAAyB,cAA6B,IAA2C;AACtG,UAAA,SAAS,SAAS,cAAc,QAAQ;AAE9C,aAAS,cAA8B;AAChC,UAAA,YAAoB,eAAe,QAAW;AAC3C,cAAA,UAAU,SAAS,cAAc,KAAK;AAC5C,gBAAQ,MAAM,UAAU;AACf,iBAAA,KAAK,YAAY,OAAO;AAEjC,cAAM,MAAM,SAAS,gBAAgB,8BAA8B,KAAK;AACpE,YAAA,aAAa,SAAS,MAAM;AAC5B,YAAA,aAAa,UAAU,MAAM;AACjC,YAAI,MAAM,WAAW;AACrB,YAAI,MAAM,QAAQ;AAClB,YAAI,MAAM,MAAM;AACZ,YAAA,iBAAiB,SAAS,WAAY;AACxC,2DAAgB;AAAA,QAAI,CACrB;AACD,gBAAQ,YAAY,GAAG;AAEvB,cAAM,OAAO,SAAS,gBAAgB,8BAA8B,MAAM;AACrE,aAAA,aAAa,KAAK,+BAA+B;AACjD,aAAA,aAAa,UAAU,MAAM;AAC7B,aAAA,aAAa,gBAAgB,KAAK;AACvC,YAAI,YAAY,IAAI;AAEhB,YAAA,YAAY,qBAAqB,QAAW;AAC9C,sBAAY,mBAAmB;QACjC;AAEY,oBAAA,iBAAiB,KAAK,aAAa;AAC7C,oBAAoB,aAAa,EAAE,MAAM,QAAQ;AAAA,MACrD;AAIA,UAAI,iBAAmC;AAEvC,qBAAe,iBAAiB,SAAmC;AACzD,gBAAA,iBAAiB,OAAO,cAAc;AAErC,iBAAA,GAAG,sBAAsB,OAAO;AAEnC,cAAA,SAAS,GAAG,WAAW,OAAc;AAE3C,eAAO,cAAc;AACnB,oBAAoB,WAAY,KAAK,MAAM,UAAU;AAEtC,yBAAA;AAAA,MACnB;AAEA,eAAS,iBAAgC;AACvB,uBAAA,oBAAoB,OAAO,cAAc;AAEzD,eAAO,cAAc;AACnB,oBAAoB,WAAY,KAAK,MAAM,UAAU;AAEtC,yBAAA;AAAA,MACnB;AAIA,aAAO,MAAM,UAAU;AAEvB,aAAO,MAAM,SAAS;AACtB,aAAO,MAAM,OAAO;AACpB,aAAO,MAAM,QAAQ;AAErB,aAAO,cAAc;AAErB,aAAO,eAAe,MAAY;AAChC,eAAO,MAAM,UAAU;AAAA,MAAA;AAGzB,aAAO,eAAe,MAAY;AAChC,eAAO,MAAM,UAAU;AAAA,MAAA;AAGzB,aAAO,UAAU,MAAY;AAC3B,YAAI,mBAAmB,MAAM;AACzB,oBAAwB,GAAI,eAAe,gBAAgB,WAAW,EAAE,KAAK,gBAAgB;AAAA,QAAA,OAC1F;AACL,yBAAe,IAAI;AAAA,QACrB;AAAA,MAAA;AAAA,IAEJ;AAEA,aAAS,gBAAsB;AAC7B,aAAO,MAAM,UAAU;AAEvB,aAAO,MAAM,SAAS;AACtB,aAAO,MAAM,OAAO;AACpB,aAAO,MAAM,QAAQ;AAErB,aAAO,eAAe;AACtB,aAAO,eAAe;AAEtB,aAAO,UAAU;AAAA,IACnB;AAEA,aAAS,qBAA2B;AACpB;AAEd,aAAO,cAAc;AAAA,IACvB;AAEA,aAAS,eAAe,SAA4B;AAClD,cAAQ,MAAM,WAAW;AACzB,cAAQ,MAAM,SAAS;AACvB,cAAQ,MAAM,UAAU;AACxB,cAAQ,MAAM,SAAS;AACvB,cAAQ,MAAM,eAAe;AAC7B,cAAQ,MAAM,aAAa;AAC3B,cAAQ,MAAM,QAAQ;AACtB,cAAQ,MAAM,OAAO;AACrB,cAAQ,MAAM,YAAY;AAC1B,cAAQ,MAAM,UAAU;AACxB,cAAQ,MAAM,UAAU;AACxB,cAAQ,MAAM,SAAS;AAAA,IACzB;AAEA,QAAI,QAAQ,WAAW;AACrB,aAAO,KAAK;AACZ,aAAO,MAAM,UAAU;AAEvB,qBAAe,MAAM;AAGnB,gBACC,GAAI,mBAAmB,cAAc,EACrC,KAAK,SAAU,WAAoB;AACtB,oBAAA,gBAAgB;MAAmB,CAChD,EACA,MAAM,kBAAkB;AAEpB,aAAA;AAAA,IAAA,OACF;AACC,YAAA,UAAU,SAAS,cAAc,GAAG;AAEtC,UAAA,OAAO,oBAAoB,OAAO;AACpC,gBAAQ,OAAO,SAAS,SAAS,KAAK,QAAQ,UAAU,QAAQ;AAChE,gBAAQ,YAAY;AAAA,MAAA,OACf;AACL,gBAAQ,OAAO;AACf,gBAAQ,YAAY;AAAA,MACtB;AAEA,cAAQ,MAAM,OAAO;AACrB,cAAQ,MAAM,QAAQ;AACtB,cAAQ,MAAM,iBAAiB;AAE/B,qBAAe,OAAO;AAEf,aAAA;AAAA,IACT;AAAA,EACF;AACF;"}