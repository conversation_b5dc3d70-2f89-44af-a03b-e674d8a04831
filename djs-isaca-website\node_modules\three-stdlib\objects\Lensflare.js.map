{"version": 3, "file": "Lensflare.js", "sources": ["../../src/objects/Lensflare.js"], "sourcesContent": ["import {\n  AdditiveBlending,\n  Box2,\n  BufferGeometry,\n  Color,\n  Texture,\n  NearestFilter,\n  InterleavedBuffer,\n  InterleavedBufferAttribute,\n  Mesh,\n  MeshBasicMaterial,\n  RawShaderMaterial,\n  Vector2,\n  Vector3,\n  Vector4,\n} from 'three'\n\nconst Lensflare = /* @__PURE__ */ (() => {\n  class Lensflare extends Mesh {\n    constructor() {\n      super(Lensflare.Geometry, new MeshBasicMaterial({ opacity: 0, transparent: true }))\n\n      this.isLensflare = true\n\n      this.type = 'Lensflare'\n      this.frustumCulled = false\n      this.renderOrder = Infinity\n\n      //\n\n      const positionScreen = new Vector3()\n      const positionView = new Vector3()\n\n      // textures\n      const tempMap = new Texture({ width: 16, height: 16 })\n      tempMap.isFramebufferTexture = true\n      tempMap.magFilter = NearestFilter\n      tempMap.minFilter = NearestFilter\n      tempMap.generateMipmaps = false\n      tempMap.needsUpdate = true\n\n      const occlusionMap = new Texture({ width: 16, height: 16 })\n      occlusionMap.isFramebufferTexture = true\n      occlusionMap.magFilter = NearestFilter\n      occlusionMap.minFilter = NearestFilter\n      occlusionMap.generateMipmaps = false\n      occlusionMap.needsUpdate = true\n\n      // material\n\n      const geometry = Lensflare.Geometry\n\n      const material1a = new RawShaderMaterial({\n        uniforms: {\n          scale: { value: null },\n          screenPosition: { value: null },\n        },\n        vertexShader: /* glsl */ `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform vec3 screenPosition;\n\t\t\t\tuniform vec2 scale;\n\n\t\t\t\tattribute vec3 position;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_Position = vec4( position.xy * scale + screenPosition.xy, screenPosition.z, 1.0 );\n\n\t\t\t\t}`,\n\n        fragmentShader: /* glsl */ `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_FragColor = vec4( 1.0, 0.0, 1.0, 1.0 );\n\n\t\t\t\t}`,\n        depthTest: true,\n        depthWrite: false,\n        transparent: false,\n      })\n\n      const material1b = new RawShaderMaterial({\n        uniforms: {\n          map: { value: tempMap },\n          scale: { value: null },\n          screenPosition: { value: null },\n        },\n        vertexShader: /* glsl */ `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform vec3 screenPosition;\n\t\t\t\tuniform vec2 scale;\n\n\t\t\t\tattribute vec3 position;\n\t\t\t\tattribute vec2 uv;\n\n\t\t\t\tvarying vec2 vUV;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvUV = uv;\n\n\t\t\t\t\tgl_Position = vec4( position.xy * scale + screenPosition.xy, screenPosition.z, 1.0 );\n\n\t\t\t\t}`,\n\n        fragmentShader: /* glsl */ `\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform sampler2D map;\n\n\t\t\t\tvarying vec2 vUV;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_FragColor = texture2D( map, vUV );\n\n\t\t\t\t}`,\n        depthTest: false,\n        depthWrite: false,\n        transparent: false,\n      })\n\n      // the following object is used for occlusionMap generation\n\n      const mesh1 = new Mesh(geometry, material1a)\n\n      //\n\n      const elements = []\n\n      const shader = LensflareElement.Shader\n\n      const material2 = new RawShaderMaterial({\n        uniforms: {\n          map: { value: null },\n          occlusionMap: { value: occlusionMap },\n          color: { value: new Color(0xffffff) },\n          scale: { value: new Vector2() },\n          screenPosition: { value: new Vector3() },\n        },\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n        blending: AdditiveBlending,\n        transparent: true,\n        depthWrite: false,\n      })\n\n      const mesh2 = new Mesh(geometry, material2)\n\n      this.addElement = function (element) {\n        elements.push(element)\n      }\n\n      //\n\n      const scale = new Vector2()\n      const screenPositionPixels = new Vector2()\n      const validArea = new Box2()\n      const viewport = new Vector4()\n\n      this.onBeforeRender = function (renderer, scene, camera) {\n        renderer.getCurrentViewport(viewport)\n\n        const invAspect = viewport.w / viewport.z\n        const halfViewportWidth = viewport.z / 2.0\n        const halfViewportHeight = viewport.w / 2.0\n\n        let size = 16 / viewport.w\n        scale.set(size * invAspect, size)\n\n        validArea.min.set(viewport.x, viewport.y)\n        validArea.max.set(viewport.x + (viewport.z - 16), viewport.y + (viewport.w - 16))\n\n        // calculate position in screen space\n\n        positionView.setFromMatrixPosition(this.matrixWorld)\n        positionView.applyMatrix4(camera.matrixWorldInverse)\n\n        if (positionView.z > 0) return // lensflare is behind the camera\n\n        positionScreen.copy(positionView).applyMatrix4(camera.projectionMatrix)\n\n        // horizontal and vertical coordinate of the lower left corner of the pixels to copy\n\n        screenPositionPixels.x = viewport.x + positionScreen.x * halfViewportWidth + halfViewportWidth - 8\n        screenPositionPixels.y = viewport.y + positionScreen.y * halfViewportHeight + halfViewportHeight - 8\n\n        // screen cull\n\n        if (validArea.containsPoint(screenPositionPixels)) {\n          // save current RGB to temp texture\n\n          renderer.copyFramebufferToTexture(screenPositionPixels, tempMap)\n\n          // render pink quad\n\n          let uniforms = material1a.uniforms\n          uniforms['scale'].value = scale\n          uniforms['screenPosition'].value = positionScreen\n\n          renderer.renderBufferDirect(camera, null, geometry, material1a, mesh1, null)\n\n          // copy result to occlusionMap\n\n          renderer.copyFramebufferToTexture(screenPositionPixels, occlusionMap)\n\n          // restore graphics\n\n          uniforms = material1b.uniforms\n          uniforms['scale'].value = scale\n          uniforms['screenPosition'].value = positionScreen\n\n          renderer.renderBufferDirect(camera, null, geometry, material1b, mesh1, null)\n\n          // render elements\n\n          const vecX = -positionScreen.x * 2\n          const vecY = -positionScreen.y * 2\n\n          for (let i = 0, l = elements.length; i < l; i++) {\n            const element = elements[i]\n\n            const uniforms = material2.uniforms\n\n            uniforms['color'].value.copy(element.color)\n            uniforms['map'].value = element.texture\n            uniforms['screenPosition'].value.x = positionScreen.x + vecX * element.distance\n            uniforms['screenPosition'].value.y = positionScreen.y + vecY * element.distance\n\n            size = element.size / viewport.w\n            const invAspect = viewport.w / viewport.z\n\n            uniforms['scale'].value.set(size * invAspect, size)\n\n            material2.uniformsNeedUpdate = true\n\n            renderer.renderBufferDirect(camera, null, geometry, material2, mesh2, null)\n          }\n        }\n      }\n\n      this.dispose = function () {\n        material1a.dispose()\n        material1b.dispose()\n        material2.dispose()\n\n        tempMap.dispose()\n        occlusionMap.dispose()\n\n        for (let i = 0, l = elements.length; i < l; i++) {\n          elements[i].texture.dispose()\n        }\n      }\n    }\n  }\n\n  const _geometry = new BufferGeometry()\n  const interleavedBuffer = new InterleavedBuffer(\n    new Float32Array([-1, -1, 0, 0, 0, 1, -1, 0, 1, 0, 1, 1, 0, 1, 1, -1, 1, 0, 0, 1]),\n    5,\n  )\n\n  _geometry.setIndex([0, 1, 2, 0, 2, 3])\n  _geometry.setAttribute('position', new InterleavedBufferAttribute(interleavedBuffer, 3, 0, false))\n  _geometry.setAttribute('uv', new InterleavedBufferAttribute(interleavedBuffer, 2, 3, false))\n\n  Lensflare.Geometry = _geometry\n\n  return Lensflare\n})()\n\n//\n\nconst LensflareElement = /* @__PURE__ */ (() => {\n  class LensflareElement {\n    constructor(texture, size = 1, distance = 0, color = new Color(0xffffff)) {\n      this.texture = texture\n      this.size = size\n      this.distance = distance\n      this.color = color\n    }\n  }\n\n  LensflareElement.Shader = {\n    uniforms: {\n      map: { value: null },\n      occlusionMap: { value: null },\n      color: { value: null },\n      scale: { value: null },\n      screenPosition: { value: null },\n    },\n\n    vertexShader: /* glsl */ `\n\n      precision highp float;\n\n      uniform vec3 screenPosition;\n      uniform vec2 scale;\n\n      uniform sampler2D occlusionMap;\n\n      attribute vec3 position;\n      attribute vec2 uv;\n\n      varying vec2 vUV;\n      varying float vVisibility;\n\n      void main() {\n\n        vUV = uv;\n\n        vec2 pos = position.xy;\n\n        vec4 visibility = texture2D( occlusionMap, vec2( 0.1, 0.1 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.5, 0.1 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.9, 0.1 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.9, 0.5 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.9, 0.9 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.5, 0.9 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.1, 0.9 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.1, 0.5 ) );\n        visibility += texture2D( occlusionMap, vec2( 0.5, 0.5 ) );\n\n        vVisibility =        visibility.r / 9.0;\n        vVisibility *= 1.0 - visibility.g / 9.0;\n        vVisibility *=       visibility.b / 9.0;\n\n        gl_Position = vec4( ( pos * scale + screenPosition.xy ).xy, screenPosition.z, 1.0 );\n\n      }\n    `,\n\n    fragmentShader: /* glsl */ `\n\n      precision highp float;\n\n      uniform sampler2D map;\n      uniform vec3 color;\n\n      varying vec2 vUV;\n      varying float vVisibility;\n\n      void main() {\n\n        vec4 texture = texture2D( map, vUV );\n        texture.a *= vVisibility;\n        gl_FragColor = texture;\n        gl_FragColor.rgb *= color;\n\n      }\n    `,\n  }\n\n  return LensflareElement\n})()\n\nexport { Lensflare, LensflareElement }\n"], "names": ["Lensflare", "uniforms", "invAspect", "LensflareElement"], "mappings": ";AAiBK,MAAC,YAA6B,uBAAM;AACvC,QAAMA,mBAAkB,KAAK;AAAA,IAC3B,cAAc;AACZ,YAAMA,WAAU,UAAU,IAAI,kBAAkB,EAAE,SAAS,GAAG,aAAa,KAAI,CAAE,CAAC;AAElF,WAAK,cAAc;AAEnB,WAAK,OAAO;AACZ,WAAK,gBAAgB;AACrB,WAAK,cAAc;AAInB,YAAM,iBAAiB,IAAI,QAAS;AACpC,YAAM,eAAe,IAAI,QAAS;AAGlC,YAAM,UAAU,IAAI,QAAQ,EAAE,OAAO,IAAI,QAAQ,IAAI;AACrD,cAAQ,uBAAuB;AAC/B,cAAQ,YAAY;AACpB,cAAQ,YAAY;AACpB,cAAQ,kBAAkB;AAC1B,cAAQ,cAAc;AAEtB,YAAM,eAAe,IAAI,QAAQ,EAAE,OAAO,IAAI,QAAQ,IAAI;AAC1D,mBAAa,uBAAuB;AACpC,mBAAa,YAAY;AACzB,mBAAa,YAAY;AACzB,mBAAa,kBAAkB;AAC/B,mBAAa,cAAc;AAI3B,YAAM,WAAWA,WAAU;AAE3B,YAAM,aAAa,IAAI,kBAAkB;AAAA,QACvC,UAAU;AAAA,UACR,OAAO,EAAE,OAAO,KAAM;AAAA,UACtB,gBAAgB,EAAE,OAAO,KAAM;AAAA,QAChC;AAAA,QACD;AAAA;AAAA,UAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAezB;AAAA;AAAA,UAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAS3B,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,MACrB,CAAO;AAED,YAAM,aAAa,IAAI,kBAAkB;AAAA,QACvC,UAAU;AAAA,UACR,KAAK,EAAE,OAAO,QAAS;AAAA,UACvB,OAAO,EAAE,OAAO,KAAM;AAAA,UACtB,gBAAgB,EAAE,OAAO,KAAM;AAAA,QAChC;AAAA,QACD;AAAA;AAAA,UAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAoBzB;AAAA;AAAA,UAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAa3B,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,MACrB,CAAO;AAID,YAAM,QAAQ,IAAI,KAAK,UAAU,UAAU;AAI3C,YAAM,WAAW,CAAE;AAEnB,YAAM,SAAS,iBAAiB;AAEhC,YAAM,YAAY,IAAI,kBAAkB;AAAA,QACtC,UAAU;AAAA,UACR,KAAK,EAAE,OAAO,KAAM;AAAA,UACpB,cAAc,EAAE,OAAO,aAAc;AAAA,UACrC,OAAO,EAAE,OAAO,IAAI,MAAM,QAAQ,EAAG;AAAA,UACrC,OAAO,EAAE,OAAO,IAAI,UAAW;AAAA,UAC/B,gBAAgB,EAAE,OAAO,IAAI,UAAW;AAAA,QACzC;AAAA,QACD,cAAc,OAAO;AAAA,QACrB,gBAAgB,OAAO;AAAA,QACvB,UAAU;AAAA,QACV,aAAa;AAAA,QACb,YAAY;AAAA,MACpB,CAAO;AAED,YAAM,QAAQ,IAAI,KAAK,UAAU,SAAS;AAE1C,WAAK,aAAa,SAAU,SAAS;AACnC,iBAAS,KAAK,OAAO;AAAA,MACtB;AAID,YAAM,QAAQ,IAAI,QAAS;AAC3B,YAAM,uBAAuB,IAAI,QAAS;AAC1C,YAAM,YAAY,IAAI,KAAM;AAC5B,YAAM,WAAW,IAAI,QAAS;AAE9B,WAAK,iBAAiB,SAAU,UAAU,OAAO,QAAQ;AACvD,iBAAS,mBAAmB,QAAQ;AAEpC,cAAM,YAAY,SAAS,IAAI,SAAS;AACxC,cAAM,oBAAoB,SAAS,IAAI;AACvC,cAAM,qBAAqB,SAAS,IAAI;AAExC,YAAI,OAAO,KAAK,SAAS;AACzB,cAAM,IAAI,OAAO,WAAW,IAAI;AAEhC,kBAAU,IAAI,IAAI,SAAS,GAAG,SAAS,CAAC;AACxC,kBAAU,IAAI,IAAI,SAAS,KAAK,SAAS,IAAI,KAAK,SAAS,KAAK,SAAS,IAAI,GAAG;AAIhF,qBAAa,sBAAsB,KAAK,WAAW;AACnD,qBAAa,aAAa,OAAO,kBAAkB;AAEnD,YAAI,aAAa,IAAI;AAAG;AAExB,uBAAe,KAAK,YAAY,EAAE,aAAa,OAAO,gBAAgB;AAItE,6BAAqB,IAAI,SAAS,IAAI,eAAe,IAAI,oBAAoB,oBAAoB;AACjG,6BAAqB,IAAI,SAAS,IAAI,eAAe,IAAI,qBAAqB,qBAAqB;AAInG,YAAI,UAAU,cAAc,oBAAoB,GAAG;AAGjD,mBAAS,yBAAyB,sBAAsB,OAAO;AAI/D,cAAI,WAAW,WAAW;AAC1B,mBAAS,OAAO,EAAE,QAAQ;AAC1B,mBAAS,gBAAgB,EAAE,QAAQ;AAEnC,mBAAS,mBAAmB,QAAQ,MAAM,UAAU,YAAY,OAAO,IAAI;AAI3E,mBAAS,yBAAyB,sBAAsB,YAAY;AAIpE,qBAAW,WAAW;AACtB,mBAAS,OAAO,EAAE,QAAQ;AAC1B,mBAAS,gBAAgB,EAAE,QAAQ;AAEnC,mBAAS,mBAAmB,QAAQ,MAAM,UAAU,YAAY,OAAO,IAAI;AAI3E,gBAAM,OAAO,CAAC,eAAe,IAAI;AACjC,gBAAM,OAAO,CAAC,eAAe,IAAI;AAEjC,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,kBAAM,UAAU,SAAS,CAAC;AAE1B,kBAAMC,YAAW,UAAU;AAE3B,YAAAA,UAAS,OAAO,EAAE,MAAM,KAAK,QAAQ,KAAK;AAC1C,YAAAA,UAAS,KAAK,EAAE,QAAQ,QAAQ;AAChC,YAAAA,UAAS,gBAAgB,EAAE,MAAM,IAAI,eAAe,IAAI,OAAO,QAAQ;AACvE,YAAAA,UAAS,gBAAgB,EAAE,MAAM,IAAI,eAAe,IAAI,OAAO,QAAQ;AAEvE,mBAAO,QAAQ,OAAO,SAAS;AAC/B,kBAAMC,aAAY,SAAS,IAAI,SAAS;AAExC,YAAAD,UAAS,OAAO,EAAE,MAAM,IAAI,OAAOC,YAAW,IAAI;AAElD,sBAAU,qBAAqB;AAE/B,qBAAS,mBAAmB,QAAQ,MAAM,UAAU,WAAW,OAAO,IAAI;AAAA,UAC3E;AAAA,QACF;AAAA,MACF;AAED,WAAK,UAAU,WAAY;AACzB,mBAAW,QAAS;AACpB,mBAAW,QAAS;AACpB,kBAAU,QAAS;AAEnB,gBAAQ,QAAS;AACjB,qBAAa,QAAS;AAEtB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,mBAAS,CAAC,EAAE,QAAQ,QAAS;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAED,QAAM,YAAY,IAAI,eAAgB;AACtC,QAAM,oBAAoB,IAAI;AAAA,IAC5B,IAAI,aAAa,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,IACjF;AAAA,EACD;AAED,YAAU,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACrC,YAAU,aAAa,YAAY,IAAI,2BAA2B,mBAAmB,GAAG,GAAG,KAAK,CAAC;AACjG,YAAU,aAAa,MAAM,IAAI,2BAA2B,mBAAmB,GAAG,GAAG,KAAK,CAAC;AAE3F,EAAAF,WAAU,WAAW;AAErB,SAAOA;AACT,GAAI;AAIC,MAAC,mBAAoC,uBAAM;AAC9C,QAAMG,kBAAiB;AAAA,IACrB,YAAY,SAAS,OAAO,GAAG,WAAW,GAAG,QAAQ,IAAI,MAAM,QAAQ,GAAG;AACxE,WAAK,UAAU;AACf,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,QAAQ;AAAA,IACd;AAAA,EACF;AAED,EAAAA,kBAAiB,SAAS;AAAA,IACxB,UAAU;AAAA,MACR,KAAK,EAAE,OAAO,KAAM;AAAA,MACpB,cAAc,EAAE,OAAO,KAAM;AAAA,MAC7B,OAAO,EAAE,OAAO,KAAM;AAAA,MACtB,OAAO,EAAE,OAAO,KAAM;AAAA,MACtB,gBAAgB,EAAE,OAAO,KAAM;AAAA,IAChC;AAAA,IAED;AAAA;AAAA,MAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAwCzB;AAAA;AAAA,MAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmB5B;AAED,SAAOA;AACT,GAAC;"}