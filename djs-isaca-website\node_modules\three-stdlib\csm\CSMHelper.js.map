{"version": 3, "file": "CSMHelper.js", "sources": ["../../src/csm/CSMHelper.js"], "sourcesContent": ["import {\n  Group,\n  Mesh,\n  LineSegments,\n  BufferGeometry,\n  LineBasicMaterial,\n  Box3Helper,\n  Box3,\n  PlaneGeometry,\n  MeshBasicMaterial,\n  BufferAttribute,\n  DoubleSide,\n} from 'three'\n\nclass CSMHelper extends Group {\n  constructor(csm) {\n    super()\n    this.csm = csm\n    this.displayFrustum = true\n    this.displayPlanes = true\n    this.displayShadowBounds = true\n\n    const indices = new Uint16Array([0, 1, 1, 2, 2, 3, 3, 0, 4, 5, 5, 6, 6, 7, 7, 4, 0, 4, 1, 5, 2, 6, 3, 7])\n    const positions = new Float32Array(24)\n    const frustumGeometry = new BufferGeometry()\n    frustumGeometry.setIndex(new BufferAttribute(indices, 1))\n    frustumGeometry.setAttribute('position', new BufferAttribute(positions, 3, false))\n    const frustumLines = new LineSegments(frustumGeometry, new LineBasicMaterial())\n    this.add(frustumLines)\n\n    this.frustumLines = frustumLines\n    this.cascadeLines = []\n    this.cascadePlanes = []\n    this.shadowLines = []\n  }\n\n  updateVisibility() {\n    const displayFrustum = this.displayFrustum\n    const displayPlanes = this.displayPlanes\n    const displayShadowBounds = this.displayShadowBounds\n\n    const frustumLines = this.frustumLines\n    const cascadeLines = this.cascadeLines\n    const cascadePlanes = this.cascadePlanes\n    const shadowLines = this.shadowLines\n    for (let i = 0, l = cascadeLines.length; i < l; i++) {\n      const cascadeLine = cascadeLines[i]\n      const cascadePlane = cascadePlanes[i]\n      const shadowLineGroup = shadowLines[i]\n\n      cascadeLine.visible = displayFrustum\n      cascadePlane.visible = displayFrustum && displayPlanes\n      shadowLineGroup.visible = displayShadowBounds\n    }\n\n    frustumLines.visible = displayFrustum\n  }\n\n  update() {\n    const csm = this.csm\n    const camera = csm.camera\n    const cascades = csm.cascades\n    const mainFrustum = csm.mainFrustum\n    const frustums = csm.frustums\n    const lights = csm.lights\n\n    const frustumLines = this.frustumLines\n    const frustumLinePositions = frustumLines.geometry.getAttribute('position')\n    const cascadeLines = this.cascadeLines\n    const cascadePlanes = this.cascadePlanes\n    const shadowLines = this.shadowLines\n\n    this.position.copy(camera.position)\n    this.quaternion.copy(camera.quaternion)\n    this.scale.copy(camera.scale)\n    this.updateMatrixWorld(true)\n\n    while (cascadeLines.length > cascades) {\n      this.remove(cascadeLines.pop())\n      this.remove(cascadePlanes.pop())\n      this.remove(shadowLines.pop())\n    }\n\n    while (cascadeLines.length < cascades) {\n      const cascadeLine = new Box3Helper(new Box3(), 0xffffff)\n      const planeMat = new MeshBasicMaterial({ transparent: true, opacity: 0.1, depthWrite: false, side: DoubleSide })\n      const cascadePlane = new Mesh(new PlaneGeometry(), planeMat)\n      const shadowLineGroup = new Group()\n      const shadowLine = new Box3Helper(new Box3(), 0xffff00)\n      shadowLineGroup.add(shadowLine)\n\n      this.add(cascadeLine)\n      this.add(cascadePlane)\n      this.add(shadowLineGroup)\n\n      cascadeLines.push(cascadeLine)\n      cascadePlanes.push(cascadePlane)\n      shadowLines.push(shadowLineGroup)\n    }\n\n    for (let i = 0; i < cascades; i++) {\n      const frustum = frustums[i]\n      const light = lights[i]\n      const shadowCam = light.shadow.camera\n      const farVerts = frustum.vertices.far\n\n      const cascadeLine = cascadeLines[i]\n      const cascadePlane = cascadePlanes[i]\n      const shadowLineGroup = shadowLines[i]\n      const shadowLine = shadowLineGroup.children[0]\n\n      cascadeLine.box.min.copy(farVerts[2])\n      cascadeLine.box.max.copy(farVerts[0])\n      cascadeLine.box.max.z += 1e-4\n\n      cascadePlane.position.addVectors(farVerts[0], farVerts[2])\n      cascadePlane.position.multiplyScalar(0.5)\n      cascadePlane.scale.subVectors(farVerts[0], farVerts[2])\n      cascadePlane.scale.z = 1e-4\n\n      this.remove(shadowLineGroup)\n      shadowLineGroup.position.copy(shadowCam.position)\n      shadowLineGroup.quaternion.copy(shadowCam.quaternion)\n      shadowLineGroup.scale.copy(shadowCam.scale)\n      shadowLineGroup.updateMatrixWorld(true)\n      this.attach(shadowLineGroup)\n\n      shadowLine.box.min.set(shadowCam.bottom, shadowCam.left, -shadowCam.far)\n      shadowLine.box.max.set(shadowCam.top, shadowCam.right, -shadowCam.near)\n    }\n\n    const nearVerts = mainFrustum.vertices.near\n    const farVerts = mainFrustum.vertices.far\n    frustumLinePositions.setXYZ(0, farVerts[0].x, farVerts[0].y, farVerts[0].z)\n    frustumLinePositions.setXYZ(1, farVerts[3].x, farVerts[3].y, farVerts[3].z)\n    frustumLinePositions.setXYZ(2, farVerts[2].x, farVerts[2].y, farVerts[2].z)\n    frustumLinePositions.setXYZ(3, farVerts[1].x, farVerts[1].y, farVerts[1].z)\n\n    frustumLinePositions.setXYZ(4, nearVerts[0].x, nearVerts[0].y, nearVerts[0].z)\n    frustumLinePositions.setXYZ(5, nearVerts[3].x, nearVerts[3].y, nearVerts[3].z)\n    frustumLinePositions.setXYZ(6, nearVerts[2].x, nearVerts[2].y, nearVerts[2].z)\n    frustumLinePositions.setXYZ(7, nearVerts[1].x, nearVerts[1].y, nearVerts[1].z)\n    frustumLinePositions.needsUpdate = true\n  }\n}\n\nexport { CSMHelper }\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";AAcA,MAAM,kBAAkB,MAAM;AAAA,EAC5B,YAAY,KAAK;AACf,UAAO;AACP,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,sBAAsB;AAE3B,UAAM,UAAU,IAAI,YAAY,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACxG,UAAM,YAAY,IAAI,aAAa,EAAE;AACrC,UAAM,kBAAkB,IAAI,eAAgB;AAC5C,oBAAgB,SAAS,IAAI,gBAAgB,SAAS,CAAC,CAAC;AACxD,oBAAgB,aAAa,YAAY,IAAI,gBAAgB,WAAW,GAAG,KAAK,CAAC;AACjF,UAAM,eAAe,IAAI,aAAa,iBAAiB,IAAI,kBAAiB,CAAE;AAC9E,SAAK,IAAI,YAAY;AAErB,SAAK,eAAe;AACpB,SAAK,eAAe,CAAE;AACtB,SAAK,gBAAgB,CAAE;AACvB,SAAK,cAAc,CAAE;AAAA,EACtB;AAAA,EAED,mBAAmB;AACjB,UAAM,iBAAiB,KAAK;AAC5B,UAAM,gBAAgB,KAAK;AAC3B,UAAM,sBAAsB,KAAK;AAEjC,UAAM,eAAe,KAAK;AAC1B,UAAM,eAAe,KAAK;AAC1B,UAAM,gBAAgB,KAAK;AAC3B,UAAM,cAAc,KAAK;AACzB,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,IAAI,GAAG,KAAK;AACnD,YAAM,cAAc,aAAa,CAAC;AAClC,YAAM,eAAe,cAAc,CAAC;AACpC,YAAM,kBAAkB,YAAY,CAAC;AAErC,kBAAY,UAAU;AACtB,mBAAa,UAAU,kBAAkB;AACzC,sBAAgB,UAAU;AAAA,IAC3B;AAED,iBAAa,UAAU;AAAA,EACxB;AAAA,EAED,SAAS;AACP,UAAM,MAAM,KAAK;AACjB,UAAM,SAAS,IAAI;AACnB,UAAM,WAAW,IAAI;AACrB,UAAM,cAAc,IAAI;AACxB,UAAM,WAAW,IAAI;AACrB,UAAM,SAAS,IAAI;AAEnB,UAAM,eAAe,KAAK;AAC1B,UAAM,uBAAuB,aAAa,SAAS,aAAa,UAAU;AAC1E,UAAM,eAAe,KAAK;AAC1B,UAAM,gBAAgB,KAAK;AAC3B,UAAM,cAAc,KAAK;AAEzB,SAAK,SAAS,KAAK,OAAO,QAAQ;AAClC,SAAK,WAAW,KAAK,OAAO,UAAU;AACtC,SAAK,MAAM,KAAK,OAAO,KAAK;AAC5B,SAAK,kBAAkB,IAAI;AAE3B,WAAO,aAAa,SAAS,UAAU;AACrC,WAAK,OAAO,aAAa,KAAK;AAC9B,WAAK,OAAO,cAAc,KAAK;AAC/B,WAAK,OAAO,YAAY,KAAK;AAAA,IAC9B;AAED,WAAO,aAAa,SAAS,UAAU;AACrC,YAAM,cAAc,IAAI,WAAW,IAAI,KAAI,GAAI,QAAQ;AACvD,YAAM,WAAW,IAAI,kBAAkB,EAAE,aAAa,MAAM,SAAS,KAAK,YAAY,OAAO,MAAM,WAAU,CAAE;AAC/G,YAAM,eAAe,IAAI,KAAK,IAAI,cAAa,GAAI,QAAQ;AAC3D,YAAM,kBAAkB,IAAI,MAAO;AACnC,YAAM,aAAa,IAAI,WAAW,IAAI,KAAI,GAAI,QAAQ;AACtD,sBAAgB,IAAI,UAAU;AAE9B,WAAK,IAAI,WAAW;AACpB,WAAK,IAAI,YAAY;AACrB,WAAK,IAAI,eAAe;AAExB,mBAAa,KAAK,WAAW;AAC7B,oBAAc,KAAK,YAAY;AAC/B,kBAAY,KAAK,eAAe;AAAA,IACjC;AAED,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,YAAM,UAAU,SAAS,CAAC;AAC1B,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,YAAY,MAAM,OAAO;AAC/B,YAAMA,YAAW,QAAQ,SAAS;AAElC,YAAM,cAAc,aAAa,CAAC;AAClC,YAAM,eAAe,cAAc,CAAC;AACpC,YAAM,kBAAkB,YAAY,CAAC;AACrC,YAAM,aAAa,gBAAgB,SAAS,CAAC;AAE7C,kBAAY,IAAI,IAAI,KAAKA,UAAS,CAAC,CAAC;AACpC,kBAAY,IAAI,IAAI,KAAKA,UAAS,CAAC,CAAC;AACpC,kBAAY,IAAI,IAAI,KAAK;AAEzB,mBAAa,SAAS,WAAWA,UAAS,CAAC,GAAGA,UAAS,CAAC,CAAC;AACzD,mBAAa,SAAS,eAAe,GAAG;AACxC,mBAAa,MAAM,WAAWA,UAAS,CAAC,GAAGA,UAAS,CAAC,CAAC;AACtD,mBAAa,MAAM,IAAI;AAEvB,WAAK,OAAO,eAAe;AAC3B,sBAAgB,SAAS,KAAK,UAAU,QAAQ;AAChD,sBAAgB,WAAW,KAAK,UAAU,UAAU;AACpD,sBAAgB,MAAM,KAAK,UAAU,KAAK;AAC1C,sBAAgB,kBAAkB,IAAI;AACtC,WAAK,OAAO,eAAe;AAE3B,iBAAW,IAAI,IAAI,IAAI,UAAU,QAAQ,UAAU,MAAM,CAAC,UAAU,GAAG;AACvE,iBAAW,IAAI,IAAI,IAAI,UAAU,KAAK,UAAU,OAAO,CAAC,UAAU,IAAI;AAAA,IACvE;AAED,UAAM,YAAY,YAAY,SAAS;AACvC,UAAM,WAAW,YAAY,SAAS;AACtC,yBAAqB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;AAC1E,yBAAqB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;AAC1E,yBAAqB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;AAC1E,yBAAqB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;AAE1E,yBAAqB,OAAO,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;AAC7E,yBAAqB,OAAO,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;AAC7E,yBAAqB,OAAO,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;AAC7E,yBAAqB,OAAO,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;AAC7E,yBAAqB,cAAc;AAAA,EACpC;AACH;"}