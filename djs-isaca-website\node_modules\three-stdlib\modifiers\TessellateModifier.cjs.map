{"version": 3, "file": "TessellateModifier.cjs", "sources": ["../../src/modifiers/TessellateModifier.ts"], "sourcesContent": ["import { BufferGeometry, Color, Float32BufferAttribute, Vector2, Vector3 } from 'three'\nimport { UV1 } from '../_polyfill/uv1'\n\n/**\n * Break faces with edges longer than maxEdgeLength\n */\n\nclass TessellateModifier {\n  public maxEdgeLength: number\n  public maxIterations: number\n\n  constructor(maxEdgeLength = 0.1, maxIterations = 6) {\n    this.maxEdgeLength = maxEdgeLength\n    this.maxIterations = maxIterations\n  }\n\n  public modify = (geometry: BufferGeometry): BufferGeometry => {\n    if (geometry.index !== null) {\n      geometry = geometry.toNonIndexed()\n    }\n\n    //\n\n    const maxIterations = this.maxIterations\n    const maxEdgeLengthSquared = this.maxEdgeLength * this.maxEdgeLength\n\n    const va = new Vector3()\n    const vb = new Vector3()\n    const vc = new Vector3()\n    const vm = new Vector3()\n    const vs = [va, vb, vc, vm]\n\n    const na = new Vector3()\n    const nb = new Vector3()\n    const nc = new Vector3()\n    const nm = new Vector3()\n    const ns = [na, nb, nc, nm]\n\n    const ca = new Color()\n    const cb = new Color()\n    const cc = new Color()\n    const cm = new Color()\n    const cs = [ca, cb, cc, cm]\n\n    const ua = new Vector2()\n    const ub = new Vector2()\n    const uc = new Vector2()\n    const um = new Vector2()\n    const us = [ua, ub, uc, um]\n\n    const u2a = new Vector2()\n    const u2b = new Vector2()\n    const u2c = new Vector2()\n    const u2m = new Vector2()\n    const u2s = [u2a, u2b, u2c, u2m]\n\n    const attributes = geometry.attributes\n    const hasNormals = attributes.normal !== undefined\n    const hasColors = attributes.color !== undefined\n    const hasUVs = attributes.uv !== undefined\n    const hasUV1s = attributes[UV1] !== undefined\n\n    let positions = attributes.position.array\n    let normals = hasNormals ? attributes.normal.array : null\n    let colors = hasColors ? attributes.color.array : null\n    let uvs = hasUVs ? attributes.uv.array : null\n    let uv1s = hasUV1s ? attributes.uv1.array : null\n\n    let positions2 = (positions as unknown) as number[]\n    let normals2 = (normals as unknown) as number[]\n    let colors2 = (colors as unknown) as number[]\n    let uvs2 = (uvs as unknown) as number[]\n    let uv1s2 = (uv1s as unknown) as number[]\n\n    let iteration = 0\n    let tessellating = true\n\n    function addTriangle(a: number, b: number, c: number): void {\n      const v1 = vs[a]\n      const v2 = vs[b]\n      const v3 = vs[c]\n\n      positions2.push(v1.x, v1.y, v1.z)\n      positions2.push(v2.x, v2.y, v2.z)\n      positions2.push(v3.x, v3.y, v3.z)\n\n      if (hasNormals) {\n        const n1 = ns[a]\n        const n2 = ns[b]\n        const n3 = ns[c]\n\n        normals2.push(n1.x, n1.y, n1.z)\n        normals2.push(n2.x, n2.y, n2.z)\n        normals2.push(n3.x, n3.y, n3.z)\n      }\n\n      if (hasColors) {\n        const c1 = cs[a]\n        const c2 = cs[b]\n        const c3 = cs[c]\n\n        colors2.push(c1.r, c1.g, c1.b)\n        colors2.push(c2.r, c2.g, c2.b)\n        colors2.push(c3.r, c3.g, c3.b)\n      }\n\n      if (hasUVs) {\n        const u1 = us[a]\n        const u2 = us[b]\n        const u3 = us[c]\n\n        uvs2.push(u1.x, u1.y)\n        uvs2.push(u2.x, u2.y)\n        uvs2.push(u3.x, u3.y)\n      }\n\n      if (hasUV1s) {\n        const u21 = u2s[a]\n        const u22 = u2s[b]\n        const u23 = u2s[c]\n\n        uv1s2.push(u21.x, u21.y)\n        uv1s2.push(u22.x, u22.y)\n        uv1s2.push(u23.x, u23.y)\n      }\n    }\n\n    while (tessellating && iteration < maxIterations) {\n      iteration++\n      tessellating = false\n\n      positions = positions2 as any\n      positions2 = []\n\n      if (hasNormals) {\n        normals = normals2 as any\n        normals2 = []\n      }\n\n      if (hasColors) {\n        colors = colors2 as any\n        colors2 = []\n      }\n\n      if (hasUVs) {\n        uvs = uvs2 as any\n        uvs2 = []\n      }\n\n      if (hasUV1s) {\n        uv1s = uv1s2 as any\n        uv1s2 = []\n      }\n\n      for (let i = 0, i2 = 0, il = positions.length; i < il; i += 9, i2 += 6) {\n        va.fromArray(positions, i + 0)\n        vb.fromArray(positions, i + 3)\n        vc.fromArray(positions, i + 6)\n\n        if (hasNormals && normals) {\n          na.fromArray(normals, i + 0)\n          nb.fromArray(normals, i + 3)\n          nc.fromArray(normals, i + 6)\n        }\n\n        if (hasColors && colors) {\n          ca.fromArray(colors, i + 0)\n          cb.fromArray(colors, i + 3)\n          cc.fromArray(colors, i + 6)\n        }\n\n        if (hasUVs && uvs) {\n          ua.fromArray(uvs, i2 + 0)\n          ub.fromArray(uvs, i2 + 2)\n          uc.fromArray(uvs, i2 + 4)\n        }\n\n        if (hasUV1s && uv1s) {\n          u2a.fromArray(uv1s, i2 + 0)\n          u2b.fromArray(uv1s, i2 + 2)\n          u2c.fromArray(uv1s, i2 + 4)\n        }\n\n        const dab = va.distanceToSquared(vb)\n        const dbc = vb.distanceToSquared(vc)\n        const dac = va.distanceToSquared(vc)\n\n        if (dab > maxEdgeLengthSquared || dbc > maxEdgeLengthSquared || dac > maxEdgeLengthSquared) {\n          tessellating = true\n\n          if (dab >= dbc && dab >= dac) {\n            vm.lerpVectors(va, vb, 0.5)\n            if (hasNormals) nm.lerpVectors(na, nb, 0.5)\n            if (hasColors) cm.lerpColors(ca, cb, 0.5)\n            if (hasUVs) um.lerpVectors(ua, ub, 0.5)\n            if (hasUV1s) u2m.lerpVectors(u2a, u2b, 0.5)\n\n            addTriangle(0, 3, 2)\n            addTriangle(3, 1, 2)\n          } else if (dbc >= dab && dbc >= dac) {\n            vm.lerpVectors(vb, vc, 0.5)\n            if (hasNormals) nm.lerpVectors(nb, nc, 0.5)\n            if (hasColors) cm.lerpColors(cb, cc, 0.5)\n            if (hasUVs) um.lerpVectors(ub, uc, 0.5)\n            if (hasUV1s) u2m.lerpVectors(u2b, u2c, 0.5)\n\n            addTriangle(0, 1, 3)\n            addTriangle(3, 2, 0)\n          } else {\n            vm.lerpVectors(va, vc, 0.5)\n            if (hasNormals) nm.lerpVectors(na, nc, 0.5)\n            if (hasColors) cm.lerpColors(ca, cc, 0.5)\n            if (hasUVs) um.lerpVectors(ua, uc, 0.5)\n            if (hasUV1s) u2m.lerpVectors(u2a, u2c, 0.5)\n\n            addTriangle(0, 1, 3)\n            addTriangle(3, 1, 2)\n          }\n        } else {\n          addTriangle(0, 1, 2)\n        }\n      }\n    }\n\n    const geometry2 = new BufferGeometry()\n\n    geometry2.setAttribute('position', new Float32BufferAttribute(positions2, 3))\n\n    if (hasNormals) {\n      geometry2.setAttribute('normal', new Float32BufferAttribute(normals2 as any, 3))\n    }\n\n    if (hasColors) {\n      geometry2.setAttribute('color', new Float32BufferAttribute(colors2 as any, 3))\n    }\n\n    if (hasUVs) {\n      geometry2.setAttribute('uv', new Float32BufferAttribute(uvs2 as any, 2))\n    }\n\n    if (hasUV1s) {\n      geometry2.setAttribute(UV1, new Float32BufferAttribute(uv1s2 as any, 2))\n    }\n\n    return geometry2\n  }\n}\n\nexport { TessellateModifier }\n"], "names": ["Vector3", "Color", "Vector2", "UV1", "BufferGeometry", "Float32BufferAttribute"], "mappings": ";;;;;;;;;;AAOA,MAAM,mBAAmB;AAAA,EAIvB,YAAY,gBAAgB,KAAK,gBAAgB,GAAG;AAH7C;AACA;AAOA,kCAAS,CAAC,aAA6C;AACxD,UAAA,SAAS,UAAU,MAAM;AAC3B,mBAAW,SAAS;MACtB;AAIA,YAAM,gBAAgB,KAAK;AACrB,YAAA,uBAAuB,KAAK,gBAAgB,KAAK;AAEjD,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACf,YAAM,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;AAEpB,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACf,YAAM,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;AAEpB,YAAA,KAAK,IAAIC,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACf,YAAM,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;AAEpB,YAAA,KAAK,IAAIC,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACT,YAAA,KAAK,IAAIA,MAAAA;AACf,YAAM,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE;AAEpB,YAAA,MAAM,IAAIA,MAAAA;AACV,YAAA,MAAM,IAAIA,MAAAA;AACV,YAAA,MAAM,IAAIA,MAAAA;AACV,YAAA,MAAM,IAAIA,MAAAA;AAChB,YAAM,MAAM,CAAC,KAAK,KAAK,KAAK,GAAG;AAE/B,YAAM,aAAa,SAAS;AACtB,YAAA,aAAa,WAAW,WAAW;AACnC,YAAA,YAAY,WAAW,UAAU;AACjC,YAAA,SAAS,WAAW,OAAO;AAC3B,YAAA,UAAU,WAAWC,OAAG,MAAM;AAEhC,UAAA,YAAY,WAAW,SAAS;AACpC,UAAI,UAAU,aAAa,WAAW,OAAO,QAAQ;AACrD,UAAI,SAAS,YAAY,WAAW,MAAM,QAAQ;AAClD,UAAI,MAAM,SAAS,WAAW,GAAG,QAAQ;AACzC,UAAI,OAAO,UAAU,WAAW,IAAI,QAAQ;AAE5C,UAAI,aAAc;AAClB,UAAI,WAAY;AAChB,UAAI,UAAW;AACf,UAAI,OAAQ;AACZ,UAAI,QAAS;AAEb,UAAI,YAAY;AAChB,UAAI,eAAe;AAEV,eAAA,YAAY,GAAW,GAAW,GAAiB;AACpD,cAAA,KAAK,GAAG,CAAC;AACT,cAAA,KAAK,GAAG,CAAC;AACT,cAAA,KAAK,GAAG,CAAC;AAEf,mBAAW,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAChC,mBAAW,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAChC,mBAAW,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAEhC,YAAI,YAAY;AACR,gBAAA,KAAK,GAAG,CAAC;AACT,gBAAA,KAAK,GAAG,CAAC;AACT,gBAAA,KAAK,GAAG,CAAC;AAEf,mBAAS,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC9B,mBAAS,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC9B,mBAAS,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QAChC;AAEA,YAAI,WAAW;AACP,gBAAA,KAAK,GAAG,CAAC;AACT,gBAAA,KAAK,GAAG,CAAC;AACT,gBAAA,KAAK,GAAG,CAAC;AAEf,kBAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7B,kBAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7B,kBAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QAC/B;AAEA,YAAI,QAAQ;AACJ,gBAAA,KAAK,GAAG,CAAC;AACT,gBAAA,KAAK,GAAG,CAAC;AACT,gBAAA,KAAK,GAAG,CAAC;AAEf,eAAK,KAAK,GAAG,GAAG,GAAG,CAAC;AACpB,eAAK,KAAK,GAAG,GAAG,GAAG,CAAC;AACpB,eAAK,KAAK,GAAG,GAAG,GAAG,CAAC;AAAA,QACtB;AAEA,YAAI,SAAS;AACL,gBAAA,MAAM,IAAI,CAAC;AACX,gBAAA,MAAM,IAAI,CAAC;AACX,gBAAA,MAAM,IAAI,CAAC;AAEjB,gBAAM,KAAK,IAAI,GAAG,IAAI,CAAC;AACvB,gBAAM,KAAK,IAAI,GAAG,IAAI,CAAC;AACvB,gBAAM,KAAK,IAAI,GAAG,IAAI,CAAC;AAAA,QACzB;AAAA,MACF;AAEO,aAAA,gBAAgB,YAAY,eAAe;AAChD;AACe,uBAAA;AAEH,oBAAA;AACZ,qBAAa,CAAA;AAEb,YAAI,YAAY;AACJ,oBAAA;AACV,qBAAW,CAAA;AAAA,QACb;AAEA,YAAI,WAAW;AACJ,mBAAA;AACT,oBAAU,CAAA;AAAA,QACZ;AAEA,YAAI,QAAQ;AACJ,gBAAA;AACN,iBAAO,CAAA;AAAA,QACT;AAEA,YAAI,SAAS;AACJ,iBAAA;AACP,kBAAQ,CAAA;AAAA,QACV;AAEA,iBAAS,IAAI,GAAG,KAAK,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,KAAK,GAAG,MAAM,GAAG;AACnE,aAAA,UAAU,WAAW,IAAI,CAAC;AAC1B,aAAA,UAAU,WAAW,IAAI,CAAC;AAC1B,aAAA,UAAU,WAAW,IAAI,CAAC;AAE7B,cAAI,cAAc,SAAS;AACtB,eAAA,UAAU,SAAS,IAAI,CAAC;AACxB,eAAA,UAAU,SAAS,IAAI,CAAC;AACxB,eAAA,UAAU,SAAS,IAAI,CAAC;AAAA,UAC7B;AAEA,cAAI,aAAa,QAAQ;AACpB,eAAA,UAAU,QAAQ,IAAI,CAAC;AACvB,eAAA,UAAU,QAAQ,IAAI,CAAC;AACvB,eAAA,UAAU,QAAQ,IAAI,CAAC;AAAA,UAC5B;AAEA,cAAI,UAAU,KAAK;AACd,eAAA,UAAU,KAAK,KAAK,CAAC;AACrB,eAAA,UAAU,KAAK,KAAK,CAAC;AACrB,eAAA,UAAU,KAAK,KAAK,CAAC;AAAA,UAC1B;AAEA,cAAI,WAAW,MAAM;AACf,gBAAA,UAAU,MAAM,KAAK,CAAC;AACtB,gBAAA,UAAU,MAAM,KAAK,CAAC;AACtB,gBAAA,UAAU,MAAM,KAAK,CAAC;AAAA,UAC5B;AAEM,gBAAA,MAAM,GAAG,kBAAkB,EAAE;AAC7B,gBAAA,MAAM,GAAG,kBAAkB,EAAE;AAC7B,gBAAA,MAAM,GAAG,kBAAkB,EAAE;AAEnC,cAAI,MAAM,wBAAwB,MAAM,wBAAwB,MAAM,sBAAsB;AAC3E,2BAAA;AAEX,gBAAA,OAAO,OAAO,OAAO,KAAK;AACzB,iBAAA,YAAY,IAAI,IAAI,GAAG;AACtB,kBAAA;AAAe,mBAAA,YAAY,IAAI,IAAI,GAAG;AACtC,kBAAA;AAAc,mBAAA,WAAW,IAAI,IAAI,GAAG;AACpC,kBAAA;AAAW,mBAAA,YAAY,IAAI,IAAI,GAAG;AAClC,kBAAA;AAAa,oBAAA,YAAY,KAAK,KAAK,GAAG;AAE9B,0BAAA,GAAG,GAAG,CAAC;AACP,0BAAA,GAAG,GAAG,CAAC;AAAA,YACV,WAAA,OAAO,OAAO,OAAO,KAAK;AAChC,iBAAA,YAAY,IAAI,IAAI,GAAG;AACtB,kBAAA;AAAe,mBAAA,YAAY,IAAI,IAAI,GAAG;AACtC,kBAAA;AAAc,mBAAA,WAAW,IAAI,IAAI,GAAG;AACpC,kBAAA;AAAW,mBAAA,YAAY,IAAI,IAAI,GAAG;AAClC,kBAAA;AAAa,oBAAA,YAAY,KAAK,KAAK,GAAG;AAE9B,0BAAA,GAAG,GAAG,CAAC;AACP,0BAAA,GAAG,GAAG,CAAC;AAAA,YAAA,OACd;AACF,iBAAA,YAAY,IAAI,IAAI,GAAG;AACtB,kBAAA;AAAe,mBAAA,YAAY,IAAI,IAAI,GAAG;AACtC,kBAAA;AAAc,mBAAA,WAAW,IAAI,IAAI,GAAG;AACpC,kBAAA;AAAW,mBAAA,YAAY,IAAI,IAAI,GAAG;AAClC,kBAAA;AAAa,oBAAA,YAAY,KAAK,KAAK,GAAG;AAE9B,0BAAA,GAAG,GAAG,CAAC;AACP,0BAAA,GAAG,GAAG,CAAC;AAAA,YACrB;AAAA,UAAA,OACK;AACO,wBAAA,GAAG,GAAG,CAAC;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAEM,YAAA,YAAY,IAAIC,MAAAA;AAEtB,gBAAU,aAAa,YAAY,IAAIC,MAAuB,uBAAA,YAAY,CAAC,CAAC;AAE5E,UAAI,YAAY;AACd,kBAAU,aAAa,UAAU,IAAIA,MAAuB,uBAAA,UAAiB,CAAC,CAAC;AAAA,MACjF;AAEA,UAAI,WAAW;AACb,kBAAU,aAAa,SAAS,IAAIA,MAAuB,uBAAA,SAAgB,CAAC,CAAC;AAAA,MAC/E;AAEA,UAAI,QAAQ;AACV,kBAAU,aAAa,MAAM,IAAIA,MAAuB,uBAAA,MAAa,CAAC,CAAC;AAAA,MACzE;AAEA,UAAI,SAAS;AACX,kBAAU,aAAaF,SAAK,IAAIE,MAAuB,uBAAA,OAAc,CAAC,CAAC;AAAA,MACzE;AAEO,aAAA;AAAA,IAAA;AAxOP,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AAAA,EACvB;AAwOF;;"}