{"version": 3, "file": "CCDIKSolver.js", "sources": ["../../src/animation/CCDIKSolver.js"], "sourcesContent": ["import {\n  Buffer<PERSON>ttribute,\n  BufferGeometry,\n  Color,\n  Line,\n  LineBasicMaterial,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  Object3D,\n  Quaternion,\n  SphereGeometry,\n  Vector3,\n} from 'three'\n\nconst _q = /* @__PURE__ */ new Quaternion()\nconst _targetPos = /* @__PURE__ */ new Vector3()\nconst _targetVec = /* @__PURE__ */ new Vector3()\nconst _effectorPos = /* @__PURE__ */ new Vector3()\nconst _effectorVec = /* @__PURE__ */ new Vector3()\nconst _linkPos = /* @__PURE__ */ new Vector3()\nconst _invLinkQ = /* @__PURE__ */ new Quaternion()\nconst _linkScale = /* @__PURE__ */ new Vector3()\nconst _axis = /* @__PURE__ */ new Vector3()\nconst _vector = /* @__PURE__ */ new Vector3()\nconst _matrix = /* @__PURE__ */ new Matrix4()\n\n/**\n * CCD Algorithm\n *  - https://sites.google.com/site/auraliusproject/ccd-algorithm\n *\n * // ik parameter example\n * //\n * // target, effector, index in links are bone index in skeleton.bones.\n * // the bones relation should be\n * // <-- parent                                  child -->\n * // links[ n ], links[ n - 1 ], ..., links[ 0 ], effector\n * iks = [ {\n *\ttarget: 1,\n *\teffector: 2,\n *\tlinks: [ { index: 5, limitation: new Vector3( 1, 0, 0 ) }, { index: 4, enabled: false }, { index : 3 } ],\n *\titeration: 10,\n *\tminAngle: 0.0,\n *\tmaxAngle: 1.0,\n * } ];\n */\n\nclass CCDIKSolver {\n  /**\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Array<Object>} iks\n   */\n  constructor(mesh, iks = []) {\n    this.mesh = mesh\n    this.iks = iks\n\n    this._valid()\n  }\n\n  /**\n   * Update all IK bones.\n   *\n   * @return {CCDIKSolver}\n   */\n  update() {\n    const iks = this.iks\n\n    for (let i = 0, il = iks.length; i < il; i++) {\n      this.updateOne(iks[i])\n    }\n\n    return this\n  }\n\n  /**\n   * Update one IK bone\n   *\n   * @param {Object} ik parameter\n   * @return {CCDIKSolver}\n   */\n  updateOne(ik) {\n    const bones = this.mesh.skeleton.bones\n\n    // for reference overhead reduction in loop\n    const math = Math\n\n    const effector = bones[ik.effector]\n    const target = bones[ik.target]\n\n    // don't use getWorldPosition() here for the performance\n    // because it calls updateMatrixWorld( true ) inside.\n    _targetPos.setFromMatrixPosition(target.matrixWorld)\n\n    const links = ik.links\n    const iteration = ik.iteration !== undefined ? ik.iteration : 1\n\n    for (let i = 0; i < iteration; i++) {\n      let rotated = false\n\n      for (let j = 0, jl = links.length; j < jl; j++) {\n        const link = bones[links[j].index]\n\n        // skip this link and following links.\n        // this skip is used for MMD performance optimization.\n        if (links[j].enabled === false) break\n\n        const limitation = links[j].limitation\n        const rotationMin = links[j].rotationMin\n        const rotationMax = links[j].rotationMax\n\n        // don't use getWorldPosition/Quaternion() here for the performance\n        // because they call updateMatrixWorld( true ) inside.\n        link.matrixWorld.decompose(_linkPos, _invLinkQ, _linkScale)\n        _invLinkQ.invert()\n        _effectorPos.setFromMatrixPosition(effector.matrixWorld)\n\n        // work in link world\n        _effectorVec.subVectors(_effectorPos, _linkPos)\n        _effectorVec.applyQuaternion(_invLinkQ)\n        _effectorVec.normalize()\n\n        _targetVec.subVectors(_targetPos, _linkPos)\n        _targetVec.applyQuaternion(_invLinkQ)\n        _targetVec.normalize()\n\n        let angle = _targetVec.dot(_effectorVec)\n\n        if (angle > 1.0) {\n          angle = 1.0\n        } else if (angle < -1.0) {\n          angle = -1.0\n        }\n\n        angle = math.acos(angle)\n\n        // skip if changing angle is too small to prevent vibration of bone\n        if (angle < 1e-5) continue\n\n        if (ik.minAngle !== undefined && angle < ik.minAngle) {\n          angle = ik.minAngle\n        }\n\n        if (ik.maxAngle !== undefined && angle > ik.maxAngle) {\n          angle = ik.maxAngle\n        }\n\n        _axis.crossVectors(_effectorVec, _targetVec)\n        _axis.normalize()\n\n        _q.setFromAxisAngle(_axis, angle)\n        link.quaternion.multiply(_q)\n\n        // TODO: re-consider the limitation specification\n        if (limitation !== undefined) {\n          let c = link.quaternion.w\n\n          if (c > 1.0) c = 1.0\n\n          const c2 = math.sqrt(1 - c * c)\n          link.quaternion.set(limitation.x * c2, limitation.y * c2, limitation.z * c2, c)\n        }\n\n        if (rotationMin !== undefined) {\n          link.rotation.setFromVector3(_vector.setFromEuler(link.rotation).max(rotationMin))\n        }\n\n        if (rotationMax !== undefined) {\n          link.rotation.setFromVector3(_vector.setFromEuler(link.rotation).min(rotationMax))\n        }\n\n        link.updateMatrixWorld(true)\n\n        rotated = true\n      }\n\n      if (!rotated) break\n    }\n\n    return this\n  }\n\n  /**\n   * Creates Helper\n   *\n   * @return {CCDIKHelper}\n   */\n  createHelper() {\n    return new CCDIKHelper(this.mesh, this.iks)\n  }\n\n  // private methods\n\n  _valid() {\n    const iks = this.iks\n    const bones = this.mesh.skeleton.bones\n\n    for (let i = 0, il = iks.length; i < il; i++) {\n      const ik = iks[i]\n      const effector = bones[ik.effector]\n      const links = ik.links\n      let link0, link1\n\n      link0 = effector\n\n      for (let j = 0, jl = links.length; j < jl; j++) {\n        link1 = bones[links[j].index]\n\n        if (link0.parent !== link1) {\n          console.warn('THREE.CCDIKSolver: bone ' + link0.name + ' is not the child of bone ' + link1.name)\n        }\n\n        link0 = link1\n      }\n    }\n  }\n}\n\nfunction getPosition(bone, matrixWorldInv) {\n  return _vector.setFromMatrixPosition(bone.matrixWorld).applyMatrix4(matrixWorldInv)\n}\n\nfunction setPositionOfBoneToAttributeArray(array, index, bone, matrixWorldInv) {\n  const v = getPosition(bone, matrixWorldInv)\n\n  array[index * 3 + 0] = v.x\n  array[index * 3 + 1] = v.y\n  array[index * 3 + 2] = v.z\n}\n\n/**\n * Visualize IK bones\n *\n * @param {SkinnedMesh} mesh\n * @param {Array<Object>} iks\n */\nclass CCDIKHelper extends Object3D {\n  constructor(mesh, iks = [], sphereSize = 0.25) {\n    super()\n\n    this.root = mesh\n    this.iks = iks\n\n    this.matrix.copy(mesh.matrixWorld)\n    this.matrixAutoUpdate = false\n\n    this.sphereGeometry = new SphereGeometry(sphereSize, 16, 8)\n\n    this.targetSphereMaterial = new MeshBasicMaterial({\n      color: new Color(0xff8888),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this.effectorSphereMaterial = new MeshBasicMaterial({\n      color: new Color(0x88ff88),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this.linkSphereMaterial = new MeshBasicMaterial({\n      color: new Color(0x8888ff),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this.lineMaterial = new LineBasicMaterial({\n      color: new Color(0xff0000),\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this._init()\n  }\n\n  /**\n   * Updates IK bones visualization.\n   */\n  updateMatrixWorld(force) {\n    const mesh = this.root\n\n    if (this.visible) {\n      let offset = 0\n\n      const iks = this.iks\n      const bones = mesh.skeleton.bones\n\n      _matrix.copy(mesh.matrixWorld).invert()\n\n      for (let i = 0, il = iks.length; i < il; i++) {\n        const ik = iks[i]\n\n        const targetBone = bones[ik.target]\n        const effectorBone = bones[ik.effector]\n\n        const targetMesh = this.children[offset++]\n        const effectorMesh = this.children[offset++]\n\n        targetMesh.position.copy(getPosition(targetBone, _matrix))\n        effectorMesh.position.copy(getPosition(effectorBone, _matrix))\n\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = ik.links[j]\n          const linkBone = bones[link.index]\n\n          const linkMesh = this.children[offset++]\n\n          linkMesh.position.copy(getPosition(linkBone, _matrix))\n        }\n\n        const line = this.children[offset++]\n        const array = line.geometry.attributes.position.array\n\n        setPositionOfBoneToAttributeArray(array, 0, targetBone, _matrix)\n        setPositionOfBoneToAttributeArray(array, 1, effectorBone, _matrix)\n\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = ik.links[j]\n          const linkBone = bones[link.index]\n          setPositionOfBoneToAttributeArray(array, j + 2, linkBone, _matrix)\n        }\n\n        line.geometry.attributes.position.needsUpdate = true\n      }\n    }\n\n    this.matrix.copy(mesh.matrixWorld)\n\n    super.updateMatrixWorld(force)\n  }\n\n  /**\n   * Frees the GPU-related resources allocated by this instance. Call this method whenever this instance is no longer used in your app.\n   */\n  dispose() {\n    this.sphereGeometry.dispose()\n\n    this.targetSphereMaterial.dispose()\n    this.effectorSphereMaterial.dispose()\n    this.linkSphereMaterial.dispose()\n    this.lineMaterial.dispose()\n\n    const children = this.children\n\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i]\n\n      if (child.isLine) child.geometry.dispose()\n    }\n  }\n\n  // private method\n\n  _init() {\n    const scope = this\n    const iks = this.iks\n\n    function createLineGeometry(ik) {\n      const geometry = new BufferGeometry()\n      const vertices = new Float32Array((2 + ik.links.length) * 3)\n      geometry.setAttribute('position', new BufferAttribute(vertices, 3))\n\n      return geometry\n    }\n\n    function createTargetMesh() {\n      return new Mesh(scope.sphereGeometry, scope.targetSphereMaterial)\n    }\n\n    function createEffectorMesh() {\n      return new Mesh(scope.sphereGeometry, scope.effectorSphereMaterial)\n    }\n\n    function createLinkMesh() {\n      return new Mesh(scope.sphereGeometry, scope.linkSphereMaterial)\n    }\n\n    function createLine(ik) {\n      return new Line(createLineGeometry(ik), scope.lineMaterial)\n    }\n\n    for (let i = 0, il = iks.length; i < il; i++) {\n      const ik = iks[i]\n\n      this.add(createTargetMesh())\n      this.add(createEffectorMesh())\n\n      for (let j = 0, jl = ik.links.length; j < jl; j++) {\n        this.add(createLinkMesh())\n      }\n\n      this.add(createLine(ik))\n    }\n  }\n}\n\nexport { CCDIKSolver, CCDIKHelper }\n"], "names": [], "mappings": ";AAeA,MAAM,KAAqB,oBAAI,WAAY;AAC3C,MAAM,aAA6B,oBAAI,QAAS;AAChD,MAAM,aAA6B,oBAAI,QAAS;AAChD,MAAM,eAA+B,oBAAI,QAAS;AAClD,MAAM,eAA+B,oBAAI,QAAS;AAClD,MAAM,WAA2B,oBAAI,QAAS;AAC9C,MAAM,YAA4B,oBAAI,WAAY;AAClD,MAAM,aAA6B,oBAAI,QAAS;AAChD,MAAM,QAAwB,oBAAI,QAAS;AAC3C,MAAM,UAA0B,oBAAI,QAAS;AAC7C,MAAM,UAA0B,oBAAI,QAAS;AAsB7C,MAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,YAAY,MAAM,MAAM,IAAI;AAC1B,SAAK,OAAO;AACZ,SAAK,MAAM;AAEX,SAAK,OAAQ;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,SAAS;AACP,UAAM,MAAM,KAAK;AAEjB,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAC5C,WAAK,UAAU,IAAI,CAAC,CAAC;AAAA,IACtB;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,UAAU,IAAI;AACZ,UAAM,QAAQ,KAAK,KAAK,SAAS;AAGjC,UAAM,OAAO;AAEb,UAAM,WAAW,MAAM,GAAG,QAAQ;AAClC,UAAM,SAAS,MAAM,GAAG,MAAM;AAI9B,eAAW,sBAAsB,OAAO,WAAW;AAEnD,UAAM,QAAQ,GAAG;AACjB,UAAM,YAAY,GAAG,cAAc,SAAY,GAAG,YAAY;AAE9D,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,UAAI,UAAU;AAEd,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,cAAM,OAAO,MAAM,MAAM,CAAC,EAAE,KAAK;AAIjC,YAAI,MAAM,CAAC,EAAE,YAAY;AAAO;AAEhC,cAAM,aAAa,MAAM,CAAC,EAAE;AAC5B,cAAM,cAAc,MAAM,CAAC,EAAE;AAC7B,cAAM,cAAc,MAAM,CAAC,EAAE;AAI7B,aAAK,YAAY,UAAU,UAAU,WAAW,UAAU;AAC1D,kBAAU,OAAQ;AAClB,qBAAa,sBAAsB,SAAS,WAAW;AAGvD,qBAAa,WAAW,cAAc,QAAQ;AAC9C,qBAAa,gBAAgB,SAAS;AACtC,qBAAa,UAAW;AAExB,mBAAW,WAAW,YAAY,QAAQ;AAC1C,mBAAW,gBAAgB,SAAS;AACpC,mBAAW,UAAW;AAEtB,YAAI,QAAQ,WAAW,IAAI,YAAY;AAEvC,YAAI,QAAQ,GAAK;AACf,kBAAQ;AAAA,QAClB,WAAmB,QAAQ,IAAM;AACvB,kBAAQ;AAAA,QACT;AAED,gBAAQ,KAAK,KAAK,KAAK;AAGvB,YAAI,QAAQ;AAAM;AAElB,YAAI,GAAG,aAAa,UAAa,QAAQ,GAAG,UAAU;AACpD,kBAAQ,GAAG;AAAA,QACZ;AAED,YAAI,GAAG,aAAa,UAAa,QAAQ,GAAG,UAAU;AACpD,kBAAQ,GAAG;AAAA,QACZ;AAED,cAAM,aAAa,cAAc,UAAU;AAC3C,cAAM,UAAW;AAEjB,WAAG,iBAAiB,OAAO,KAAK;AAChC,aAAK,WAAW,SAAS,EAAE;AAG3B,YAAI,eAAe,QAAW;AAC5B,cAAI,IAAI,KAAK,WAAW;AAExB,cAAI,IAAI;AAAK,gBAAI;AAEjB,gBAAM,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC;AAC9B,eAAK,WAAW,IAAI,WAAW,IAAI,IAAI,WAAW,IAAI,IAAI,WAAW,IAAI,IAAI,CAAC;AAAA,QAC/E;AAED,YAAI,gBAAgB,QAAW;AAC7B,eAAK,SAAS,eAAe,QAAQ,aAAa,KAAK,QAAQ,EAAE,IAAI,WAAW,CAAC;AAAA,QAClF;AAED,YAAI,gBAAgB,QAAW;AAC7B,eAAK,SAAS,eAAe,QAAQ,aAAa,KAAK,QAAQ,EAAE,IAAI,WAAW,CAAC;AAAA,QAClF;AAED,aAAK,kBAAkB,IAAI;AAE3B,kBAAU;AAAA,MACX;AAED,UAAI,CAAC;AAAS;AAAA,IACf;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,eAAe;AACb,WAAO,IAAI,YAAY,KAAK,MAAM,KAAK,GAAG;AAAA,EAC3C;AAAA;AAAA,EAID,SAAS;AACP,UAAM,MAAM,KAAK;AACjB,UAAM,QAAQ,KAAK,KAAK,SAAS;AAEjC,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAC5C,YAAM,KAAK,IAAI,CAAC;AAChB,YAAM,WAAW,MAAM,GAAG,QAAQ;AAClC,YAAM,QAAQ,GAAG;AACjB,UAAI,OAAO;AAEX,cAAQ;AAER,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,gBAAQ,MAAM,MAAM,CAAC,EAAE,KAAK;AAE5B,YAAI,MAAM,WAAW,OAAO;AAC1B,kBAAQ,KAAK,6BAA6B,MAAM,OAAO,+BAA+B,MAAM,IAAI;AAAA,QACjG;AAED,gBAAQ;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACH;AAEA,SAAS,YAAY,MAAM,gBAAgB;AACzC,SAAO,QAAQ,sBAAsB,KAAK,WAAW,EAAE,aAAa,cAAc;AACpF;AAEA,SAAS,kCAAkC,OAAO,OAAO,MAAM,gBAAgB;AAC7E,QAAM,IAAI,YAAY,MAAM,cAAc;AAE1C,QAAM,QAAQ,IAAI,CAAC,IAAI,EAAE;AACzB,QAAM,QAAQ,IAAI,CAAC,IAAI,EAAE;AACzB,QAAM,QAAQ,IAAI,CAAC,IAAI,EAAE;AAC3B;AAQA,MAAM,oBAAoB,SAAS;AAAA,EACjC,YAAY,MAAM,MAAM,CAAA,GAAI,aAAa,MAAM;AAC7C,UAAO;AAEP,SAAK,OAAO;AACZ,SAAK,MAAM;AAEX,SAAK,OAAO,KAAK,KAAK,WAAW;AACjC,SAAK,mBAAmB;AAExB,SAAK,iBAAiB,IAAI,eAAe,YAAY,IAAI,CAAC;AAE1D,SAAK,uBAAuB,IAAI,kBAAkB;AAAA,MAChD,OAAO,IAAI,MAAM,QAAQ;AAAA,MACzB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IACnB,CAAK;AAED,SAAK,yBAAyB,IAAI,kBAAkB;AAAA,MAClD,OAAO,IAAI,MAAM,OAAQ;AAAA,MACzB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IACnB,CAAK;AAED,SAAK,qBAAqB,IAAI,kBAAkB;AAAA,MAC9C,OAAO,IAAI,MAAM,OAAQ;AAAA,MACzB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IACnB,CAAK;AAED,SAAK,eAAe,IAAI,kBAAkB;AAAA,MACxC,OAAO,IAAI,MAAM,QAAQ;AAAA,MACzB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IACnB,CAAK;AAED,SAAK,MAAO;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB,OAAO;AACvB,UAAM,OAAO,KAAK;AAElB,QAAI,KAAK,SAAS;AAChB,UAAI,SAAS;AAEb,YAAM,MAAM,KAAK;AACjB,YAAM,QAAQ,KAAK,SAAS;AAE5B,cAAQ,KAAK,KAAK,WAAW,EAAE,OAAQ;AAEvC,eAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAC5C,cAAM,KAAK,IAAI,CAAC;AAEhB,cAAM,aAAa,MAAM,GAAG,MAAM;AAClC,cAAM,eAAe,MAAM,GAAG,QAAQ;AAEtC,cAAM,aAAa,KAAK,SAAS,QAAQ;AACzC,cAAM,eAAe,KAAK,SAAS,QAAQ;AAE3C,mBAAW,SAAS,KAAK,YAAY,YAAY,OAAO,CAAC;AACzD,qBAAa,SAAS,KAAK,YAAY,cAAc,OAAO,CAAC;AAE7D,iBAAS,IAAI,GAAG,KAAK,GAAG,MAAM,QAAQ,IAAI,IAAI,KAAK;AACjD,gBAAM,OAAO,GAAG,MAAM,CAAC;AACvB,gBAAM,WAAW,MAAM,KAAK,KAAK;AAEjC,gBAAM,WAAW,KAAK,SAAS,QAAQ;AAEvC,mBAAS,SAAS,KAAK,YAAY,UAAU,OAAO,CAAC;AAAA,QACtD;AAED,cAAM,OAAO,KAAK,SAAS,QAAQ;AACnC,cAAM,QAAQ,KAAK,SAAS,WAAW,SAAS;AAEhD,0CAAkC,OAAO,GAAG,YAAY,OAAO;AAC/D,0CAAkC,OAAO,GAAG,cAAc,OAAO;AAEjE,iBAAS,IAAI,GAAG,KAAK,GAAG,MAAM,QAAQ,IAAI,IAAI,KAAK;AACjD,gBAAM,OAAO,GAAG,MAAM,CAAC;AACvB,gBAAM,WAAW,MAAM,KAAK,KAAK;AACjC,4CAAkC,OAAO,IAAI,GAAG,UAAU,OAAO;AAAA,QAClE;AAED,aAAK,SAAS,WAAW,SAAS,cAAc;AAAA,MACjD;AAAA,IACF;AAED,SAAK,OAAO,KAAK,KAAK,WAAW;AAEjC,UAAM,kBAAkB,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU;AACR,SAAK,eAAe,QAAS;AAE7B,SAAK,qBAAqB,QAAS;AACnC,SAAK,uBAAuB,QAAS;AACrC,SAAK,mBAAmB,QAAS;AACjC,SAAK,aAAa,QAAS;AAE3B,UAAM,WAAW,KAAK;AAEtB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,QAAQ,SAAS,CAAC;AAExB,UAAI,MAAM;AAAQ,cAAM,SAAS,QAAS;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA,EAID,QAAQ;AACN,UAAM,QAAQ;AACd,UAAM,MAAM,KAAK;AAEjB,aAAS,mBAAmB,IAAI;AAC9B,YAAM,WAAW,IAAI,eAAgB;AACrC,YAAM,WAAW,IAAI,cAAc,IAAI,GAAG,MAAM,UAAU,CAAC;AAC3D,eAAS,aAAa,YAAY,IAAI,gBAAgB,UAAU,CAAC,CAAC;AAElE,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB;AAC1B,aAAO,IAAI,KAAK,MAAM,gBAAgB,MAAM,oBAAoB;AAAA,IACjE;AAED,aAAS,qBAAqB;AAC5B,aAAO,IAAI,KAAK,MAAM,gBAAgB,MAAM,sBAAsB;AAAA,IACnE;AAED,aAAS,iBAAiB;AACxB,aAAO,IAAI,KAAK,MAAM,gBAAgB,MAAM,kBAAkB;AAAA,IAC/D;AAED,aAAS,WAAW,IAAI;AACtB,aAAO,IAAI,KAAK,mBAAmB,EAAE,GAAG,MAAM,YAAY;AAAA,IAC3D;AAED,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAC5C,YAAM,KAAK,IAAI,CAAC;AAEhB,WAAK,IAAI,kBAAkB;AAC3B,WAAK,IAAI,oBAAoB;AAE7B,eAAS,IAAI,GAAG,KAAK,GAAG,MAAM,QAAQ,IAAI,IAAI,KAAK;AACjD,aAAK,IAAI,gBAAgB;AAAA,MAC1B;AAED,WAAK,IAAI,WAAW,EAAE,CAAC;AAAA,IACxB;AAAA,EACF;AACH;"}