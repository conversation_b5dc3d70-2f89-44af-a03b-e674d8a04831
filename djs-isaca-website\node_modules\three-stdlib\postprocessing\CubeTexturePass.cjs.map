{"version": 3, "file": "CubeTexturePass.cjs", "sources": ["../../src/postprocessing/CubeTexturePass.js"], "sourcesContent": ["import { BackSide, BoxGeometry, Mesh, PerspectiveCamera, Scene, ShaderLib, ShaderMaterial, UniformsUtils } from 'three'\nimport { Pass } from './Pass'\n\nclass CubeTexturePass extends Pass {\n  constructor(camera, tCube, opacity = 1) {\n    super()\n\n    this.camera = camera\n\n    this.needsSwap = false\n\n    this.cubeShader = ShaderLib['cube']\n    this.cubeMesh = new Mesh(\n      new BoxGeometry(10, 10, 10),\n      new ShaderMaterial({\n        uniforms: UniformsUtils.clone(this.cubeShader.uniforms),\n        vertexShader: this.cubeShader.vertexShader,\n        fragmentShader: this.cubeShader.fragmentShader,\n        depthTest: false,\n        depthWrite: false,\n        side: BackSide,\n      }),\n    )\n\n    Object.defineProperty(this.cubeMesh.material, 'envMap', {\n      get: function () {\n        return this.uniforms.tCube.value\n      },\n    })\n\n    this.tCube = tCube\n    this.opacity = opacity\n\n    this.cubeScene = new Scene()\n    this.cubeCamera = new PerspectiveCamera()\n    this.cubeScene.add(this.cubeMesh)\n  }\n\n  render(renderer, writeBuffer, readBuffer /*, deltaTime, maskActive*/) {\n    const oldAutoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    this.cubeCamera.projectionMatrix.copy(this.camera.projectionMatrix)\n    this.cubeCamera.quaternion.setFromRotationMatrix(this.camera.matrixWorld)\n\n    this.cubeMesh.material.uniforms.tCube.value = this.tCube\n    this.cubeMesh.material.uniforms.tFlip.value =\n      this.tCube.isCubeTexture && this.tCube.isRenderTargetTexture === false ? -1 : 1\n    this.cubeMesh.material.uniforms.opacity.value = this.opacity\n    this.cubeMesh.material.transparent = this.opacity < 1.0\n\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer)\n    if (this.clear) renderer.clear()\n    renderer.render(this.cubeScene, this.cubeCamera)\n\n    renderer.autoClear = oldAutoClear\n  }\n\n  dispose() {\n    this.cubeMesh.geometry.dispose()\n    this.cubeMesh.material.dispose()\n  }\n}\n\nexport { CubeTexturePass }\n"], "names": ["Pass", "ShaderLib", "<PERSON><PERSON>", "BoxGeometry", "ShaderMaterial", "UniformsUtils", "BackSide", "Scene", "PerspectiveCamera"], "mappings": ";;;;AAGA,MAAM,wBAAwBA,KAAAA,KAAK;AAAA,EACjC,YAAY,QAAQ,OAAO,UAAU,GAAG;AACtC,UAAO;AAEP,SAAK,SAAS;AAEd,SAAK,YAAY;AAEjB,SAAK,aAAaC,MAAS,UAAC,MAAM;AAClC,SAAK,WAAW,IAAIC,MAAI;AAAA,MACtB,IAAIC,kBAAY,IAAI,IAAI,EAAE;AAAA,MAC1B,IAAIC,qBAAe;AAAA,QACjB,UAAUC,MAAAA,cAAc,MAAM,KAAK,WAAW,QAAQ;AAAA,QACtD,cAAc,KAAK,WAAW;AAAA,QAC9B,gBAAgB,KAAK,WAAW;AAAA,QAChC,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,MAAMC,MAAQ;AAAA,MACtB,CAAO;AAAA,IACF;AAED,WAAO,eAAe,KAAK,SAAS,UAAU,UAAU;AAAA,MACtD,KAAK,WAAY;AACf,eAAO,KAAK,SAAS,MAAM;AAAA,MAC5B;AAAA,IACP,CAAK;AAED,SAAK,QAAQ;AACb,SAAK,UAAU;AAEf,SAAK,YAAY,IAAIC,YAAO;AAC5B,SAAK,aAAa,IAAIC,wBAAmB;AACzC,SAAK,UAAU,IAAI,KAAK,QAAQ;AAAA,EACjC;AAAA,EAED,OAAO,UAAU,aAAa,YAAwC;AACpE,UAAM,eAAe,SAAS;AAC9B,aAAS,YAAY;AAErB,SAAK,WAAW,iBAAiB,KAAK,KAAK,OAAO,gBAAgB;AAClE,SAAK,WAAW,WAAW,sBAAsB,KAAK,OAAO,WAAW;AAExE,SAAK,SAAS,SAAS,SAAS,MAAM,QAAQ,KAAK;AACnD,SAAK,SAAS,SAAS,SAAS,MAAM,QACpC,KAAK,MAAM,iBAAiB,KAAK,MAAM,0BAA0B,QAAQ,KAAK;AAChF,SAAK,SAAS,SAAS,SAAS,QAAQ,QAAQ,KAAK;AACrD,SAAK,SAAS,SAAS,cAAc,KAAK,UAAU;AAEpD,aAAS,gBAAgB,KAAK,iBAAiB,OAAO,UAAU;AAChE,QAAI,KAAK;AAAO,eAAS,MAAO;AAChC,aAAS,OAAO,KAAK,WAAW,KAAK,UAAU;AAE/C,aAAS,YAAY;AAAA,EACtB;AAAA,EAED,UAAU;AACR,SAAK,SAAS,SAAS,QAAS;AAChC,SAAK,SAAS,SAAS,QAAS;AAAA,EACjC;AACH;;"}