{"version": 3, "file": "PDBLoader.cjs", "sources": ["../../src/loaders/PDBLoader.js"], "sourcesContent": ["import { <PERSON>ufferGeo<PERSON>, FileLoader, Float32<PERSON>uffer<PERSON><PERSON><PERSON><PERSON><PERSON>, Loader } from 'three'\n\nclass PDBLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  // Based on CanvasMol PDB parser\n\n  parse(text) {\n    function trim(text) {\n      return text.replace(/^\\s\\s*/, '').replace(/\\s\\s*$/, '')\n    }\n\n    function capitalize(text) {\n      return text.charAt(0).toUpperCase() + text.substr(1).toLowerCase()\n    }\n\n    function hash(s, e) {\n      return 's' + Math.min(s, e) + 'e' + Math.max(s, e)\n    }\n\n    function parseBond(start, length, satom, i) {\n      const eatom = parseInt(lines[i].substr(start, length))\n\n      if (eatom) {\n        const h = hash(satom, eatom)\n\n        if (_bhash[h] === undefined) {\n          _bonds.push([satom - 1, eatom - 1, 1])\n          _bhash[h] = _bonds.length - 1\n        } else {\n          // doesn't really work as almost all PDBs\n          // have just normal bonds appearing multiple\n          // times instead of being double/triple bonds\n          // bonds[bhash[h]][2] += 1;\n        }\n      }\n    }\n\n    function buildGeometry() {\n      const build = {\n        geometryAtoms: new BufferGeometry(),\n        geometryBonds: new BufferGeometry(),\n        json: {\n          atoms: atoms,\n        },\n      }\n\n      const geometryAtoms = build.geometryAtoms\n      const geometryBonds = build.geometryBonds\n\n      const verticesAtoms = []\n      const colorsAtoms = []\n      const verticesBonds = []\n\n      // atoms\n\n      for (let i = 0, l = atoms.length; i < l; i++) {\n        const atom = atoms[i]\n\n        const x = atom[0]\n        const y = atom[1]\n        const z = atom[2]\n\n        verticesAtoms.push(x, y, z)\n\n        const r = atom[3][0] / 255\n        const g = atom[3][1] / 255\n        const b = atom[3][2] / 255\n\n        colorsAtoms.push(r, g, b)\n      }\n\n      // bonds\n\n      for (let i = 0, l = _bonds.length; i < l; i++) {\n        const bond = _bonds[i]\n\n        const start = bond[0]\n        const end = bond[1]\n\n        const startAtom = _atomMap[start]\n        const endAtom = _atomMap[end]\n\n        let x = startAtom[0]\n        let y = startAtom[1]\n        let z = startAtom[2]\n\n        verticesBonds.push(x, y, z)\n\n        x = endAtom[0]\n        y = endAtom[1]\n        z = endAtom[2]\n\n        verticesBonds.push(x, y, z)\n      }\n\n      // build geometry\n\n      geometryAtoms.setAttribute('position', new Float32BufferAttribute(verticesAtoms, 3))\n      geometryAtoms.setAttribute('color', new Float32BufferAttribute(colorsAtoms, 3))\n\n      geometryBonds.setAttribute('position', new Float32BufferAttribute(verticesBonds, 3))\n\n      return build\n    }\n\n    const CPK = {\n      h: [255, 255, 255],\n      he: [217, 255, 255],\n      li: [204, 128, 255],\n      be: [194, 255, 0],\n      b: [255, 181, 181],\n      c: [144, 144, 144],\n      n: [48, 80, 248],\n      o: [255, 13, 13],\n      f: [144, 224, 80],\n      ne: [179, 227, 245],\n      na: [171, 92, 242],\n      mg: [138, 255, 0],\n      al: [191, 166, 166],\n      si: [240, 200, 160],\n      p: [255, 128, 0],\n      s: [255, 255, 48],\n      cl: [31, 240, 31],\n      ar: [128, 209, 227],\n      k: [143, 64, 212],\n      ca: [61, 255, 0],\n      sc: [230, 230, 230],\n      ti: [191, 194, 199],\n      v: [166, 166, 171],\n      cr: [138, 153, 199],\n      mn: [156, 122, 199],\n      fe: [224, 102, 51],\n      co: [240, 144, 160],\n      ni: [80, 208, 80],\n      cu: [200, 128, 51],\n      zn: [125, 128, 176],\n      ga: [194, 143, 143],\n      ge: [102, 143, 143],\n      as: [189, 128, 227],\n      se: [255, 161, 0],\n      br: [166, 41, 41],\n      kr: [92, 184, 209],\n      rb: [112, 46, 176],\n      sr: [0, 255, 0],\n      y: [148, 255, 255],\n      zr: [148, 224, 224],\n      nb: [115, 194, 201],\n      mo: [84, 181, 181],\n      tc: [59, 158, 158],\n      ru: [36, 143, 143],\n      rh: [10, 125, 140],\n      pd: [0, 105, 133],\n      ag: [192, 192, 192],\n      cd: [255, 217, 143],\n      in: [166, 117, 115],\n      sn: [102, 128, 128],\n      sb: [158, 99, 181],\n      te: [212, 122, 0],\n      i: [148, 0, 148],\n      xe: [66, 158, 176],\n      cs: [87, 23, 143],\n      ba: [0, 201, 0],\n      la: [112, 212, 255],\n      ce: [255, 255, 199],\n      pr: [217, 255, 199],\n      nd: [199, 255, 199],\n      pm: [163, 255, 199],\n      sm: [143, 255, 199],\n      eu: [97, 255, 199],\n      gd: [69, 255, 199],\n      tb: [48, 255, 199],\n      dy: [31, 255, 199],\n      ho: [0, 255, 156],\n      er: [0, 230, 117],\n      tm: [0, 212, 82],\n      yb: [0, 191, 56],\n      lu: [0, 171, 36],\n      hf: [77, 194, 255],\n      ta: [77, 166, 255],\n      w: [33, 148, 214],\n      re: [38, 125, 171],\n      os: [38, 102, 150],\n      ir: [23, 84, 135],\n      pt: [208, 208, 224],\n      au: [255, 209, 35],\n      hg: [184, 184, 208],\n      tl: [166, 84, 77],\n      pb: [87, 89, 97],\n      bi: [158, 79, 181],\n      po: [171, 92, 0],\n      at: [117, 79, 69],\n      rn: [66, 130, 150],\n      fr: [66, 0, 102],\n      ra: [0, 125, 0],\n      ac: [112, 171, 250],\n      th: [0, 186, 255],\n      pa: [0, 161, 255],\n      u: [0, 143, 255],\n      np: [0, 128, 255],\n      pu: [0, 107, 255],\n      am: [84, 92, 242],\n      cm: [120, 92, 227],\n      bk: [138, 79, 227],\n      cf: [161, 54, 212],\n      es: [179, 31, 212],\n      fm: [179, 31, 186],\n      md: [179, 13, 166],\n      no: [189, 13, 135],\n      lr: [199, 0, 102],\n      rf: [204, 0, 89],\n      db: [209, 0, 79],\n      sg: [217, 0, 69],\n      bh: [224, 0, 56],\n      hs: [230, 0, 46],\n      mt: [235, 0, 38],\n      ds: [235, 0, 38],\n      rg: [235, 0, 38],\n      cn: [235, 0, 38],\n      uut: [235, 0, 38],\n      uuq: [235, 0, 38],\n      uup: [235, 0, 38],\n      uuh: [235, 0, 38],\n      uus: [235, 0, 38],\n      uuo: [235, 0, 38],\n    }\n\n    const atoms = []\n\n    const _bonds = []\n    const _bhash = {}\n    const _atomMap = {}\n\n    // parse\n\n    const lines = text.split('\\n')\n\n    for (let i = 0, l = lines.length; i < l; i++) {\n      if (lines[i].substr(0, 4) === 'ATOM' || lines[i].substr(0, 6) === 'HETATM') {\n        const x = parseFloat(lines[i].substr(30, 7))\n        const y = parseFloat(lines[i].substr(38, 7))\n        const z = parseFloat(lines[i].substr(46, 7))\n        const index = parseInt(lines[i].substr(6, 5)) - 1\n\n        let e = trim(lines[i].substr(76, 2)).toLowerCase()\n\n        if (e === '') {\n          e = trim(lines[i].substr(12, 2)).toLowerCase()\n        }\n\n        const atomData = [x, y, z, CPK[e], capitalize(e)]\n\n        atoms.push(atomData)\n        _atomMap[index] = atomData\n      } else if (lines[i].substr(0, 6) === 'CONECT') {\n        const satom = parseInt(lines[i].substr(6, 5))\n\n        parseBond(11, 5, satom, i)\n        parseBond(16, 5, satom, i)\n        parseBond(21, 5, satom, i)\n        parseBond(26, 5, satom, i)\n      }\n    }\n\n    // build and return geometry\n\n    return buildGeometry()\n  }\n}\n\nexport { PDBLoader }\n"], "names": ["Loader", "<PERSON><PERSON><PERSON><PERSON>", "text", "BufferGeometry", "Float32BufferAttribute"], "mappings": ";;;AAEA,MAAM,kBAAkBA,MAAAA,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAIC,iBAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAC/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,IAAI,CAAC;AAAA,QACzB,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA;AAAA,EAID,MAAM,MAAM;AACV,aAAS,KAAKC,OAAM;AAClB,aAAOA,MAAK,QAAQ,UAAU,EAAE,EAAE,QAAQ,UAAU,EAAE;AAAA,IACvD;AAED,aAAS,WAAWA,OAAM;AACxB,aAAOA,MAAK,OAAO,CAAC,EAAE,gBAAgBA,MAAK,OAAO,CAAC,EAAE,YAAa;AAAA,IACnE;AAED,aAAS,KAAK,GAAG,GAAG;AAClB,aAAO,MAAM,KAAK,IAAI,GAAG,CAAC,IAAI,MAAM,KAAK,IAAI,GAAG,CAAC;AAAA,IAClD;AAED,aAAS,UAAU,OAAO,QAAQ,OAAO,GAAG;AAC1C,YAAM,QAAQ,SAAS,MAAM,CAAC,EAAE,OAAO,OAAO,MAAM,CAAC;AAErD,UAAI,OAAO;AACT,cAAM,IAAI,KAAK,OAAO,KAAK;AAE3B,YAAI,OAAO,CAAC,MAAM,QAAW;AAC3B,iBAAO,KAAK,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;AACrC,iBAAO,CAAC,IAAI,OAAO,SAAS;AAAA,QAM7B;AAAA,MACF;AAAA,IACF;AAED,aAAS,gBAAgB;AACvB,YAAM,QAAQ;AAAA,QACZ,eAAe,IAAIC,MAAAA,eAAgB;AAAA,QACnC,eAAe,IAAIA,MAAAA,eAAgB;AAAA,QACnC,MAAM;AAAA,UACJ;AAAA,QACD;AAAA,MACF;AAED,YAAM,gBAAgB,MAAM;AAC5B,YAAM,gBAAgB,MAAM;AAE5B,YAAM,gBAAgB,CAAE;AACxB,YAAM,cAAc,CAAE;AACtB,YAAM,gBAAgB,CAAE;AAIxB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,OAAO,MAAM,CAAC;AAEpB,cAAM,IAAI,KAAK,CAAC;AAChB,cAAM,IAAI,KAAK,CAAC;AAChB,cAAM,IAAI,KAAK,CAAC;AAEhB,sBAAc,KAAK,GAAG,GAAG,CAAC;AAE1B,cAAM,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI;AACvB,cAAM,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI;AACvB,cAAM,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI;AAEvB,oBAAY,KAAK,GAAG,GAAG,CAAC;AAAA,MACzB;AAID,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,OAAO,OAAO,CAAC;AAErB,cAAM,QAAQ,KAAK,CAAC;AACpB,cAAM,MAAM,KAAK,CAAC;AAElB,cAAM,YAAY,SAAS,KAAK;AAChC,cAAM,UAAU,SAAS,GAAG;AAE5B,YAAI,IAAI,UAAU,CAAC;AACnB,YAAI,IAAI,UAAU,CAAC;AACnB,YAAI,IAAI,UAAU,CAAC;AAEnB,sBAAc,KAAK,GAAG,GAAG,CAAC;AAE1B,YAAI,QAAQ,CAAC;AACb,YAAI,QAAQ,CAAC;AACb,YAAI,QAAQ,CAAC;AAEb,sBAAc,KAAK,GAAG,GAAG,CAAC;AAAA,MAC3B;AAID,oBAAc,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,eAAe,CAAC,CAAC;AACnF,oBAAc,aAAa,SAAS,IAAIA,MAAAA,uBAAuB,aAAa,CAAC,CAAC;AAE9E,oBAAc,aAAa,YAAY,IAAIA,MAAAA,uBAAuB,eAAe,CAAC,CAAC;AAEnF,aAAO;AAAA,IACR;AAED,UAAM,MAAM;AAAA,MACV,GAAG,CAAC,KAAK,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,CAAC;AAAA,MAChB,GAAG,CAAC,KAAK,KAAK,GAAG;AAAA,MACjB,GAAG,CAAC,KAAK,KAAK,GAAG;AAAA,MACjB,GAAG,CAAC,IAAI,IAAI,GAAG;AAAA,MACf,GAAG,CAAC,KAAK,IAAI,EAAE;AAAA,MACf,GAAG,CAAC,KAAK,KAAK,EAAE;AAAA,MAChB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,KAAK,CAAC;AAAA,MAChB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,GAAG,CAAC,KAAK,KAAK,CAAC;AAAA,MACf,GAAG,CAAC,KAAK,KAAK,EAAE;AAAA,MAChB,IAAI,CAAC,IAAI,KAAK,EAAE;AAAA,MAChB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,GAAG,CAAC,KAAK,IAAI,GAAG;AAAA,MAChB,IAAI,CAAC,IAAI,KAAK,CAAC;AAAA,MACf,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,GAAG,CAAC,KAAK,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,EAAE;AAAA,MACjB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,IAAI,KAAK,EAAE;AAAA,MAChB,IAAI,CAAC,KAAK,KAAK,EAAE;AAAA,MACjB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,CAAC;AAAA,MAChB,IAAI,CAAC,KAAK,IAAI,EAAE;AAAA,MAChB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,GAAG,KAAK,CAAC;AAAA,MACd,GAAG,CAAC,KAAK,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,GAAG,KAAK,GAAG;AAAA,MAChB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,KAAK,CAAC;AAAA,MAChB,GAAG,CAAC,KAAK,GAAG,GAAG;AAAA,MACf,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,IAAI,GAAG;AAAA,MAChB,IAAI,CAAC,GAAG,KAAK,CAAC;AAAA,MACd,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,GAAG,KAAK,GAAG;AAAA,MAChB,IAAI,CAAC,GAAG,KAAK,GAAG;AAAA,MAChB,IAAI,CAAC,GAAG,KAAK,EAAE;AAAA,MACf,IAAI,CAAC,GAAG,KAAK,EAAE;AAAA,MACf,IAAI,CAAC,GAAG,KAAK,EAAE;AAAA,MACf,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,GAAG,CAAC,IAAI,KAAK,GAAG;AAAA,MAChB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,IAAI,GAAG;AAAA,MAChB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,KAAK,EAAE;AAAA,MACjB,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,KAAK,IAAI,EAAE;AAAA,MAChB,IAAI,CAAC,IAAI,IAAI,EAAE;AAAA,MACf,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,IAAI,CAAC;AAAA,MACf,IAAI,CAAC,KAAK,IAAI,EAAE;AAAA,MAChB,IAAI,CAAC,IAAI,KAAK,GAAG;AAAA,MACjB,IAAI,CAAC,IAAI,GAAG,GAAG;AAAA,MACf,IAAI,CAAC,GAAG,KAAK,CAAC;AAAA,MACd,IAAI,CAAC,KAAK,KAAK,GAAG;AAAA,MAClB,IAAI,CAAC,GAAG,KAAK,GAAG;AAAA,MAChB,IAAI,CAAC,GAAG,KAAK,GAAG;AAAA,MAChB,GAAG,CAAC,GAAG,KAAK,GAAG;AAAA,MACf,IAAI,CAAC,GAAG,KAAK,GAAG;AAAA,MAChB,IAAI,CAAC,GAAG,KAAK,GAAG;AAAA,MAChB,IAAI,CAAC,IAAI,IAAI,GAAG;AAAA,MAChB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,IAAI,GAAG;AAAA,MACjB,IAAI,CAAC,KAAK,GAAG,GAAG;AAAA,MAChB,IAAI,CAAC,KAAK,GAAG,EAAE;AAAA,MACf,IAAI,CAAC,KAAK,GAAG,EAAE;AAAA,MACf,IAAI,CAAC,KAAK,GAAG,EAAE;AAAA,MACf,IAAI,CAAC,KAAK,GAAG,EAAE;AAAA,MACf,IAAI,CAAC,KAAK,GAAG,EAAE;AAAA,MACf,IAAI,CAAC,KAAK,GAAG,EAAE;AAAA,MACf,IAAI,CAAC,KAAK,GAAG,EAAE;AAAA,MACf,IAAI,CAAC,KAAK,GAAG,EAAE;AAAA,MACf,IAAI,CAAC,KAAK,GAAG,EAAE;AAAA,MACf,KAAK,CAAC,KAAK,GAAG,EAAE;AAAA,MAChB,KAAK,CAAC,KAAK,GAAG,EAAE;AAAA,MAChB,KAAK,CAAC,KAAK,GAAG,EAAE;AAAA,MAChB,KAAK,CAAC,KAAK,GAAG,EAAE;AAAA,MAChB,KAAK,CAAC,KAAK,GAAG,EAAE;AAAA,MAChB,KAAK,CAAC,KAAK,GAAG,EAAE;AAAA,IACjB;AAED,UAAM,QAAQ,CAAE;AAEhB,UAAM,SAAS,CAAE;AACjB,UAAM,SAAS,CAAE;AACjB,UAAM,WAAW,CAAE;AAInB,UAAM,QAAQ,KAAK,MAAM,IAAI;AAE7B,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,UAAI,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC,MAAM,UAAU,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC,MAAM,UAAU;AAC1E,cAAM,IAAI,WAAW,MAAM,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;AAC3C,cAAM,IAAI,WAAW,MAAM,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;AAC3C,cAAM,IAAI,WAAW,MAAM,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;AAC3C,cAAM,QAAQ,SAAS,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,IAAI;AAEhD,YAAI,IAAI,KAAK,MAAM,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,YAAa;AAElD,YAAI,MAAM,IAAI;AACZ,cAAI,KAAK,MAAM,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,YAAa;AAAA,QAC/C;AAED,cAAM,WAAW,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;AAEhD,cAAM,KAAK,QAAQ;AACnB,iBAAS,KAAK,IAAI;AAAA,MAC1B,WAAiB,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC,MAAM,UAAU;AAC7C,cAAM,QAAQ,SAAS,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC;AAE5C,kBAAU,IAAI,GAAG,OAAO,CAAC;AACzB,kBAAU,IAAI,GAAG,OAAO,CAAC;AACzB,kBAAU,IAAI,GAAG,OAAO,CAAC;AACzB,kBAAU,IAAI,GAAG,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF;AAID,WAAO,cAAe;AAAA,EACvB;AACH;;"}