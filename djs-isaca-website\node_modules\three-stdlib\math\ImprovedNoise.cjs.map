{"version": 3, "file": "ImprovedNoise.cjs", "sources": ["../../src/math/ImprovedNoise.js"], "sourcesContent": ["// https://cs.nyu.edu/~perlin/noise/\n\nfunction init() {\n  // prettier-ignore\n  const _p = [ 151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10,\n    23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87,\n    174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211,\n    133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208,\n    89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5,\n    202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119,\n    248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232,\n    178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249,\n    14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205,\n    93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180 ];\n\n  for (let i = 0; i < 256; i++) {\n    _p[256 + i] = _p[i]\n  }\n\n  return _p\n}\n\nconst _p = /* @__PURE__ */ init()\n\nfunction fade(t) {\n  return t * t * t * (t * (t * 6 - 15) + 10)\n}\n\nfunction lerp(t, a, b) {\n  return a + t * (b - a)\n}\n\nfunction grad(hash, x, y, z) {\n  const h = hash & 15\n  const u = h < 8 ? x : y,\n    v = h < 4 ? y : h == 12 || h == 14 ? x : z\n  return ((h & 1) == 0 ? u : -u) + ((h & 2) == 0 ? v : -v)\n}\n\nclass ImprovedNoise {\n  noise(x, y, z) {\n    const floorX = Math.floor(x),\n      floorY = Math.floor(y),\n      floorZ = Math.floor(z)\n\n    const X = floorX & 255,\n      Y = floorY & 255,\n      Z = floorZ & 255\n\n    x -= floorX\n    y -= floorY\n    z -= floorZ\n\n    const xMinus1 = x - 1,\n      yMinus1 = y - 1,\n      zMinus1 = z - 1\n\n    const u = fade(x),\n      v = fade(y),\n      w = fade(z)\n\n    const A = _p[X] + Y,\n      AA = _p[A] + Z,\n      AB = _p[A + 1] + Z,\n      B = _p[X + 1] + Y,\n      BA = _p[B] + Z,\n      BB = _p[B + 1] + Z\n\n    return lerp(\n      w,\n      lerp(\n        v,\n        lerp(u, grad(_p[AA], x, y, z), grad(_p[BA], xMinus1, y, z)),\n        lerp(u, grad(_p[AB], x, yMinus1, z), grad(_p[BB], xMinus1, yMinus1, z)),\n      ),\n      lerp(\n        v,\n        lerp(u, grad(_p[AA + 1], x, y, zMinus1), grad(_p[BA + 1], xMinus1, y, zMinus1)),\n        lerp(u, grad(_p[AB + 1], x, yMinus1, zMinus1), grad(_p[BB + 1], xMinus1, yMinus1, zMinus1)),\n      ),\n    )\n  }\n}\n\nexport { ImprovedNoise }\n"], "names": ["_p"], "mappings": ";;AAEA,SAAS,OAAO;AAEd,QAAMA,MAAK;AAAA,IAAE;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAG;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAG;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IACjI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAG;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAG;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IACvH;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IACpH;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAG;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IACpH;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAG;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IACtH;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IACnH;AAAA,IAAK;AAAA,IAAK;AAAA,IAAG;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAG;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IACtH;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IACnH;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAG;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IACrH;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAK;AAAA,IAAI;AAAA,IAAI;AAAA,IAAK;AAAA,IAAI;AAAA,IAAK;AAAA;AAE1E,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,IAAAA,IAAG,MAAM,CAAC,IAAIA,IAAG,CAAC;AAAA,EACnB;AAED,SAAOA;AACT;AAEA,MAAM,KAAqB,qBAAM;AAEjC,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM;AACzC;AAEA,SAAS,KAAK,GAAG,GAAG,GAAG;AACrB,SAAO,IAAI,KAAK,IAAI;AACtB;AAEA,SAAS,KAAK,MAAM,GAAG,GAAG,GAAG;AAC3B,QAAM,IAAI,OAAO;AACjB,QAAM,IAAI,IAAI,IAAI,IAAI,GACpB,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI;AAC3C,WAAS,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,MAAM,IAAI,IAAI,CAAC;AACxD;AAEA,MAAM,cAAc;AAAA,EAClB,MAAM,GAAG,GAAG,GAAG;AACb,UAAM,SAAS,KAAK,MAAM,CAAC,GACzB,SAAS,KAAK,MAAM,CAAC,GACrB,SAAS,KAAK,MAAM,CAAC;AAEvB,UAAM,IAAI,SAAS,KACjB,IAAI,SAAS,KACb,IAAI,SAAS;AAEf,SAAK;AACL,SAAK;AACL,SAAK;AAEL,UAAM,UAAU,IAAI,GAClB,UAAU,IAAI,GACd,UAAU,IAAI;AAEhB,UAAM,IAAI,KAAK,CAAC,GACd,IAAI,KAAK,CAAC,GACV,IAAI,KAAK,CAAC;AAEZ,UAAM,IAAI,GAAG,CAAC,IAAI,GAChB,KAAK,GAAG,CAAC,IAAI,GACb,KAAK,GAAG,IAAI,CAAC,IAAI,GACjB,IAAI,GAAG,IAAI,CAAC,IAAI,GAChB,KAAK,GAAG,CAAC,IAAI,GACb,KAAK,GAAG,IAAI,CAAC,IAAI;AAEnB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,QACE;AAAA,QACA,KAAK,GAAG,KAAK,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,SAAS,GAAG,CAAC,CAAC;AAAA,QAC1D,KAAK,GAAG,KAAK,GAAG,EAAE,GAAG,GAAG,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,SAAS,SAAS,CAAC,CAAC;AAAA,MACvE;AAAA,MACD;AAAA,QACE;AAAA,QACA,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC;AAAA,QAC9E,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,SAAS,OAAO,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,SAAS,SAAS,OAAO,CAAC;AAAA,MAC3F;AAAA,IACF;AAAA,EACF;AACH;;"}