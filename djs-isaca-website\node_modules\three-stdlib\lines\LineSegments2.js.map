{"version": 3, "file": "LineSegments2.js", "sources": ["../../src/lines/LineSegments2.js"], "sourcesContent": ["import {\n  Box3,\n  InstancedInterleavedBuffer,\n  InterleavedBuffer<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Line3,\n  MathUtils,\n  Matrix4,\n  Mesh,\n  Sphere,\n  Vector3,\n  Vector4,\n} from 'three'\nimport { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\nimport { UV1 } from '../_polyfill/uv1'\n\nconst _viewport = /* @__PURE__ */ new Vector4()\n\nconst _start = /* @__PURE__ */ new Vector3()\nconst _end = /* @__PURE__ */ new Vector3()\n\nconst _start4 = /* @__PURE__ */ new Vector4()\nconst _end4 = /* @__PURE__ */ new Vector4()\n\nconst _ssOrigin = /* @__PURE__ */ new Vector4()\nconst _ssOrigin3 = /* @__PURE__ */ new Vector3()\nconst _mvMatrix = /* @__PURE__ */ new Matrix4()\nconst _line = /* @__PURE__ */ new Line3()\nconst _closestPoint = /* @__PURE__ */ new Vector3()\n\nconst _box = /* @__PURE__ */ new Box3()\nconst _sphere = /* @__PURE__ */ new Sphere()\nconst _clipToWorldVector = /* @__PURE__ */ new Vector4()\n\nlet _ray, _lineWidth\n\n// Returns the margin required to expand by in world space given the distance from the camera,\n// line width, resolution, and camera projection\nfunction getWorldSpaceHalfWidth(camera, distance, resolution) {\n  // transform into clip space, adjust the x and y values by the pixel width offset, then\n  // transform back into world space to get world offset. Note clip space is [-1, 1] so full\n  // width does not need to be halved.\n  _clipToWorldVector.set(0, 0, -distance, 1.0).applyMatrix4(camera.projectionMatrix)\n  _clipToWorldVector.multiplyScalar(1.0 / _clipToWorldVector.w)\n  _clipToWorldVector.x = _lineWidth / resolution.width\n  _clipToWorldVector.y = _lineWidth / resolution.height\n  _clipToWorldVector.applyMatrix4(camera.projectionMatrixInverse)\n  _clipToWorldVector.multiplyScalar(1.0 / _clipToWorldVector.w)\n\n  return Math.abs(Math.max(_clipToWorldVector.x, _clipToWorldVector.y))\n}\n\nfunction raycastWorldUnits(lineSegments, intersects) {\n  const matrixWorld = lineSegments.matrixWorld\n  const geometry = lineSegments.geometry\n  const instanceStart = geometry.attributes.instanceStart\n  const instanceEnd = geometry.attributes.instanceEnd\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count)\n\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _line.start.fromBufferAttribute(instanceStart, i)\n    _line.end.fromBufferAttribute(instanceEnd, i)\n\n    _line.applyMatrix4(matrixWorld)\n\n    const pointOnLine = new Vector3()\n    const point = new Vector3()\n\n    _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine)\n    const isInside = point.distanceTo(pointOnLine) < _lineWidth * 0.5\n\n    if (isInside) {\n      intersects.push({\n        point,\n        pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null,\n      })\n    }\n  }\n}\n\nfunction raycastScreenSpace(lineSegments, camera, intersects) {\n  const projectionMatrix = camera.projectionMatrix\n  const material = lineSegments.material\n  const resolution = material.resolution\n  const matrixWorld = lineSegments.matrixWorld\n\n  const geometry = lineSegments.geometry\n  const instanceStart = geometry.attributes.instanceStart\n  const instanceEnd = geometry.attributes.instanceEnd\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count)\n\n  const near = -camera.near\n\n  //\n\n  // pick a point 1 unit out along the ray to avoid the ray origin\n  // sitting at the camera origin which will cause \"w\" to be 0 when\n  // applying the projection matrix.\n  _ray.at(1, _ssOrigin)\n\n  // ndc space [ - 1.0, 1.0 ]\n  _ssOrigin.w = 1\n  _ssOrigin.applyMatrix4(camera.matrixWorldInverse)\n  _ssOrigin.applyMatrix4(projectionMatrix)\n  _ssOrigin.multiplyScalar(1 / _ssOrigin.w)\n\n  // screen space\n  _ssOrigin.x *= resolution.x / 2\n  _ssOrigin.y *= resolution.y / 2\n  _ssOrigin.z = 0\n\n  _ssOrigin3.copy(_ssOrigin)\n\n  _mvMatrix.multiplyMatrices(camera.matrixWorldInverse, matrixWorld)\n\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _start4.fromBufferAttribute(instanceStart, i)\n    _end4.fromBufferAttribute(instanceEnd, i)\n\n    _start4.w = 1\n    _end4.w = 1\n\n    // camera space\n    _start4.applyMatrix4(_mvMatrix)\n    _end4.applyMatrix4(_mvMatrix)\n\n    // skip the segment if it's entirely behind the camera\n    const isBehindCameraNear = _start4.z > near && _end4.z > near\n    if (isBehindCameraNear) {\n      continue\n    }\n\n    // trim the segment if it extends behind camera near\n    if (_start4.z > near) {\n      const deltaDist = _start4.z - _end4.z\n      const t = (_start4.z - near) / deltaDist\n      _start4.lerp(_end4, t)\n    } else if (_end4.z > near) {\n      const deltaDist = _end4.z - _start4.z\n      const t = (_end4.z - near) / deltaDist\n      _end4.lerp(_start4, t)\n    }\n\n    // clip space\n    _start4.applyMatrix4(projectionMatrix)\n    _end4.applyMatrix4(projectionMatrix)\n\n    // ndc space [ - 1.0, 1.0 ]\n    _start4.multiplyScalar(1 / _start4.w)\n    _end4.multiplyScalar(1 / _end4.w)\n\n    // screen space\n    _start4.x *= resolution.x / 2\n    _start4.y *= resolution.y / 2\n\n    _end4.x *= resolution.x / 2\n    _end4.y *= resolution.y / 2\n\n    // create 2d segment\n    _line.start.copy(_start4)\n    _line.start.z = 0\n\n    _line.end.copy(_end4)\n    _line.end.z = 0\n\n    // get closest point on ray to segment\n    const param = _line.closestPointToPointParameter(_ssOrigin3, true)\n    _line.at(param, _closestPoint)\n\n    // check if the intersection point is within clip space\n    const zPos = MathUtils.lerp(_start4.z, _end4.z, param)\n    const isInClipSpace = zPos >= -1 && zPos <= 1\n\n    const isInside = _ssOrigin3.distanceTo(_closestPoint) < _lineWidth * 0.5\n\n    if (isInClipSpace && isInside) {\n      _line.start.fromBufferAttribute(instanceStart, i)\n      _line.end.fromBufferAttribute(instanceEnd, i)\n\n      _line.start.applyMatrix4(matrixWorld)\n      _line.end.applyMatrix4(matrixWorld)\n\n      const pointOnLine = new Vector3()\n      const point = new Vector3()\n\n      _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine)\n\n      intersects.push({\n        point: point,\n        pointOnLine: pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null,\n      })\n    }\n  }\n}\n\nclass LineSegments2 extends Mesh {\n  constructor(geometry = new LineSegmentsGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isLineSegments2 = true\n\n    this.type = 'LineSegments2'\n  }\n\n  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...\n\n  computeLineDistances() {\n    const geometry = this.geometry\n\n    const instanceStart = geometry.attributes.instanceStart\n    const instanceEnd = geometry.attributes.instanceEnd\n    const lineDistances = new Float32Array(2 * instanceStart.count)\n\n    for (let i = 0, j = 0, l = instanceStart.count; i < l; i++, j += 2) {\n      _start.fromBufferAttribute(instanceStart, i)\n      _end.fromBufferAttribute(instanceEnd, i)\n\n      lineDistances[j] = j === 0 ? 0 : lineDistances[j - 1]\n      lineDistances[j + 1] = lineDistances[j] + _start.distanceTo(_end)\n    }\n\n    const instanceDistanceBuffer = new InstancedInterleavedBuffer(lineDistances, 2, 1) // d0, d1\n\n    geometry.setAttribute('instanceDistanceStart', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 0)) // d0\n    geometry.setAttribute('instanceDistanceEnd', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 1)) // d1\n\n    return this\n  }\n\n  raycast(raycaster, intersects) {\n    const worldUnits = this.material.worldUnits\n    const camera = raycaster.camera\n\n    if (camera === null && !worldUnits) {\n      console.error(\n        'LineSegments2: \"Raycaster.camera\" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.',\n      )\n    }\n\n    const threshold = raycaster.params.Line2 !== undefined ? raycaster.params.Line2.threshold || 0 : 0\n\n    _ray = raycaster.ray\n\n    const matrixWorld = this.matrixWorld\n    const geometry = this.geometry\n    const material = this.material\n\n    _lineWidth = material.linewidth + threshold\n\n    // check if we intersect the sphere bounds\n    if (geometry.boundingSphere === null) {\n      geometry.computeBoundingSphere()\n    }\n\n    _sphere.copy(geometry.boundingSphere).applyMatrix4(matrixWorld)\n\n    // increase the sphere bounds by the worst case line screen space width\n    let sphereMargin\n    if (worldUnits) {\n      sphereMargin = _lineWidth * 0.5\n    } else {\n      const distanceToSphere = Math.max(camera.near, _sphere.distanceToPoint(_ray.origin))\n      sphereMargin = getWorldSpaceHalfWidth(camera, distanceToSphere, material.resolution)\n    }\n\n    _sphere.radius += sphereMargin\n\n    if (_ray.intersectsSphere(_sphere) === false) {\n      return\n    }\n\n    // check if we intersect the box bounds\n    if (geometry.boundingBox === null) {\n      geometry.computeBoundingBox()\n    }\n\n    _box.copy(geometry.boundingBox).applyMatrix4(matrixWorld)\n\n    // increase the box bounds by the worst case line width\n    let boxMargin\n    if (worldUnits) {\n      boxMargin = _lineWidth * 0.5\n    } else {\n      const distanceToBox = Math.max(camera.near, _box.distanceToPoint(_ray.origin))\n      boxMargin = getWorldSpaceHalfWidth(camera, distanceToBox, material.resolution)\n    }\n\n    _box.expandByScalar(boxMargin)\n\n    if (_ray.intersectsBox(_box) === false) {\n      return\n    }\n\n    if (worldUnits) {\n      raycastWorldUnits(this, intersects)\n    } else {\n      raycastScreenSpace(this, camera, intersects)\n    }\n  }\n\n  onBeforeRender(renderer) {\n    const uniforms = this.material.uniforms\n\n    if (uniforms && uniforms.resolution) {\n      renderer.getViewport(_viewport)\n      this.material.uniforms.resolution.value.set(_viewport.z, _viewport.w)\n    }\n  }\n}\n\nexport { LineSegments2 }\n"], "names": [], "mappings": ";;;;AAgBA,MAAM,YAA4B,oBAAI,QAAS;AAE/C,MAAM,SAAyB,oBAAI,QAAS;AAC5C,MAAM,OAAuB,oBAAI,QAAS;AAE1C,MAAM,UAA0B,oBAAI,QAAS;AAC7C,MAAM,QAAwB,oBAAI,QAAS;AAE3C,MAAM,YAA4B,oBAAI,QAAS;AAC/C,MAAM,aAA6B,oBAAI,QAAS;AAChD,MAAM,YAA4B,oBAAI,QAAS;AAC/C,MAAM,QAAwB,oBAAI,MAAO;AACzC,MAAM,gBAAgC,oBAAI,QAAS;AAEnD,MAAM,OAAuB,oBAAI,KAAM;AACvC,MAAM,UAA0B,oBAAI,OAAQ;AAC5C,MAAM,qBAAqC,oBAAI,QAAS;AAExD,IAAI,MAAM;AAIV,SAAS,uBAAuB,QAAQ,UAAU,YAAY;AAI5D,qBAAmB,IAAI,GAAG,GAAG,CAAC,UAAU,CAAG,EAAE,aAAa,OAAO,gBAAgB;AACjF,qBAAmB,eAAe,IAAM,mBAAmB,CAAC;AAC5D,qBAAmB,IAAI,aAAa,WAAW;AAC/C,qBAAmB,IAAI,aAAa,WAAW;AAC/C,qBAAmB,aAAa,OAAO,uBAAuB;AAC9D,qBAAmB,eAAe,IAAM,mBAAmB,CAAC;AAE5D,SAAO,KAAK,IAAI,KAAK,IAAI,mBAAmB,GAAG,mBAAmB,CAAC,CAAC;AACtE;AAEA,SAAS,kBAAkB,cAAc,YAAY;AACnD,QAAM,cAAc,aAAa;AACjC,QAAM,WAAW,aAAa;AAC9B,QAAM,gBAAgB,SAAS,WAAW;AAC1C,QAAM,cAAc,SAAS,WAAW;AACxC,QAAM,eAAe,KAAK,IAAI,SAAS,eAAe,cAAc,KAAK;AAEzE,WAAS,IAAI,GAAG,IAAI,cAAc,IAAI,GAAG,KAAK;AAC5C,UAAM,MAAM,oBAAoB,eAAe,CAAC;AAChD,UAAM,IAAI,oBAAoB,aAAa,CAAC;AAE5C,UAAM,aAAa,WAAW;AAE9B,UAAM,cAAc,IAAI,QAAS;AACjC,UAAM,QAAQ,IAAI,QAAS;AAE3B,SAAK,oBAAoB,MAAM,OAAO,MAAM,KAAK,OAAO,WAAW;AACnE,UAAM,WAAW,MAAM,WAAW,WAAW,IAAI,aAAa;AAE9D,QAAI,UAAU;AACZ,iBAAW,KAAK;AAAA,QACd;AAAA,QACA;AAAA,QACA,UAAU,KAAK,OAAO,WAAW,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,WAAW;AAAA,QACX,IAAI;AAAA,QACJ,CAAC,GAAG,GAAG;AAAA,MACf,CAAO;AAAA,IACF;AAAA,EACF;AACH;AAEA,SAAS,mBAAmB,cAAc,QAAQ,YAAY;AAC5D,QAAM,mBAAmB,OAAO;AAChC,QAAM,WAAW,aAAa;AAC9B,QAAM,aAAa,SAAS;AAC5B,QAAM,cAAc,aAAa;AAEjC,QAAM,WAAW,aAAa;AAC9B,QAAM,gBAAgB,SAAS,WAAW;AAC1C,QAAM,cAAc,SAAS,WAAW;AACxC,QAAM,eAAe,KAAK,IAAI,SAAS,eAAe,cAAc,KAAK;AAEzE,QAAM,OAAO,CAAC,OAAO;AAOrB,OAAK,GAAG,GAAG,SAAS;AAGpB,YAAU,IAAI;AACd,YAAU,aAAa,OAAO,kBAAkB;AAChD,YAAU,aAAa,gBAAgB;AACvC,YAAU,eAAe,IAAI,UAAU,CAAC;AAGxC,YAAU,KAAK,WAAW,IAAI;AAC9B,YAAU,KAAK,WAAW,IAAI;AAC9B,YAAU,IAAI;AAEd,aAAW,KAAK,SAAS;AAEzB,YAAU,iBAAiB,OAAO,oBAAoB,WAAW;AAEjE,WAAS,IAAI,GAAG,IAAI,cAAc,IAAI,GAAG,KAAK;AAC5C,YAAQ,oBAAoB,eAAe,CAAC;AAC5C,UAAM,oBAAoB,aAAa,CAAC;AAExC,YAAQ,IAAI;AACZ,UAAM,IAAI;AAGV,YAAQ,aAAa,SAAS;AAC9B,UAAM,aAAa,SAAS;AAG5B,UAAM,qBAAqB,QAAQ,IAAI,QAAQ,MAAM,IAAI;AACzD,QAAI,oBAAoB;AACtB;AAAA,IACD;AAGD,QAAI,QAAQ,IAAI,MAAM;AACpB,YAAM,YAAY,QAAQ,IAAI,MAAM;AACpC,YAAM,KAAK,QAAQ,IAAI,QAAQ;AAC/B,cAAQ,KAAK,OAAO,CAAC;AAAA,IAC3B,WAAe,MAAM,IAAI,MAAM;AACzB,YAAM,YAAY,MAAM,IAAI,QAAQ;AACpC,YAAM,KAAK,MAAM,IAAI,QAAQ;AAC7B,YAAM,KAAK,SAAS,CAAC;AAAA,IACtB;AAGD,YAAQ,aAAa,gBAAgB;AACrC,UAAM,aAAa,gBAAgB;AAGnC,YAAQ,eAAe,IAAI,QAAQ,CAAC;AACpC,UAAM,eAAe,IAAI,MAAM,CAAC;AAGhC,YAAQ,KAAK,WAAW,IAAI;AAC5B,YAAQ,KAAK,WAAW,IAAI;AAE5B,UAAM,KAAK,WAAW,IAAI;AAC1B,UAAM,KAAK,WAAW,IAAI;AAG1B,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,MAAM,IAAI;AAEhB,UAAM,IAAI,KAAK,KAAK;AACpB,UAAM,IAAI,IAAI;AAGd,UAAM,QAAQ,MAAM,6BAA6B,YAAY,IAAI;AACjE,UAAM,GAAG,OAAO,aAAa;AAG7B,UAAM,OAAO,UAAU,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AACrD,UAAM,gBAAgB,QAAQ,MAAM,QAAQ;AAE5C,UAAM,WAAW,WAAW,WAAW,aAAa,IAAI,aAAa;AAErE,QAAI,iBAAiB,UAAU;AAC7B,YAAM,MAAM,oBAAoB,eAAe,CAAC;AAChD,YAAM,IAAI,oBAAoB,aAAa,CAAC;AAE5C,YAAM,MAAM,aAAa,WAAW;AACpC,YAAM,IAAI,aAAa,WAAW;AAElC,YAAM,cAAc,IAAI,QAAS;AACjC,YAAM,QAAQ,IAAI,QAAS;AAE3B,WAAK,oBAAoB,MAAM,OAAO,MAAM,KAAK,OAAO,WAAW;AAEnE,iBAAW,KAAK;AAAA,QACd;AAAA,QACA;AAAA,QACA,UAAU,KAAK,OAAO,WAAW,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,WAAW;AAAA,QACX,IAAI;AAAA,QACJ,CAAC,GAAG,GAAG;AAAA,MACf,CAAO;AAAA,IACF;AAAA,EACF;AACH;AAEA,MAAM,sBAAsB,KAAK;AAAA,EAC/B,YAAY,WAAW,IAAI,qBAAsB,GAAE,WAAW,IAAI,aAAa,EAAE,OAAO,KAAK,WAAW,SAAU,CAAA,GAAG;AACnH,UAAM,UAAU,QAAQ;AAExB,SAAK,kBAAkB;AAEvB,SAAK,OAAO;AAAA,EACb;AAAA;AAAA,EAID,uBAAuB;AACrB,UAAM,WAAW,KAAK;AAEtB,UAAM,gBAAgB,SAAS,WAAW;AAC1C,UAAM,cAAc,SAAS,WAAW;AACxC,UAAM,gBAAgB,IAAI,aAAa,IAAI,cAAc,KAAK;AAE9D,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,cAAc,OAAO,IAAI,GAAG,KAAK,KAAK,GAAG;AAClE,aAAO,oBAAoB,eAAe,CAAC;AAC3C,WAAK,oBAAoB,aAAa,CAAC;AAEvC,oBAAc,CAAC,IAAI,MAAM,IAAI,IAAI,cAAc,IAAI,CAAC;AACpD,oBAAc,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,OAAO,WAAW,IAAI;AAAA,IACjE;AAED,UAAM,yBAAyB,IAAI,2BAA2B,eAAe,GAAG,CAAC;AAEjF,aAAS,aAAa,yBAAyB,IAAI,2BAA2B,wBAAwB,GAAG,CAAC,CAAC;AAC3G,aAAS,aAAa,uBAAuB,IAAI,2BAA2B,wBAAwB,GAAG,CAAC,CAAC;AAEzG,WAAO;AAAA,EACR;AAAA,EAED,QAAQ,WAAW,YAAY;AAC7B,UAAM,aAAa,KAAK,SAAS;AACjC,UAAM,SAAS,UAAU;AAEzB,QAAI,WAAW,QAAQ,CAAC,YAAY;AAClC,cAAQ;AAAA,QACN;AAAA,MACD;AAAA,IACF;AAED,UAAM,YAAY,UAAU,OAAO,UAAU,SAAY,UAAU,OAAO,MAAM,aAAa,IAAI;AAEjG,WAAO,UAAU;AAEjB,UAAM,cAAc,KAAK;AACzB,UAAM,WAAW,KAAK;AACtB,UAAM,WAAW,KAAK;AAEtB,iBAAa,SAAS,YAAY;AAGlC,QAAI,SAAS,mBAAmB,MAAM;AACpC,eAAS,sBAAuB;AAAA,IACjC;AAED,YAAQ,KAAK,SAAS,cAAc,EAAE,aAAa,WAAW;AAG9D,QAAI;AACJ,QAAI,YAAY;AACd,qBAAe,aAAa;AAAA,IAClC,OAAW;AACL,YAAM,mBAAmB,KAAK,IAAI,OAAO,MAAM,QAAQ,gBAAgB,KAAK,MAAM,CAAC;AACnF,qBAAe,uBAAuB,QAAQ,kBAAkB,SAAS,UAAU;AAAA,IACpF;AAED,YAAQ,UAAU;AAElB,QAAI,KAAK,iBAAiB,OAAO,MAAM,OAAO;AAC5C;AAAA,IACD;AAGD,QAAI,SAAS,gBAAgB,MAAM;AACjC,eAAS,mBAAoB;AAAA,IAC9B;AAED,SAAK,KAAK,SAAS,WAAW,EAAE,aAAa,WAAW;AAGxD,QAAI;AACJ,QAAI,YAAY;AACd,kBAAY,aAAa;AAAA,IAC/B,OAAW;AACL,YAAM,gBAAgB,KAAK,IAAI,OAAO,MAAM,KAAK,gBAAgB,KAAK,MAAM,CAAC;AAC7E,kBAAY,uBAAuB,QAAQ,eAAe,SAAS,UAAU;AAAA,IAC9E;AAED,SAAK,eAAe,SAAS;AAE7B,QAAI,KAAK,cAAc,IAAI,MAAM,OAAO;AACtC;AAAA,IACD;AAED,QAAI,YAAY;AACd,wBAAkB,MAAM,UAAU;AAAA,IACxC,OAAW;AACL,yBAAmB,MAAM,QAAQ,UAAU;AAAA,IAC5C;AAAA,EACF;AAAA,EAED,eAAe,UAAU;AACvB,UAAM,WAAW,KAAK,SAAS;AAE/B,QAAI,YAAY,SAAS,YAAY;AACnC,eAAS,YAAY,SAAS;AAC9B,WAAK,SAAS,SAAS,WAAW,MAAM,IAAI,UAAU,GAAG,UAAU,CAAC;AAAA,IACrE;AAAA,EACF;AACH;"}