{"version": 3, "file": "CinematicCamera.cjs", "sources": ["../../src/cameras/CinematicCamera.js"], "sourcesContent": ["import {\n  Mesh,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PlaneGeometry,\n  Scene,\n  ShaderMaterial,\n  UniformsUtils,\n  WebGLRenderTarget,\n} from 'three'\nimport { BokehShader2, BokehDepthShader } from '../shaders/BokehShader2'\n\nclass CinematicCamera extends PerspectiveCamera {\n  constructor(fov, aspect, near, far) {\n    super(fov, aspect, near, far)\n\n    this.type = 'CinematicCamera'\n\n    this.postprocessing = { enabled: true }\n    this.shaderSettings = {\n      rings: 3,\n      samples: 4,\n    }\n\n    const depthShader = BokehDepthShader\n\n    this.materialDepth = new ShaderMaterial({\n      uniforms: depthShader.uniforms,\n      vertexShader: depthShader.vertexShader,\n      fragmentShader: depthShader.fragmentShader,\n    })\n\n    this.materialDepth.uniforms['mNear'].value = near\n    this.materialDepth.uniforms['mFar'].value = far\n\n    // In case of cinematicCamera, having a default lens set is important\n    this.setLens()\n\n    this.initPostProcessing()\n  }\n\n  // providing fnumber and coc(Circle of Confusion) as extra arguments\n  setLens(focalLength, filmGauge, fNumber, coc) {\n    // In case of cinematicCamera, having a default lens set is important\n    if (focalLength === undefined) focalLength = 35\n    if (filmGauge !== undefined) this.filmGauge = filmGauge\n\n    this.setFocalLength(focalLength)\n\n    // if fnumber and coc are not provided, cinematicCamera tries to act as a basic PerspectiveCamera\n    if (fNumber === undefined) fNumber = 8\n    if (coc === undefined) coc = 0.019\n\n    this.fNumber = fNumber\n    this.coc = coc\n\n    // fNumber is focalLength by aperture\n    this.aperture = focalLength / this.fNumber\n\n    // hyperFocal is required to calculate depthOfField when a lens tries to focus at a distance with given fNumber and focalLength\n    this.hyperFocal = (focalLength * focalLength) / (this.aperture * this.coc)\n  }\n\n  linearize(depth) {\n    const zfar = this.far\n    const znear = this.near\n    return (-zfar * znear) / (depth * (zfar - znear) - zfar)\n  }\n\n  smoothstep(near, far, depth) {\n    const x = this.saturate((depth - near) / (far - near))\n    return x * x * (3 - 2 * x)\n  }\n\n  saturate(x) {\n    return Math.max(0, Math.min(1, x))\n  }\n\n  // function for focusing at a distance from the camera\n  focusAt(focusDistance) {\n    if (focusDistance === undefined) focusDistance = 20\n\n    const focalLength = this.getFocalLength()\n\n    // distance from the camera (normal to frustrum) to focus on\n    this.focus = focusDistance\n\n    // the nearest point from the camera which is in focus (unused)\n    this.nearPoint = (this.hyperFocal * this.focus) / (this.hyperFocal + (this.focus - focalLength))\n\n    // the farthest point from the camera which is in focus (unused)\n    this.farPoint = (this.hyperFocal * this.focus) / (this.hyperFocal - (this.focus - focalLength))\n\n    // the gap or width of the space in which is everything is in focus (unused)\n    this.depthOfField = this.farPoint - this.nearPoint\n\n    // Considering minimum distance of focus for a standard lens (unused)\n    if (this.depthOfField < 0) this.depthOfField = 0\n\n    this.sdistance = this.smoothstep(this.near, this.far, this.focus)\n\n    this.ldistance = this.linearize(1 - this.sdistance)\n\n    this.postprocessing.bokeh_uniforms['focalDepth'].value = this.ldistance\n  }\n\n  initPostProcessing() {\n    if (this.postprocessing.enabled) {\n      this.postprocessing.scene = new Scene()\n\n      this.postprocessing.camera = new OrthographicCamera(\n        window.innerWidth / -2,\n        window.innerWidth / 2,\n        window.innerHeight / 2,\n        window.innerHeight / -2,\n        -10000,\n        10000,\n      )\n\n      this.postprocessing.scene.add(this.postprocessing.camera)\n\n      this.postprocessing.rtTextureDepth = new WebGLRenderTarget(window.innerWidth, window.innerHeight)\n      this.postprocessing.rtTextureColor = new WebGLRenderTarget(window.innerWidth, window.innerHeight)\n\n      const bokeh_shader = BokehShader2\n\n      this.postprocessing.bokeh_uniforms = UniformsUtils.clone(bokeh_shader.uniforms)\n\n      this.postprocessing.bokeh_uniforms['tColor'].value = this.postprocessing.rtTextureColor.texture\n      this.postprocessing.bokeh_uniforms['tDepth'].value = this.postprocessing.rtTextureDepth.texture\n\n      this.postprocessing.bokeh_uniforms['manualdof'].value = 0\n      this.postprocessing.bokeh_uniforms['shaderFocus'].value = 0\n\n      this.postprocessing.bokeh_uniforms['fstop'].value = 2.8\n\n      this.postprocessing.bokeh_uniforms['showFocus'].value = 1\n\n      this.postprocessing.bokeh_uniforms['focalDepth'].value = 0.1\n\n      //console.log( this.postprocessing.bokeh_uniforms[ \"focalDepth\" ].value );\n\n      this.postprocessing.bokeh_uniforms['znear'].value = this.near\n      this.postprocessing.bokeh_uniforms['zfar'].value = this.near\n\n      this.postprocessing.bokeh_uniforms['textureWidth'].value = window.innerWidth\n\n      this.postprocessing.bokeh_uniforms['textureHeight'].value = window.innerHeight\n\n      this.postprocessing.materialBokeh = new ShaderMaterial({\n        uniforms: this.postprocessing.bokeh_uniforms,\n        vertexShader: bokeh_shader.vertexShader,\n        fragmentShader: bokeh_shader.fragmentShader,\n        defines: {\n          RINGS: this.shaderSettings.rings,\n          SAMPLES: this.shaderSettings.samples,\n          DEPTH_PACKING: 1,\n        },\n      })\n\n      this.postprocessing.quad = new Mesh(\n        new PlaneGeometry(window.innerWidth, window.innerHeight),\n        this.postprocessing.materialBokeh,\n      )\n      this.postprocessing.quad.position.z = -500\n      this.postprocessing.scene.add(this.postprocessing.quad)\n    }\n  }\n\n  renderCinematic(scene, renderer) {\n    if (this.postprocessing.enabled) {\n      const currentRenderTarget = renderer.getRenderTarget()\n\n      renderer.clear()\n\n      // Render scene into texture\n\n      scene.overrideMaterial = null\n      renderer.setRenderTarget(this.postprocessing.rtTextureColor)\n      renderer.clear()\n      renderer.render(scene, this)\n\n      // Render depth into texture\n\n      scene.overrideMaterial = this.materialDepth\n      renderer.setRenderTarget(this.postprocessing.rtTextureDepth)\n      renderer.clear()\n      renderer.render(scene, this)\n\n      // Render bokeh composite\n\n      renderer.setRenderTarget(null)\n      renderer.render(this.postprocessing.scene, this.postprocessing.camera)\n\n      renderer.setRenderTarget(currentRenderTarget)\n    }\n  }\n}\n\nexport { CinematicCamera }\n"], "names": ["PerspectiveCamera", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ShaderMaterial", "Scene", "OrthographicCamera", "WebGLRenderTarget", "BokehShader2", "UniformsUtils", "<PERSON><PERSON>", "PlaneGeometry"], "mappings": ";;;;AAYA,MAAM,wBAAwBA,MAAAA,kBAAkB;AAAA,EAC9C,YAAY,KAAK,QAAQ,MAAM,KAAK;AAClC,UAAM,KAAK,QAAQ,MAAM,GAAG;AAE5B,SAAK,OAAO;AAEZ,SAAK,iBAAiB,EAAE,SAAS,KAAM;AACvC,SAAK,iBAAiB;AAAA,MACpB,OAAO;AAAA,MACP,SAAS;AAAA,IACV;AAED,UAAM,cAAcC,aAAgB;AAEpC,SAAK,gBAAgB,IAAIC,qBAAe;AAAA,MACtC,UAAU,YAAY;AAAA,MACtB,cAAc,YAAY;AAAA,MAC1B,gBAAgB,YAAY;AAAA,IAClC,CAAK;AAED,SAAK,cAAc,SAAS,OAAO,EAAE,QAAQ;AAC7C,SAAK,cAAc,SAAS,MAAM,EAAE,QAAQ;AAG5C,SAAK,QAAS;AAEd,SAAK,mBAAoB;AAAA,EAC1B;AAAA;AAAA,EAGD,QAAQ,aAAa,WAAW,SAAS,KAAK;AAE5C,QAAI,gBAAgB;AAAW,oBAAc;AAC7C,QAAI,cAAc;AAAW,WAAK,YAAY;AAE9C,SAAK,eAAe,WAAW;AAG/B,QAAI,YAAY;AAAW,gBAAU;AACrC,QAAI,QAAQ;AAAW,YAAM;AAE7B,SAAK,UAAU;AACf,SAAK,MAAM;AAGX,SAAK,WAAW,cAAc,KAAK;AAGnC,SAAK,aAAc,cAAc,eAAgB,KAAK,WAAW,KAAK;AAAA,EACvE;AAAA,EAED,UAAU,OAAO;AACf,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,KAAK;AACnB,WAAQ,CAAC,OAAO,SAAU,SAAS,OAAO,SAAS;AAAA,EACpD;AAAA,EAED,WAAW,MAAM,KAAK,OAAO;AAC3B,UAAM,IAAI,KAAK,UAAU,QAAQ,SAAS,MAAM,KAAK;AACrD,WAAO,IAAI,KAAK,IAAI,IAAI;AAAA,EACzB;AAAA,EAED,SAAS,GAAG;AACV,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAAA,EAClC;AAAA;AAAA,EAGD,QAAQ,eAAe;AACrB,QAAI,kBAAkB;AAAW,sBAAgB;AAEjD,UAAM,cAAc,KAAK,eAAgB;AAGzC,SAAK,QAAQ;AAGb,SAAK,YAAa,KAAK,aAAa,KAAK,SAAU,KAAK,cAAc,KAAK,QAAQ;AAGnF,SAAK,WAAY,KAAK,aAAa,KAAK,SAAU,KAAK,cAAc,KAAK,QAAQ;AAGlF,SAAK,eAAe,KAAK,WAAW,KAAK;AAGzC,QAAI,KAAK,eAAe;AAAG,WAAK,eAAe;AAE/C,SAAK,YAAY,KAAK,WAAW,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK;AAEhE,SAAK,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS;AAElD,SAAK,eAAe,eAAe,YAAY,EAAE,QAAQ,KAAK;AAAA,EAC/D;AAAA,EAED,qBAAqB;AACnB,QAAI,KAAK,eAAe,SAAS;AAC/B,WAAK,eAAe,QAAQ,IAAIC,YAAO;AAEvC,WAAK,eAAe,SAAS,IAAIC,MAAkB;AAAA,QACjD,OAAO,aAAa;AAAA,QACpB,OAAO,aAAa;AAAA,QACpB,OAAO,cAAc;AAAA,QACrB,OAAO,cAAc;AAAA,QACrB;AAAA,QACA;AAAA,MACD;AAED,WAAK,eAAe,MAAM,IAAI,KAAK,eAAe,MAAM;AAExD,WAAK,eAAe,iBAAiB,IAAIC,MAAAA,kBAAkB,OAAO,YAAY,OAAO,WAAW;AAChG,WAAK,eAAe,iBAAiB,IAAIA,MAAAA,kBAAkB,OAAO,YAAY,OAAO,WAAW;AAEhG,YAAM,eAAeC,aAAY;AAEjC,WAAK,eAAe,iBAAiBC,MAAa,cAAC,MAAM,aAAa,QAAQ;AAE9E,WAAK,eAAe,eAAe,QAAQ,EAAE,QAAQ,KAAK,eAAe,eAAe;AACxF,WAAK,eAAe,eAAe,QAAQ,EAAE,QAAQ,KAAK,eAAe,eAAe;AAExF,WAAK,eAAe,eAAe,WAAW,EAAE,QAAQ;AACxD,WAAK,eAAe,eAAe,aAAa,EAAE,QAAQ;AAE1D,WAAK,eAAe,eAAe,OAAO,EAAE,QAAQ;AAEpD,WAAK,eAAe,eAAe,WAAW,EAAE,QAAQ;AAExD,WAAK,eAAe,eAAe,YAAY,EAAE,QAAQ;AAIzD,WAAK,eAAe,eAAe,OAAO,EAAE,QAAQ,KAAK;AACzD,WAAK,eAAe,eAAe,MAAM,EAAE,QAAQ,KAAK;AAExD,WAAK,eAAe,eAAe,cAAc,EAAE,QAAQ,OAAO;AAElE,WAAK,eAAe,eAAe,eAAe,EAAE,QAAQ,OAAO;AAEnE,WAAK,eAAe,gBAAgB,IAAIL,qBAAe;AAAA,QACrD,UAAU,KAAK,eAAe;AAAA,QAC9B,cAAc,aAAa;AAAA,QAC3B,gBAAgB,aAAa;AAAA,QAC7B,SAAS;AAAA,UACP,OAAO,KAAK,eAAe;AAAA,UAC3B,SAAS,KAAK,eAAe;AAAA,UAC7B,eAAe;AAAA,QAChB;AAAA,MACT,CAAO;AAED,WAAK,eAAe,OAAO,IAAIM,MAAI;AAAA,QACjC,IAAIC,MAAa,cAAC,OAAO,YAAY,OAAO,WAAW;AAAA,QACvD,KAAK,eAAe;AAAA,MACrB;AACD,WAAK,eAAe,KAAK,SAAS,IAAI;AACtC,WAAK,eAAe,MAAM,IAAI,KAAK,eAAe,IAAI;AAAA,IACvD;AAAA,EACF;AAAA,EAED,gBAAgB,OAAO,UAAU;AAC/B,QAAI,KAAK,eAAe,SAAS;AAC/B,YAAM,sBAAsB,SAAS,gBAAiB;AAEtD,eAAS,MAAO;AAIhB,YAAM,mBAAmB;AACzB,eAAS,gBAAgB,KAAK,eAAe,cAAc;AAC3D,eAAS,MAAO;AAChB,eAAS,OAAO,OAAO,IAAI;AAI3B,YAAM,mBAAmB,KAAK;AAC9B,eAAS,gBAAgB,KAAK,eAAe,cAAc;AAC3D,eAAS,MAAO;AAChB,eAAS,OAAO,OAAO,IAAI;AAI3B,eAAS,gBAAgB,IAAI;AAC7B,eAAS,OAAO,KAAK,eAAe,OAAO,KAAK,eAAe,MAAM;AAErE,eAAS,gBAAgB,mBAAmB;AAAA,IAC7C;AAAA,EACF;AACH;;"}