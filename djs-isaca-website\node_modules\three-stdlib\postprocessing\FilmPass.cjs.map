{"version": 3, "file": "FilmPass.cjs", "sources": ["../../src/postprocessing/FilmPass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport { IUniform, ShaderMaterial, UniformsUtils, WebGLRenderer, WebGLRenderTarget } from 'three'\nimport { FilmShader } from '../shaders/FilmShader'\n\nclass FilmPass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  public uniforms: Record<keyof typeof FilmShader['uniforms'], IUniform<any>>\n\n  constructor(noiseIntensity?: number, scanlinesIntensity?: number, scanlinesCount?: number, grayscale?: boolean) {\n    super()\n\n    if (FilmShader === undefined) console.error('THREE.FilmPass relies on FilmShader')\n\n    const shader = FilmShader\n\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n    })\n\n    if (grayscale !== undefined) this.uniforms.grayscale.value = grayscale\n    if (noiseIntensity !== undefined) this.uniforms.nIntensity.value = noiseIntensity\n    if (scanlinesIntensity !== undefined) this.uniforms.sIntensity.value = scanlinesIntensity\n    if (scanlinesCount !== undefined) this.uniforms.sCount.value = scanlinesCount\n\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    deltaTime: number,\n  ): void {\n    this.uniforms['tDiffuse'].value = readBuffer.texture\n    this.uniforms['time'].value += deltaTime\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n}\n\nexport { FilmPass }\n"], "names": ["Pass", "FilmShader", "UniformsUtils", "ShaderMaterial", "FullScreenQuad"], "mappings": ";;;;;;;;;;;AAIA,MAAM,iBAAiBA,KAAAA,KAAK;AAAA,EAM1B,YAAY,gBAAyB,oBAA6B,gBAAyB,WAAqB;AACxG;AAND;AACA;AAEA;AAKL,QAAIC,WAAAA,eAAe;AAAW,cAAQ,MAAM,qCAAqC;AAEjF,UAAM,SAASA,WAAAA;AAEf,SAAK,WAAWC,MAAA,cAAc,MAAM,OAAO,QAAQ;AAE9C,SAAA,WAAW,IAAIC,qBAAe;AAAA,MACjC,UAAU,KAAK;AAAA,MACf,cAAc,OAAO;AAAA,MACrB,gBAAgB,OAAO;AAAA,IAAA,CACxB;AAED,QAAI,cAAc;AAAgB,WAAA,SAAS,UAAU,QAAQ;AAC7D,QAAI,mBAAmB;AAAgB,WAAA,SAAS,WAAW,QAAQ;AACnE,QAAI,uBAAuB;AAAgB,WAAA,SAAS,WAAW,QAAQ;AACvE,QAAI,mBAAmB;AAAgB,WAAA,SAAS,OAAO,QAAQ;AAE/D,SAAK,SAAS,IAAIC,KAAe,eAAA,KAAK,QAAQ;AAAA,EAChD;AAAA,EAEO,OACL,UACA,aACA,YACA,WACM;AACN,SAAK,SAAS,UAAU,EAAE,QAAQ,WAAW;AACxC,SAAA,SAAS,MAAM,EAAE,SAAS;AAE/B,QAAI,KAAK,gBAAgB;AACvB,eAAS,gBAAgB,IAAI;AACxB,WAAA,OAAO,OAAO,QAAQ;AAAA,IAAA,OACtB;AACL,eAAS,gBAAgB,WAAW;AACpC,UAAI,KAAK;AAAO,iBAAS,MAAM;AAC1B,WAAA,OAAO,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACF;AACF;;"}