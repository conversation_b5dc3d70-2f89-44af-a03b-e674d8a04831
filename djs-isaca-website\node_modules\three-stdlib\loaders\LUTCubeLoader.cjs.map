{"version": 3, "file": "LUTCubeLoader.cjs", "sources": ["../../src/loaders/LUTCubeLoader.js"], "sourcesContent": ["// https://wwwimages2.adobe.com/content/dam/acom/en/products/speedgrade/cc/pdfs/cube-lut-specification-1.0.pdf\n\nimport { Loader, FileLoader, Vector3, DataTexture, UnsignedByteType, ClampToEdgeWrapping, LinearFilter } from 'three'\nimport { Data3DTexture } from '../_polyfill/Data3DTexture'\n\nexport class LUTCubeLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('text')\n    loader.load(\n      url,\n      (text) => {\n        try {\n          onLoad(this.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          this.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(str) {\n    // Remove empty lines and comments\n    str = str\n      .replace(/^#.*?(\\n|\\r)/gm, '')\n      .replace(/^\\s*?(\\n|\\r)/gm, '')\n      .trim()\n\n    let title = null\n    let size = null\n    const domainMin = new Vector3(0, 0, 0)\n    const domainMax = new Vector3(1, 1, 1)\n\n    const lines = str.split(/[\\n\\r]+/g)\n    let data = null\n\n    let currIndex = 0\n    for (let i = 0, l = lines.length; i < l; i++) {\n      const line = lines[i].trim()\n      const split = line.split(/\\s/g)\n\n      switch (split[0]) {\n        case 'TITLE':\n          title = line.substring(7, line.length - 1)\n          break\n        case 'LUT_3D_SIZE':\n          // TODO: A .CUBE LUT file specifies floating point values and could be represented with\n          // more precision than can be captured with Uint8Array.\n          const sizeToken = split[1]\n          size = parseFloat(sizeToken)\n          data = new Uint8Array(size * size * size * 4)\n          break\n        case 'DOMAIN_MIN':\n          domainMin.x = parseFloat(split[1])\n          domainMin.y = parseFloat(split[2])\n          domainMin.z = parseFloat(split[3])\n          break\n        case 'DOMAIN_MAX':\n          domainMax.x = parseFloat(split[1])\n          domainMax.y = parseFloat(split[2])\n          domainMax.z = parseFloat(split[3])\n          break\n        default:\n          const r = parseFloat(split[0])\n          const g = parseFloat(split[1])\n          const b = parseFloat(split[2])\n\n          if (r > 1.0 || r < 0.0 || g > 1.0 || g < 0.0 || b > 1.0 || b < 0.0) {\n            throw new Error('LUTCubeLoader : Non normalized values not supported.')\n          }\n\n          data[currIndex + 0] = r * 255\n          data[currIndex + 1] = g * 255\n          data[currIndex + 2] = b * 255\n          data[currIndex + 3] = 255\n          currIndex += 4\n      }\n    }\n\n    const texture = new DataTexture()\n    texture.image.data = data\n    texture.image.width = size\n    texture.image.height = size * size\n    texture.type = UnsignedByteType\n    texture.magFilter = LinearFilter\n    texture.minFilter = LinearFilter\n    texture.wrapS = ClampToEdgeWrapping\n    texture.wrapT = ClampToEdgeWrapping\n    texture.generateMipmaps = false\n    texture.needsUpdate = true\n\n    const texture3D = new Data3DTexture()\n    texture3D.image.data = data\n    texture3D.image.width = size\n    texture3D.image.height = size\n    texture3D.image.depth = size\n    texture3D.type = UnsignedByteType\n    texture3D.magFilter = LinearFilter\n    texture3D.minFilter = LinearFilter\n    texture3D.wrapS = ClampToEdgeWrapping\n    texture3D.wrapT = ClampToEdgeWrapping\n    texture3D.wrapR = ClampToEdgeWrapping\n    texture3D.generateMipmaps = false\n    texture3D.needsUpdate = true\n\n    return {\n      title,\n      size,\n      domainMin,\n      domainMax,\n      texture,\n      texture3D,\n    }\n  }\n}\n"], "names": ["Loader", "<PERSON><PERSON><PERSON><PERSON>", "Vector3", "DataTexture", "UnsignedByteType", "LinearFilter", "ClampToEdgeWrapping", "Data3DTexture"], "mappings": ";;;;AAKO,MAAM,sBAAsBA,MAAAA,OAAO;AAAA,EACxC,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,SAAS,IAAIC,iBAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,gBAAgB,MAAM;AAC7B,WAAO;AAAA,MACL;AAAA,MACA,CAAC,SAAS;AACR,YAAI;AACF,iBAAO,KAAK,MAAM,IAAI,CAAC;AAAA,QACxB,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,eAAK,QAAQ,UAAU,GAAG;AAAA,QAC3B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,KAAK;AAET,UAAM,IACH,QAAQ,kBAAkB,EAAE,EAC5B,QAAQ,kBAAkB,EAAE,EAC5B,KAAM;AAET,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,UAAM,YAAY,IAAIC,MAAAA,QAAQ,GAAG,GAAG,CAAC;AACrC,UAAM,YAAY,IAAIA,MAAAA,QAAQ,GAAG,GAAG,CAAC;AAErC,UAAM,QAAQ,IAAI,MAAM,UAAU;AAClC,QAAI,OAAO;AAEX,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,YAAM,OAAO,MAAM,CAAC,EAAE,KAAM;AAC5B,YAAM,QAAQ,KAAK,MAAM,KAAK;AAE9B,cAAQ,MAAM,CAAC,GAAC;AAAA,QACd,KAAK;AACH,kBAAQ,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AACzC;AAAA,QACF,KAAK;AAGH,gBAAM,YAAY,MAAM,CAAC;AACzB,iBAAO,WAAW,SAAS;AAC3B,iBAAO,IAAI,WAAW,OAAO,OAAO,OAAO,CAAC;AAC5C;AAAA,QACF,KAAK;AACH,oBAAU,IAAI,WAAW,MAAM,CAAC,CAAC;AACjC,oBAAU,IAAI,WAAW,MAAM,CAAC,CAAC;AACjC,oBAAU,IAAI,WAAW,MAAM,CAAC,CAAC;AACjC;AAAA,QACF,KAAK;AACH,oBAAU,IAAI,WAAW,MAAM,CAAC,CAAC;AACjC,oBAAU,IAAI,WAAW,MAAM,CAAC,CAAC;AACjC,oBAAU,IAAI,WAAW,MAAM,CAAC,CAAC;AACjC;AAAA,QACF;AACE,gBAAM,IAAI,WAAW,MAAM,CAAC,CAAC;AAC7B,gBAAM,IAAI,WAAW,MAAM,CAAC,CAAC;AAC7B,gBAAM,IAAI,WAAW,MAAM,CAAC,CAAC;AAE7B,cAAI,IAAI,KAAO,IAAI,KAAO,IAAI,KAAO,IAAI,KAAO,IAAI,KAAO,IAAI,GAAK;AAClE,kBAAM,IAAI,MAAM,sDAAsD;AAAA,UACvE;AAED,eAAK,YAAY,CAAC,IAAI,IAAI;AAC1B,eAAK,YAAY,CAAC,IAAI,IAAI;AAC1B,eAAK,YAAY,CAAC,IAAI,IAAI;AAC1B,eAAK,YAAY,CAAC,IAAI;AACtB,uBAAa;AAAA,MAChB;AAAA,IACF;AAED,UAAM,UAAU,IAAIC,kBAAa;AACjC,YAAQ,MAAM,OAAO;AACrB,YAAQ,MAAM,QAAQ;AACtB,YAAQ,MAAM,SAAS,OAAO;AAC9B,YAAQ,OAAOC,MAAgB;AAC/B,YAAQ,YAAYC,MAAY;AAChC,YAAQ,YAAYA,MAAY;AAChC,YAAQ,QAAQC,MAAmB;AACnC,YAAQ,QAAQA,MAAmB;AACnC,YAAQ,kBAAkB;AAC1B,YAAQ,cAAc;AAEtB,UAAM,YAAY,IAAIC,4BAAe;AACrC,cAAU,MAAM,OAAO;AACvB,cAAU,MAAM,QAAQ;AACxB,cAAU,MAAM,SAAS;AACzB,cAAU,MAAM,QAAQ;AACxB,cAAU,OAAOH,MAAgB;AACjC,cAAU,YAAYC,MAAY;AAClC,cAAU,YAAYA,MAAY;AAClC,cAAU,QAAQC,MAAmB;AACrC,cAAU,QAAQA,MAAmB;AACrC,cAAU,QAAQA,MAAmB;AACrC,cAAU,kBAAkB;AAC5B,cAAU,cAAc;AAExB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACF;AACH;;"}