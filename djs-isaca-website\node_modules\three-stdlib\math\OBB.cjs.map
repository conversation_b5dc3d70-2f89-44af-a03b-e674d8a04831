{"version": 3, "file": "OBB.cjs", "sources": ["../../src/math/OBB.js"], "sourcesContent": ["import { Box3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>4, <PERSON>3, <PERSON>, Vector3 } from 'three'\n\n// module scope helper variables\n\nconst a = {\n  c: null, // center\n  u: [/* @__PURE__ */ new Vector3(), /* @__PURE__ */ new Vector3(), /* @__PURE__ */ new Vector3()], // basis vectors\n  e: [], // half width\n}\n\nconst b = {\n  c: null, // center\n  u: [/* @__PURE__ */ new Vector3(), /* @__PURE__ */ new Vector3(), /* @__PURE__ */ new Vector3()], // basis vectors\n  e: [], // half width\n}\n\nconst R = [[], [], []]\nconst AbsR = [[], [], []]\nconst t = []\n\nconst xAxis = /* @__PURE__ */ new Vector3()\nconst yAxis = /* @__PURE__ */ new Vector3()\nconst zAxis = /* @__PURE__ */ new Vector3()\nconst v1 = /* @__PURE__ */ new Vector3()\nconst size = /* @__PURE__ */ new Vector3()\nconst closestPoint = /* @__PURE__ */ new Vector3()\nconst rotationMatrix = /* @__PURE__ */ new Matrix3()\nconst aabb = /* @__PURE__ */ new Box3()\nconst matrix = /* @__PURE__ */ new Matrix4()\nconst inverse = /* @__PURE__ */ new Matrix4()\nconst localRay = /* @__PURE__ */ new Ray()\n\n// OBB\n\nclass OBB {\n  constructor(center = new Vector3(), halfSize = new Vector3(), rotation = new Matrix3()) {\n    this.center = center\n    this.halfSize = halfSize\n    this.rotation = rotation\n  }\n\n  set(center, halfSize, rotation) {\n    this.center = center\n    this.halfSize = halfSize\n    this.rotation = rotation\n\n    return this\n  }\n\n  copy(obb) {\n    this.center.copy(obb.center)\n    this.halfSize.copy(obb.halfSize)\n    this.rotation.copy(obb.rotation)\n\n    return this\n  }\n\n  clone() {\n    return new this.constructor().copy(this)\n  }\n\n  getSize(result) {\n    return result.copy(this.halfSize).multiplyScalar(2)\n  }\n\n  /**\n   * Reference: Closest Point on OBB to Point in Real-Time Collision Detection\n   * by Christer Ericson (chapter 5.1.4)\n   */\n  clampPoint(point, result) {\n    const halfSize = this.halfSize\n\n    v1.subVectors(point, this.center)\n    this.rotation.extractBasis(xAxis, yAxis, zAxis)\n\n    // start at the center position of the OBB\n\n    result.copy(this.center)\n\n    // project the target onto the OBB axes and walk towards that point\n\n    const x = MathUtils.clamp(v1.dot(xAxis), -halfSize.x, halfSize.x)\n    result.add(xAxis.multiplyScalar(x))\n\n    const y = MathUtils.clamp(v1.dot(yAxis), -halfSize.y, halfSize.y)\n    result.add(yAxis.multiplyScalar(y))\n\n    const z = MathUtils.clamp(v1.dot(zAxis), -halfSize.z, halfSize.z)\n    result.add(zAxis.multiplyScalar(z))\n\n    return result\n  }\n\n  containsPoint(point) {\n    v1.subVectors(point, this.center)\n    this.rotation.extractBasis(xAxis, yAxis, zAxis)\n\n    // project v1 onto each axis and check if these points lie inside the OBB\n\n    return (\n      Math.abs(v1.dot(xAxis)) <= this.halfSize.x &&\n      Math.abs(v1.dot(yAxis)) <= this.halfSize.y &&\n      Math.abs(v1.dot(zAxis)) <= this.halfSize.z\n    )\n  }\n\n  intersectsBox3(box3) {\n    return this.intersectsOBB(obb.fromBox3(box3))\n  }\n\n  intersectsSphere(sphere) {\n    // find the point on the OBB closest to the sphere center\n\n    this.clampPoint(sphere.center, closestPoint)\n\n    // if that point is inside the sphere, the OBB and sphere intersect\n\n    return closestPoint.distanceToSquared(sphere.center) <= sphere.radius * sphere.radius\n  }\n\n  /**\n   * Reference: OBB-OBB Intersection in Real-Time Collision Detection\n   * by Christer Ericson (chapter 4.4.1)\n   *\n   */\n  intersectsOBB(obb, epsilon = Number.EPSILON) {\n    // prepare data structures (the code uses the same nomenclature like the reference)\n\n    a.c = this.center\n    a.e[0] = this.halfSize.x\n    a.e[1] = this.halfSize.y\n    a.e[2] = this.halfSize.z\n    this.rotation.extractBasis(a.u[0], a.u[1], a.u[2])\n\n    b.c = obb.center\n    b.e[0] = obb.halfSize.x\n    b.e[1] = obb.halfSize.y\n    b.e[2] = obb.halfSize.z\n    obb.rotation.extractBasis(b.u[0], b.u[1], b.u[2])\n\n    // compute rotation matrix expressing b in a's coordinate frame\n\n    for (let i = 0; i < 3; i++) {\n      for (let j = 0; j < 3; j++) {\n        R[i][j] = a.u[i].dot(b.u[j])\n      }\n    }\n\n    // compute translation vector\n\n    v1.subVectors(b.c, a.c)\n\n    // bring translation into a's coordinate frame\n\n    t[0] = v1.dot(a.u[0])\n    t[1] = v1.dot(a.u[1])\n    t[2] = v1.dot(a.u[2])\n\n    // compute common subexpressions. Add in an epsilon term to\n    // counteract arithmetic errors when two edges are parallel and\n    // their cross product is (near) null\n\n    for (let i = 0; i < 3; i++) {\n      for (let j = 0; j < 3; j++) {\n        AbsR[i][j] = Math.abs(R[i][j]) + epsilon\n      }\n    }\n\n    let ra, rb\n\n    // test axes L = A0, L = A1, L = A2\n\n    for (let i = 0; i < 3; i++) {\n      ra = a.e[i]\n      rb = b.e[0] * AbsR[i][0] + b.e[1] * AbsR[i][1] + b.e[2] * AbsR[i][2]\n      if (Math.abs(t[i]) > ra + rb) return false\n    }\n\n    // test axes L = B0, L = B1, L = B2\n\n    for (let i = 0; i < 3; i++) {\n      ra = a.e[0] * AbsR[0][i] + a.e[1] * AbsR[1][i] + a.e[2] * AbsR[2][i]\n      rb = b.e[i]\n      if (Math.abs(t[0] * R[0][i] + t[1] * R[1][i] + t[2] * R[2][i]) > ra + rb) return false\n    }\n\n    // test axis L = A0 x B0\n\n    ra = a.e[1] * AbsR[2][0] + a.e[2] * AbsR[1][0]\n    rb = b.e[1] * AbsR[0][2] + b.e[2] * AbsR[0][1]\n    if (Math.abs(t[2] * R[1][0] - t[1] * R[2][0]) > ra + rb) return false\n\n    // test axis L = A0 x B1\n\n    ra = a.e[1] * AbsR[2][1] + a.e[2] * AbsR[1][1]\n    rb = b.e[0] * AbsR[0][2] + b.e[2] * AbsR[0][0]\n    if (Math.abs(t[2] * R[1][1] - t[1] * R[2][1]) > ra + rb) return false\n\n    // test axis L = A0 x B2\n\n    ra = a.e[1] * AbsR[2][2] + a.e[2] * AbsR[1][2]\n    rb = b.e[0] * AbsR[0][1] + b.e[1] * AbsR[0][0]\n    if (Math.abs(t[2] * R[1][2] - t[1] * R[2][2]) > ra + rb) return false\n\n    // test axis L = A1 x B0\n\n    ra = a.e[0] * AbsR[2][0] + a.e[2] * AbsR[0][0]\n    rb = b.e[1] * AbsR[1][2] + b.e[2] * AbsR[1][1]\n    if (Math.abs(t[0] * R[2][0] - t[2] * R[0][0]) > ra + rb) return false\n\n    // test axis L = A1 x B1\n\n    ra = a.e[0] * AbsR[2][1] + a.e[2] * AbsR[0][1]\n    rb = b.e[0] * AbsR[1][2] + b.e[2] * AbsR[1][0]\n    if (Math.abs(t[0] * R[2][1] - t[2] * R[0][1]) > ra + rb) return false\n\n    // test axis L = A1 x B2\n\n    ra = a.e[0] * AbsR[2][2] + a.e[2] * AbsR[0][2]\n    rb = b.e[0] * AbsR[1][1] + b.e[1] * AbsR[1][0]\n    if (Math.abs(t[0] * R[2][2] - t[2] * R[0][2]) > ra + rb) return false\n\n    // test axis L = A2 x B0\n\n    ra = a.e[0] * AbsR[1][0] + a.e[1] * AbsR[0][0]\n    rb = b.e[1] * AbsR[2][2] + b.e[2] * AbsR[2][1]\n    if (Math.abs(t[1] * R[0][0] - t[0] * R[1][0]) > ra + rb) return false\n\n    // test axis L = A2 x B1\n\n    ra = a.e[0] * AbsR[1][1] + a.e[1] * AbsR[0][1]\n    rb = b.e[0] * AbsR[2][2] + b.e[2] * AbsR[2][0]\n    if (Math.abs(t[1] * R[0][1] - t[0] * R[1][1]) > ra + rb) return false\n\n    // test axis L = A2 x B2\n\n    ra = a.e[0] * AbsR[1][2] + a.e[1] * AbsR[0][2]\n    rb = b.e[0] * AbsR[2][1] + b.e[1] * AbsR[2][0]\n    if (Math.abs(t[1] * R[0][2] - t[0] * R[1][2]) > ra + rb) return false\n\n    // since no separating axis is found, the OBBs must be intersecting\n\n    return true\n  }\n\n  /**\n   * Reference: Testing Box Against Plane in Real-Time Collision Detection\n   * by Christer Ericson (chapter 5.2.3)\n   */\n  intersectsPlane(plane) {\n    this.rotation.extractBasis(xAxis, yAxis, zAxis)\n\n    // compute the projection interval radius of this OBB onto L(t) = this->center + t * p.normal;\n\n    const r =\n      this.halfSize.x * Math.abs(plane.normal.dot(xAxis)) +\n      this.halfSize.y * Math.abs(plane.normal.dot(yAxis)) +\n      this.halfSize.z * Math.abs(plane.normal.dot(zAxis))\n\n    // compute distance of the OBB's center from the plane\n\n    const d = plane.normal.dot(this.center) - plane.constant\n\n    // Intersection occurs when distance d falls within [-r,+r] interval\n\n    return Math.abs(d) <= r\n  }\n\n  /**\n   * Performs a ray/OBB intersection test and stores the intersection point\n   * to the given 3D vector. If no intersection is detected, *null* is returned.\n   */\n  intersectRay(ray, result) {\n    // the idea is to perform the intersection test in the local space\n    // of the OBB.\n\n    this.getSize(size)\n    aabb.setFromCenterAndSize(v1.set(0, 0, 0), size)\n\n    // create a 4x4 transformation matrix\n\n    matrix.setFromMatrix3(this.rotation)\n    matrix.setPosition(this.center)\n\n    // transform ray to the local space of the OBB\n\n    inverse.copy(matrix).invert()\n    localRay.copy(ray).applyMatrix4(inverse)\n\n    // perform ray <-> AABB intersection test\n\n    if (localRay.intersectBox(aabb, result)) {\n      // transform the intersection point back to world space\n\n      return result.applyMatrix4(matrix)\n    } else {\n      return null\n    }\n  }\n\n  /**\n   * Performs a ray/OBB intersection test. Returns either true or false if\n   * there is a intersection or not.\n   */\n  intersectsRay(ray) {\n    return this.intersectRay(ray, v1) !== null\n  }\n\n  fromBox3(box3) {\n    box3.getCenter(this.center)\n\n    box3.getSize(this.halfSize).multiplyScalar(0.5)\n\n    this.rotation.identity()\n\n    return this\n  }\n\n  equals(obb) {\n    return obb.center.equals(this.center) && obb.halfSize.equals(this.halfSize) && obb.rotation.equals(this.rotation)\n  }\n\n  applyMatrix4(matrix) {\n    const e = matrix.elements\n\n    let sx = v1.set(e[0], e[1], e[2]).length()\n    const sy = v1.set(e[4], e[5], e[6]).length()\n    const sz = v1.set(e[8], e[9], e[10]).length()\n\n    const det = matrix.determinant()\n    if (det < 0) sx = -sx\n\n    rotationMatrix.setFromMatrix4(matrix)\n\n    const invSX = 1 / sx\n    const invSY = 1 / sy\n    const invSZ = 1 / sz\n\n    rotationMatrix.elements[0] *= invSX\n    rotationMatrix.elements[1] *= invSX\n    rotationMatrix.elements[2] *= invSX\n\n    rotationMatrix.elements[3] *= invSY\n    rotationMatrix.elements[4] *= invSY\n    rotationMatrix.elements[5] *= invSY\n\n    rotationMatrix.elements[6] *= invSZ\n    rotationMatrix.elements[7] *= invSZ\n    rotationMatrix.elements[8] *= invSZ\n\n    this.rotation.multiply(rotationMatrix)\n\n    this.halfSize.x *= sx\n    this.halfSize.y *= sy\n    this.halfSize.z *= sz\n\n    v1.setFromMatrixPosition(matrix)\n    this.center.add(v1)\n\n    return this\n  }\n}\n\nconst obb = /* @__PURE__ */ new OBB()\n\nexport { OBB }\n"], "names": ["Vector3", "Matrix3", "Box3", "Matrix4", "<PERSON>", "obb", "MathUtils", "matrix"], "mappings": ";;;AAIA,MAAM,IAAI;AAAA,EACR,GAAG;AAAA;AAAA,EACH,GAAG,CAAiB,oBAAIA,cAAS,GAAkB,oBAAIA,MAAAA,QAAS,GAAkB,oBAAIA,MAAAA,SAAS;AAAA;AAAA,EAC/F,GAAG,CAAE;AAAA;AACP;AAEA,MAAM,IAAI;AAAA,EACR,GAAG;AAAA;AAAA,EACH,GAAG,CAAiB,oBAAIA,cAAS,GAAkB,oBAAIA,MAAAA,QAAS,GAAkB,oBAAIA,MAAAA,SAAS;AAAA;AAAA,EAC/F,GAAG,CAAE;AAAA;AACP;AAEA,MAAM,IAAI,CAAC,CAAA,GAAI,CAAE,GAAE,EAAE;AACrB,MAAM,OAAO,CAAC,CAAA,GAAI,CAAE,GAAE,EAAE;AACxB,MAAM,IAAI,CAAE;AAEZ,MAAM,QAAwB,oBAAIA,MAAAA,QAAS;AAC3C,MAAM,QAAwB,oBAAIA,MAAAA,QAAS;AAC3C,MAAM,QAAwB,oBAAIA,MAAAA,QAAS;AAC3C,MAAM,KAAqB,oBAAIA,MAAAA,QAAS;AACxC,MAAM,OAAuB,oBAAIA,MAAAA,QAAS;AAC1C,MAAM,eAA+B,oBAAIA,MAAAA,QAAS;AAClD,MAAM,iBAAiC,oBAAIC,MAAAA,QAAS;AACpD,MAAM,OAAuB,oBAAIC,MAAAA,KAAM;AACvC,MAAM,SAAyB,oBAAIC,MAAAA,QAAS;AAC5C,MAAM,UAA0B,oBAAIA,MAAAA,QAAS;AAC7C,MAAM,WAA2B,oBAAIC,MAAAA,IAAK;AAI1C,MAAM,IAAI;AAAA,EACR,YAAY,SAAS,IAAIJ,MAAAA,WAAW,WAAW,IAAIA,MAAAA,WAAW,WAAW,IAAIC,MAAAA,WAAW;AACtF,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EACjB;AAAA,EAED,IAAI,QAAQ,UAAU,UAAU;AAC9B,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW;AAEhB,WAAO;AAAA,EACR;AAAA,EAED,KAAKI,MAAK;AACR,SAAK,OAAO,KAAKA,KAAI,MAAM;AAC3B,SAAK,SAAS,KAAKA,KAAI,QAAQ;AAC/B,SAAK,SAAS,KAAKA,KAAI,QAAQ;AAE/B,WAAO;AAAA,EACR;AAAA,EAED,QAAQ;AACN,WAAO,IAAI,KAAK,cAAc,KAAK,IAAI;AAAA,EACxC;AAAA,EAED,QAAQ,QAAQ;AACd,WAAO,OAAO,KAAK,KAAK,QAAQ,EAAE,eAAe,CAAC;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW,OAAO,QAAQ;AACxB,UAAM,WAAW,KAAK;AAEtB,OAAG,WAAW,OAAO,KAAK,MAAM;AAChC,SAAK,SAAS,aAAa,OAAO,OAAO,KAAK;AAI9C,WAAO,KAAK,KAAK,MAAM;AAIvB,UAAM,IAAIC,MAAAA,UAAU,MAAM,GAAG,IAAI,KAAK,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;AAChE,WAAO,IAAI,MAAM,eAAe,CAAC,CAAC;AAElC,UAAM,IAAIA,MAAAA,UAAU,MAAM,GAAG,IAAI,KAAK,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;AAChE,WAAO,IAAI,MAAM,eAAe,CAAC,CAAC;AAElC,UAAM,IAAIA,MAAAA,UAAU,MAAM,GAAG,IAAI,KAAK,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;AAChE,WAAO,IAAI,MAAM,eAAe,CAAC,CAAC;AAElC,WAAO;AAAA,EACR;AAAA,EAED,cAAc,OAAO;AACnB,OAAG,WAAW,OAAO,KAAK,MAAM;AAChC,SAAK,SAAS,aAAa,OAAO,OAAO,KAAK;AAI9C,WACE,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,KACzC,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,KACzC,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS;AAAA,EAE5C;AAAA,EAED,eAAe,MAAM;AACnB,WAAO,KAAK,cAAc,IAAI,SAAS,IAAI,CAAC;AAAA,EAC7C;AAAA,EAED,iBAAiB,QAAQ;AAGvB,SAAK,WAAW,OAAO,QAAQ,YAAY;AAI3C,WAAO,aAAa,kBAAkB,OAAO,MAAM,KAAK,OAAO,SAAS,OAAO;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,cAAcD,MAAK,UAAU,OAAO,SAAS;AAG3C,MAAE,IAAI,KAAK;AACX,MAAE,EAAE,CAAC,IAAI,KAAK,SAAS;AACvB,MAAE,EAAE,CAAC,IAAI,KAAK,SAAS;AACvB,MAAE,EAAE,CAAC,IAAI,KAAK,SAAS;AACvB,SAAK,SAAS,aAAa,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAEjD,MAAE,IAAIA,KAAI;AACV,MAAE,EAAE,CAAC,IAAIA,KAAI,SAAS;AACtB,MAAE,EAAE,CAAC,IAAIA,KAAI,SAAS;AACtB,MAAE,EAAE,CAAC,IAAIA,KAAI,SAAS;AACtB,IAAAA,KAAI,SAAS,aAAa,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAIhD,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,MAC5B;AAAA,IACF;AAID,OAAG,WAAW,EAAE,GAAG,EAAE,CAAC;AAItB,MAAE,CAAC,IAAI,GAAG,IAAI,EAAE,EAAE,CAAC,CAAC;AACpB,MAAE,CAAC,IAAI,GAAG,IAAI,EAAE,EAAE,CAAC,CAAC;AACpB,MAAE,CAAC,IAAI,GAAG,IAAI,EAAE,EAAE,CAAC,CAAC;AAMpB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,aAAK,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI;AAAA,MAClC;AAAA,IACF;AAED,QAAI,IAAI;AAIR,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,EAAE,EAAE,CAAC;AACV,WAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AACnE,UAAI,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,eAAO;AAAA,IACtC;AAID,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AACnE,WAAK,EAAE,EAAE,CAAC;AACV,UAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,eAAO;AAAA,IAClF;AAID,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,QAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,aAAO;AAIhE,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,QAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,aAAO;AAIhE,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,QAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,aAAO;AAIhE,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,QAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,aAAO;AAIhE,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,QAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,aAAO;AAIhE,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,QAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,aAAO;AAIhE,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,QAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,aAAO;AAIhE,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,QAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,aAAO;AAIhE,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,SAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7C,QAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK;AAAI,aAAO;AAIhE,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,gBAAgB,OAAO;AACrB,SAAK,SAAS,aAAa,OAAO,OAAO,KAAK;AAI9C,UAAM,IACJ,KAAK,SAAS,IAAI,KAAK,IAAI,MAAM,OAAO,IAAI,KAAK,CAAC,IAClD,KAAK,SAAS,IAAI,KAAK,IAAI,MAAM,OAAO,IAAI,KAAK,CAAC,IAClD,KAAK,SAAS,IAAI,KAAK,IAAI,MAAM,OAAO,IAAI,KAAK,CAAC;AAIpD,UAAM,IAAI,MAAM,OAAO,IAAI,KAAK,MAAM,IAAI,MAAM;AAIhD,WAAO,KAAK,IAAI,CAAC,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,aAAa,KAAK,QAAQ;AAIxB,SAAK,QAAQ,IAAI;AACjB,SAAK,qBAAqB,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI;AAI/C,WAAO,eAAe,KAAK,QAAQ;AACnC,WAAO,YAAY,KAAK,MAAM;AAI9B,YAAQ,KAAK,MAAM,EAAE,OAAQ;AAC7B,aAAS,KAAK,GAAG,EAAE,aAAa,OAAO;AAIvC,QAAI,SAAS,aAAa,MAAM,MAAM,GAAG;AAGvC,aAAO,OAAO,aAAa,MAAM;AAAA,IACvC,OAAW;AACL,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,cAAc,KAAK;AACjB,WAAO,KAAK,aAAa,KAAK,EAAE,MAAM;AAAA,EACvC;AAAA,EAED,SAAS,MAAM;AACb,SAAK,UAAU,KAAK,MAAM;AAE1B,SAAK,QAAQ,KAAK,QAAQ,EAAE,eAAe,GAAG;AAE9C,SAAK,SAAS,SAAU;AAExB,WAAO;AAAA,EACR;AAAA,EAED,OAAOA,MAAK;AACV,WAAOA,KAAI,OAAO,OAAO,KAAK,MAAM,KAAKA,KAAI,SAAS,OAAO,KAAK,QAAQ,KAAKA,KAAI,SAAS,OAAO,KAAK,QAAQ;AAAA,EACjH;AAAA,EAED,aAAaE,SAAQ;AACnB,UAAM,IAAIA,QAAO;AAEjB,QAAI,KAAK,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,OAAQ;AAC1C,UAAM,KAAK,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,OAAQ;AAC5C,UAAM,KAAK,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,OAAQ;AAE7C,UAAM,MAAMA,QAAO,YAAa;AAChC,QAAI,MAAM;AAAG,WAAK,CAAC;AAEnB,mBAAe,eAAeA,OAAM;AAEpC,UAAM,QAAQ,IAAI;AAClB,UAAM,QAAQ,IAAI;AAClB,UAAM,QAAQ,IAAI;AAElB,mBAAe,SAAS,CAAC,KAAK;AAC9B,mBAAe,SAAS,CAAC,KAAK;AAC9B,mBAAe,SAAS,CAAC,KAAK;AAE9B,mBAAe,SAAS,CAAC,KAAK;AAC9B,mBAAe,SAAS,CAAC,KAAK;AAC9B,mBAAe,SAAS,CAAC,KAAK;AAE9B,mBAAe,SAAS,CAAC,KAAK;AAC9B,mBAAe,SAAS,CAAC,KAAK;AAC9B,mBAAe,SAAS,CAAC,KAAK;AAE9B,SAAK,SAAS,SAAS,cAAc;AAErC,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,KAAK;AAEnB,OAAG,sBAAsBA,OAAM;AAC/B,SAAK,OAAO,IAAI,EAAE;AAElB,WAAO;AAAA,EACR;AACH;AAEA,MAAM,MAAsB,oBAAI,IAAG;;"}