{"version": 3, "file": "MD2Loader.js", "sources": ["../../src/loaders/MD2Loader.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ector3 } from 'three'\n\nconst _normalData = [\n  [-0.525731, 0.0, 0.850651],\n  [-0.442863, 0.238856, 0.864188],\n  [-0.295242, 0.0, 0.955423],\n  [-0.309017, 0.5, 0.809017],\n  [-0.16246, 0.262866, 0.951056],\n  [0.0, 0.0, 1.0],\n  [0.0, 0.850651, 0.525731],\n  [-0.147621, 0.716567, 0.681718],\n  [0.147621, 0.716567, 0.681718],\n  [0.0, 0.525731, 0.850651],\n  [0.309017, 0.5, 0.809017],\n  [0.525731, 0.0, 0.850651],\n  [0.295242, 0.0, 0.955423],\n  [0.442863, 0.238856, 0.864188],\n  [0.16246, 0.262866, 0.951056],\n  [-0.681718, 0.147621, 0.716567],\n  [-0.809017, 0.309017, 0.5],\n  [-0.587785, 0.425325, 0.688191],\n  [-0.850651, 0.525731, 0.0],\n  [-0.864188, 0.442863, 0.238856],\n  [-0.716567, 0.681718, 0.147621],\n  [-0.688191, 0.587785, 0.425325],\n  [-0.5, 0.809017, 0.309017],\n  [-0.238856, 0.864188, 0.442863],\n  [-0.425325, 0.688191, 0.587785],\n  [-0.716567, 0.681718, -0.147621],\n  [-0.5, 0.809017, -0.309017],\n  [-0.525731, 0.850651, 0.0],\n  [0.0, 0.850651, -0.525731],\n  [-0.238856, 0.864188, -0.442863],\n  [0.0, 0.955423, -0.295242],\n  [-0.262866, 0.951056, -0.16246],\n  [0.0, 1.0, 0.0],\n  [0.0, 0.955423, 0.295242],\n  [-0.262866, 0.951056, 0.16246],\n  [0.238856, 0.864188, 0.442863],\n  [0.262866, 0.951056, 0.16246],\n  [0.5, 0.809017, 0.309017],\n  [0.238856, 0.864188, -0.442863],\n  [0.262866, 0.951056, -0.16246],\n  [0.5, 0.809017, -0.309017],\n  [0.850651, 0.525731, 0.0],\n  [0.716567, 0.681718, 0.147621],\n  [0.716567, 0.681718, -0.147621],\n  [0.525731, 0.850651, 0.0],\n  [0.425325, 0.688191, 0.587785],\n  [0.864188, 0.442863, 0.238856],\n  [0.688191, 0.587785, 0.425325],\n  [0.809017, 0.309017, 0.5],\n  [0.681718, 0.147621, 0.716567],\n  [0.587785, 0.425325, 0.688191],\n  [0.955423, 0.295242, 0.0],\n  [1.0, 0.0, 0.0],\n  [0.951056, 0.16246, 0.262866],\n  [0.850651, -0.525731, 0.0],\n  [0.955423, -0.295242, 0.0],\n  [0.864188, -0.442863, 0.238856],\n  [0.951056, -0.16246, 0.262866],\n  [0.809017, -0.309017, 0.5],\n  [0.681718, -0.147621, 0.716567],\n  [0.850651, 0.0, 0.525731],\n  [0.864188, 0.442863, -0.238856],\n  [0.809017, 0.309017, -0.5],\n  [0.951056, 0.16246, -0.262866],\n  [0.525731, 0.0, -0.850651],\n  [0.681718, 0.147621, -0.716567],\n  [0.681718, -0.147621, -0.716567],\n  [0.850651, 0.0, -0.525731],\n  [0.809017, -0.309017, -0.5],\n  [0.864188, -0.442863, -0.238856],\n  [0.951056, -0.16246, -0.262866],\n  [0.147621, 0.716567, -0.681718],\n  [0.309017, 0.5, -0.809017],\n  [0.425325, 0.688191, -0.587785],\n  [0.442863, 0.238856, -0.864188],\n  [0.587785, 0.425325, -0.688191],\n  [0.688191, 0.587785, -0.425325],\n  [-0.147621, 0.716567, -0.681718],\n  [-0.309017, 0.5, -0.809017],\n  [0.0, 0.525731, -0.850651],\n  [-0.525731, 0.0, -0.850651],\n  [-0.442863, 0.238856, -0.864188],\n  [-0.295242, 0.0, -0.955423],\n  [-0.16246, 0.262866, -0.951056],\n  [0.0, 0.0, -1.0],\n  [0.295242, 0.0, -0.955423],\n  [0.16246, 0.262866, -0.951056],\n  [-0.442863, -0.238856, -0.864188],\n  [-0.309017, -0.5, -0.809017],\n  [-0.16246, -0.262866, -0.951056],\n  [0.0, -0.850651, -0.525731],\n  [-0.147621, -0.716567, -0.681718],\n  [0.147621, -0.716567, -0.681718],\n  [0.0, -0.525731, -0.850651],\n  [0.309017, -0.5, -0.809017],\n  [0.442863, -0.238856, -0.864188],\n  [0.16246, -0.262866, -0.951056],\n  [0.238856, -0.864188, -0.442863],\n  [0.5, -0.809017, -0.309017],\n  [0.425325, -0.688191, -0.587785],\n  [0.716567, -0.681718, -0.147621],\n  [0.688191, -0.587785, -0.425325],\n  [0.587785, -0.425325, -0.688191],\n  [0.0, -0.955423, -0.295242],\n  [0.0, -1.0, 0.0],\n  [0.262866, -0.951056, -0.16246],\n  [0.0, -0.850651, 0.525731],\n  [0.0, -0.955423, 0.295242],\n  [0.238856, -0.864188, 0.442863],\n  [0.262866, -0.951056, 0.16246],\n  [0.5, -0.809017, 0.309017],\n  [0.716567, -0.681718, 0.147621],\n  [0.525731, -0.850651, 0.0],\n  [-0.238856, -0.864188, -0.442863],\n  [-0.5, -0.809017, -0.309017],\n  [-0.262866, -0.951056, -0.16246],\n  [-0.850651, -0.525731, 0.0],\n  [-0.716567, -0.681718, -0.147621],\n  [-0.716567, -0.681718, 0.147621],\n  [-0.525731, -0.850651, 0.0],\n  [-0.5, -0.809017, 0.309017],\n  [-0.238856, -0.864188, 0.442863],\n  [-0.262866, -0.951056, 0.16246],\n  [-0.864188, -0.442863, 0.238856],\n  [-0.809017, -0.309017, 0.5],\n  [-0.688191, -0.587785, 0.425325],\n  [-0.681718, -0.147621, 0.716567],\n  [-0.442863, -0.238856, 0.864188],\n  [-0.587785, -0.425325, 0.688191],\n  [-0.309017, -0.5, 0.809017],\n  [-0.147621, -0.716567, 0.681718],\n  [-0.425325, -0.688191, 0.587785],\n  [-0.16246, -0.262866, 0.951056],\n  [0.442863, -0.238856, 0.864188],\n  [0.16246, -0.262866, 0.951056],\n  [0.309017, -0.5, 0.809017],\n  [0.147621, -0.716567, 0.681718],\n  [0.0, -0.525731, 0.850651],\n  [0.425325, -0.688191, 0.587785],\n  [0.587785, -0.425325, 0.688191],\n  [0.688191, -0.587785, 0.425325],\n  [-0.955423, 0.295242, 0.0],\n  [-0.951056, 0.16246, 0.262866],\n  [-1.0, 0.0, 0.0],\n  [-0.850651, 0.0, 0.525731],\n  [-0.955423, -0.295242, 0.0],\n  [-0.951056, -0.16246, 0.262866],\n  [-0.864188, 0.442863, -0.238856],\n  [-0.951056, 0.16246, -0.262866],\n  [-0.809017, 0.309017, -0.5],\n  [-0.864188, -0.442863, -0.238856],\n  [-0.951056, -0.16246, -0.262866],\n  [-0.809017, -0.309017, -0.5],\n  [-0.681718, 0.147621, -0.716567],\n  [-0.681718, -0.147621, -0.716567],\n  [-0.850651, 0.0, -0.525731],\n  [-0.688191, 0.587785, -0.425325],\n  [-0.587785, 0.425325, -0.688191],\n  [-0.425325, 0.688191, -0.587785],\n  [-0.425325, -0.688191, -0.587785],\n  [-0.587785, -0.425325, -0.688191],\n  [-0.688191, -0.587785, -0.425325],\n]\n\nclass MD2Loader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(buffer) {\n    const data = new DataView(buffer)\n\n    // http://tfc.duke.free.fr/coding/md2-specs-en.html\n\n    const header = {}\n    const headerNames = [\n      'ident',\n      'version',\n      'skinwidth',\n      'skinheight',\n      'framesize',\n      'num_skins',\n      'num_vertices',\n      'num_st',\n      'num_tris',\n      'num_glcmds',\n      'num_frames',\n      'offset_skins',\n      'offset_st',\n      'offset_tris',\n      'offset_frames',\n      'offset_glcmds',\n      'offset_end',\n    ]\n\n    for (let i = 0; i < headerNames.length; i++) {\n      header[headerNames[i]] = data.getInt32(i * 4, true)\n    }\n\n    if (header.ident !== 844121161 || header.version !== 8) {\n      console.error('Not a valid MD2 file')\n      return\n    }\n\n    if (header.offset_end !== data.byteLength) {\n      console.error('Corrupted MD2 file')\n      return\n    }\n\n    //\n\n    const geometry = new BufferGeometry()\n\n    // uvs\n\n    const uvsTemp = []\n    let offset = header.offset_st\n\n    for (let i = 0, l = header.num_st; i < l; i++) {\n      const u = data.getInt16(offset + 0, true)\n      const v = data.getInt16(offset + 2, true)\n\n      uvsTemp.push(u / header.skinwidth, 1 - v / header.skinheight)\n\n      offset += 4\n    }\n\n    // triangles\n\n    offset = header.offset_tris\n\n    const vertexIndices = []\n    const uvIndices = []\n\n    for (let i = 0, l = header.num_tris; i < l; i++) {\n      vertexIndices.push(\n        data.getUint16(offset + 0, true),\n        data.getUint16(offset + 2, true),\n        data.getUint16(offset + 4, true),\n      )\n\n      uvIndices.push(\n        data.getUint16(offset + 6, true),\n        data.getUint16(offset + 8, true),\n        data.getUint16(offset + 10, true),\n      )\n\n      offset += 12\n    }\n\n    // frames\n\n    const translation = new Vector3()\n    const scale = new Vector3()\n    const string = []\n\n    const frames = []\n\n    offset = header.offset_frames\n\n    for (let i = 0, l = header.num_frames; i < l; i++) {\n      scale.set(data.getFloat32(offset + 0, true), data.getFloat32(offset + 4, true), data.getFloat32(offset + 8, true))\n\n      translation.set(\n        data.getFloat32(offset + 12, true),\n        data.getFloat32(offset + 16, true),\n        data.getFloat32(offset + 20, true),\n      )\n\n      offset += 24\n\n      for (let j = 0; j < 16; j++) {\n        const character = data.getUint8(offset + j, true)\n        if (character === 0) break\n\n        string[j] = character\n      }\n\n      const frame = {\n        name: String.fromCharCode.apply(null, string),\n        vertices: [],\n        normals: [],\n      }\n\n      offset += 16\n\n      for (let j = 0; j < header.num_vertices; j++) {\n        let x = data.getUint8(offset++, true)\n        let y = data.getUint8(offset++, true)\n        let z = data.getUint8(offset++, true)\n        const n = _normalData[data.getUint8(offset++, true)]\n\n        x = x * scale.x + translation.x\n        y = y * scale.y + translation.y\n        z = z * scale.z + translation.z\n\n        frame.vertices.push(x, z, y) // convert to Y-up\n        frame.normals.push(n[0], n[2], n[1]) // convert to Y-up\n      }\n\n      frames.push(frame)\n    }\n\n    // static\n\n    const positions = []\n    const normals = []\n    const uvs = []\n\n    const verticesTemp = frames[0].vertices\n    const normalsTemp = frames[0].normals\n\n    for (let i = 0, l = vertexIndices.length; i < l; i++) {\n      const vertexIndex = vertexIndices[i]\n      let stride = vertexIndex * 3\n\n      //\n\n      const x = verticesTemp[stride]\n      const y = verticesTemp[stride + 1]\n      const z = verticesTemp[stride + 2]\n\n      positions.push(x, y, z)\n\n      //\n\n      const nx = normalsTemp[stride]\n      const ny = normalsTemp[stride + 1]\n      const nz = normalsTemp[stride + 2]\n\n      normals.push(nx, ny, nz)\n\n      //\n\n      const uvIndex = uvIndices[i]\n      stride = uvIndex * 2\n\n      const u = uvsTemp[stride]\n      const v = uvsTemp[stride + 1]\n\n      uvs.push(u, v)\n    }\n\n    geometry.setAttribute('position', new Float32BufferAttribute(positions, 3))\n    geometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n    geometry.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n\n    // animation\n\n    const morphPositions = []\n    const morphNormals = []\n\n    for (let i = 0, l = frames.length; i < l; i++) {\n      const frame = frames[i]\n      const attributeName = frame.name\n\n      if (frame.vertices.length > 0) {\n        const positions = []\n\n        for (let j = 0, jl = vertexIndices.length; j < jl; j++) {\n          const vertexIndex = vertexIndices[j]\n          const stride = vertexIndex * 3\n\n          const x = frame.vertices[stride]\n          const y = frame.vertices[stride + 1]\n          const z = frame.vertices[stride + 2]\n\n          positions.push(x, y, z)\n        }\n\n        const positionAttribute = new Float32BufferAttribute(positions, 3)\n        positionAttribute.name = attributeName\n\n        morphPositions.push(positionAttribute)\n      }\n\n      if (frame.normals.length > 0) {\n        const normals = []\n\n        for (let j = 0, jl = vertexIndices.length; j < jl; j++) {\n          const vertexIndex = vertexIndices[j]\n          const stride = vertexIndex * 3\n\n          const nx = frame.normals[stride]\n          const ny = frame.normals[stride + 1]\n          const nz = frame.normals[stride + 2]\n\n          normals.push(nx, ny, nz)\n        }\n\n        const normalAttribute = new Float32BufferAttribute(normals, 3)\n        normalAttribute.name = attributeName\n\n        morphNormals.push(normalAttribute)\n      }\n    }\n\n    geometry.morphAttributes.position = morphPositions\n    geometry.morphAttributes.normal = morphNormals\n    geometry.morphTargetsRelative = false\n\n    geometry.animations = AnimationClip.CreateClipsFromMorphTargetSequences(frames, 10)\n\n    return geometry\n  }\n}\n\nexport { MD2Loader }\n"], "names": ["positions", "normals"], "mappings": ";AAEA,MAAM,cAAc;AAAA,EAClB,CAAC,WAAW,GAAK,QAAQ;AAAA,EACzB,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,WAAW,GAAK,QAAQ;AAAA,EACzB,CAAC,WAAW,KAAK,QAAQ;AAAA,EACzB,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,GAAK,GAAK,CAAG;AAAA,EACd,CAAC,GAAK,UAAU,QAAQ;AAAA,EACxB,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,GAAK,UAAU,QAAQ;AAAA,EACxB,CAAC,UAAU,KAAK,QAAQ;AAAA,EACxB,CAAC,UAAU,GAAK,QAAQ;AAAA,EACxB,CAAC,UAAU,GAAK,QAAQ;AAAA,EACxB,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,SAAS,UAAU,QAAQ;AAAA,EAC5B,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,WAAW,UAAU,CAAG;AAAA,EACzB,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,MAAM,UAAU,QAAQ;AAAA,EACzB,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,MAAM,UAAU,SAAS;AAAA,EAC1B,CAAC,WAAW,UAAU,CAAG;AAAA,EACzB,CAAC,GAAK,UAAU,SAAS;AAAA,EACzB,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,GAAK,UAAU,SAAS;AAAA,EACzB,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,GAAK,GAAK,CAAG;AAAA,EACd,CAAC,GAAK,UAAU,QAAQ;AAAA,EACxB,CAAC,WAAW,UAAU,OAAO;AAAA,EAC7B,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,UAAU,UAAU,OAAO;AAAA,EAC5B,CAAC,KAAK,UAAU,QAAQ;AAAA,EACxB,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,KAAK,UAAU,SAAS;AAAA,EACzB,CAAC,UAAU,UAAU,CAAG;AAAA,EACxB,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,UAAU,UAAU,CAAG;AAAA,EACxB,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,UAAU,UAAU,GAAG;AAAA,EACxB,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,UAAU,UAAU,CAAG;AAAA,EACxB,CAAC,GAAK,GAAK,CAAG;AAAA,EACd,CAAC,UAAU,SAAS,QAAQ;AAAA,EAC5B,CAAC,UAAU,WAAW,CAAG;AAAA,EACzB,CAAC,UAAU,WAAW,CAAG;AAAA,EACzB,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,UAAU,UAAU,QAAQ;AAAA,EAC7B,CAAC,UAAU,WAAW,GAAG;AAAA,EACzB,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,UAAU,GAAK,QAAQ;AAAA,EACxB,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,UAAU,UAAU,IAAI;AAAA,EACzB,CAAC,UAAU,SAAS,SAAS;AAAA,EAC7B,CAAC,UAAU,GAAK,SAAS;AAAA,EACzB,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,UAAU,GAAK,SAAS;AAAA,EACzB,CAAC,UAAU,WAAW,IAAI;AAAA,EAC1B,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,UAAU,KAAK,SAAS;AAAA,EACzB,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,WAAW,KAAK,SAAS;AAAA,EAC1B,CAAC,GAAK,UAAU,SAAS;AAAA,EACzB,CAAC,WAAW,GAAK,SAAS;AAAA,EAC1B,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,WAAW,GAAK,SAAS;AAAA,EAC1B,CAAC,UAAU,UAAU,SAAS;AAAA,EAC9B,CAAC,GAAK,GAAK,EAAI;AAAA,EACf,CAAC,UAAU,GAAK,SAAS;AAAA,EACzB,CAAC,SAAS,UAAU,SAAS;AAAA,EAC7B,CAAC,WAAW,WAAW,SAAS;AAAA,EAChC,CAAC,WAAW,MAAM,SAAS;AAAA,EAC3B,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,GAAK,WAAW,SAAS;AAAA,EAC1B,CAAC,WAAW,WAAW,SAAS;AAAA,EAChC,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,GAAK,WAAW,SAAS;AAAA,EAC1B,CAAC,UAAU,MAAM,SAAS;AAAA,EAC1B,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,SAAS,WAAW,SAAS;AAAA,EAC9B,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,KAAK,WAAW,SAAS;AAAA,EAC1B,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,UAAU,WAAW,SAAS;AAAA,EAC/B,CAAC,GAAK,WAAW,SAAS;AAAA,EAC1B,CAAC,GAAK,IAAM,CAAG;AAAA,EACf,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,GAAK,WAAW,QAAQ;AAAA,EACzB,CAAC,GAAK,WAAW,QAAQ;AAAA,EACzB,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,UAAU,WAAW,OAAO;AAAA,EAC7B,CAAC,KAAK,WAAW,QAAQ;AAAA,EACzB,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,UAAU,WAAW,CAAG;AAAA,EACzB,CAAC,WAAW,WAAW,SAAS;AAAA,EAChC,CAAC,MAAM,WAAW,SAAS;AAAA,EAC3B,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,WAAW,WAAW,CAAG;AAAA,EAC1B,CAAC,WAAW,WAAW,SAAS;AAAA,EAChC,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,WAAW,WAAW,CAAG;AAAA,EAC1B,CAAC,MAAM,WAAW,QAAQ;AAAA,EAC1B,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,WAAW,WAAW,OAAO;AAAA,EAC9B,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,WAAW,WAAW,GAAG;AAAA,EAC1B,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,WAAW,MAAM,QAAQ;AAAA,EAC1B,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,WAAW,WAAW,QAAQ;AAAA,EAC/B,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,SAAS,WAAW,QAAQ;AAAA,EAC7B,CAAC,UAAU,MAAM,QAAQ;AAAA,EACzB,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,GAAK,WAAW,QAAQ;AAAA,EACzB,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,UAAU,WAAW,QAAQ;AAAA,EAC9B,CAAC,WAAW,UAAU,CAAG;AAAA,EACzB,CAAC,WAAW,SAAS,QAAQ;AAAA,EAC7B,CAAC,IAAM,GAAK,CAAG;AAAA,EACf,CAAC,WAAW,GAAK,QAAQ;AAAA,EACzB,CAAC,WAAW,WAAW,CAAG;AAAA,EAC1B,CAAC,WAAW,UAAU,QAAQ;AAAA,EAC9B,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,WAAW,SAAS,SAAS;AAAA,EAC9B,CAAC,WAAW,UAAU,IAAI;AAAA,EAC1B,CAAC,WAAW,WAAW,SAAS;AAAA,EAChC,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,WAAW,WAAW,IAAI;AAAA,EAC3B,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,WAAW,WAAW,SAAS;AAAA,EAChC,CAAC,WAAW,GAAK,SAAS;AAAA,EAC1B,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,WAAW,UAAU,SAAS;AAAA,EAC/B,CAAC,WAAW,WAAW,SAAS;AAAA,EAChC,CAAC,WAAW,WAAW,SAAS;AAAA,EAChC,CAAC,WAAW,WAAW,SAAS;AAClC;AAEA,MAAM,kBAAkB,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAC/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,QAAQ;AAChB,YAAI;AACF,iBAAO,MAAM,MAAM,MAAM,CAAC;AAAA,QAC3B,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,QAAQ;AACZ,UAAM,OAAO,IAAI,SAAS,MAAM;AAIhC,UAAM,SAAS,CAAE;AACjB,UAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAED,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,aAAO,YAAY,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,GAAG,IAAI;AAAA,IACnD;AAED,QAAI,OAAO,UAAU,aAAa,OAAO,YAAY,GAAG;AACtD,cAAQ,MAAM,sBAAsB;AACpC;AAAA,IACD;AAED,QAAI,OAAO,eAAe,KAAK,YAAY;AACzC,cAAQ,MAAM,oBAAoB;AAClC;AAAA,IACD;AAID,UAAM,WAAW,IAAI,eAAgB;AAIrC,UAAM,UAAU,CAAE;AAClB,QAAI,SAAS,OAAO;AAEpB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,YAAM,IAAI,KAAK,SAAS,SAAS,GAAG,IAAI;AACxC,YAAM,IAAI,KAAK,SAAS,SAAS,GAAG,IAAI;AAExC,cAAQ,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,UAAU;AAE5D,gBAAU;AAAA,IACX;AAID,aAAS,OAAO;AAEhB,UAAM,gBAAgB,CAAE;AACxB,UAAM,YAAY,CAAE;AAEpB,aAAS,IAAI,GAAG,IAAI,OAAO,UAAU,IAAI,GAAG,KAAK;AAC/C,oBAAc;AAAA,QACZ,KAAK,UAAU,SAAS,GAAG,IAAI;AAAA,QAC/B,KAAK,UAAU,SAAS,GAAG,IAAI;AAAA,QAC/B,KAAK,UAAU,SAAS,GAAG,IAAI;AAAA,MAChC;AAED,gBAAU;AAAA,QACR,KAAK,UAAU,SAAS,GAAG,IAAI;AAAA,QAC/B,KAAK,UAAU,SAAS,GAAG,IAAI;AAAA,QAC/B,KAAK,UAAU,SAAS,IAAI,IAAI;AAAA,MACjC;AAED,gBAAU;AAAA,IACX;AAID,UAAM,cAAc,IAAI,QAAS;AACjC,UAAM,QAAQ,IAAI,QAAS;AAC3B,UAAM,SAAS,CAAE;AAEjB,UAAM,SAAS,CAAE;AAEjB,aAAS,OAAO;AAEhB,aAAS,IAAI,GAAG,IAAI,OAAO,YAAY,IAAI,GAAG,KAAK;AACjD,YAAM,IAAI,KAAK,WAAW,SAAS,GAAG,IAAI,GAAG,KAAK,WAAW,SAAS,GAAG,IAAI,GAAG,KAAK,WAAW,SAAS,GAAG,IAAI,CAAC;AAEjH,kBAAY;AAAA,QACV,KAAK,WAAW,SAAS,IAAI,IAAI;AAAA,QACjC,KAAK,WAAW,SAAS,IAAI,IAAI;AAAA,QACjC,KAAK,WAAW,SAAS,IAAI,IAAI;AAAA,MAClC;AAED,gBAAU;AAEV,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,YAAY,KAAK,SAAS,SAAS,GAAG,IAAI;AAChD,YAAI,cAAc;AAAG;AAErB,eAAO,CAAC,IAAI;AAAA,MACb;AAED,YAAM,QAAQ;AAAA,QACZ,MAAM,OAAO,aAAa,MAAM,MAAM,MAAM;AAAA,QAC5C,UAAU,CAAE;AAAA,QACZ,SAAS,CAAE;AAAA,MACZ;AAED,gBAAU;AAEV,eAAS,IAAI,GAAG,IAAI,OAAO,cAAc,KAAK;AAC5C,YAAI,IAAI,KAAK,SAAS,UAAU,IAAI;AACpC,YAAI,IAAI,KAAK,SAAS,UAAU,IAAI;AACpC,YAAI,IAAI,KAAK,SAAS,UAAU,IAAI;AACpC,cAAM,IAAI,YAAY,KAAK,SAAS,UAAU,IAAI,CAAC;AAEnD,YAAI,IAAI,MAAM,IAAI,YAAY;AAC9B,YAAI,IAAI,MAAM,IAAI,YAAY;AAC9B,YAAI,IAAI,MAAM,IAAI,YAAY;AAE9B,cAAM,SAAS,KAAK,GAAG,GAAG,CAAC;AAC3B,cAAM,QAAQ,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,MACpC;AAED,aAAO,KAAK,KAAK;AAAA,IAClB;AAID,UAAM,YAAY,CAAE;AACpB,UAAM,UAAU,CAAE;AAClB,UAAM,MAAM,CAAE;AAEd,UAAM,eAAe,OAAO,CAAC,EAAE;AAC/B,UAAM,cAAc,OAAO,CAAC,EAAE;AAE9B,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,IAAI,GAAG,KAAK;AACpD,YAAM,cAAc,cAAc,CAAC;AACnC,UAAI,SAAS,cAAc;AAI3B,YAAM,IAAI,aAAa,MAAM;AAC7B,YAAM,IAAI,aAAa,SAAS,CAAC;AACjC,YAAM,IAAI,aAAa,SAAS,CAAC;AAEjC,gBAAU,KAAK,GAAG,GAAG,CAAC;AAItB,YAAM,KAAK,YAAY,MAAM;AAC7B,YAAM,KAAK,YAAY,SAAS,CAAC;AACjC,YAAM,KAAK,YAAY,SAAS,CAAC;AAEjC,cAAQ,KAAK,IAAI,IAAI,EAAE;AAIvB,YAAM,UAAU,UAAU,CAAC;AAC3B,eAAS,UAAU;AAEnB,YAAM,IAAI,QAAQ,MAAM;AACxB,YAAM,IAAI,QAAQ,SAAS,CAAC;AAE5B,UAAI,KAAK,GAAG,CAAC;AAAA,IACd;AAED,aAAS,aAAa,YAAY,IAAI,uBAAuB,WAAW,CAAC,CAAC;AAC1E,aAAS,aAAa,UAAU,IAAI,uBAAuB,SAAS,CAAC,CAAC;AACtE,aAAS,aAAa,MAAM,IAAI,uBAAuB,KAAK,CAAC,CAAC;AAI9D,UAAM,iBAAiB,CAAE;AACzB,UAAM,eAAe,CAAE;AAEvB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,gBAAgB,MAAM;AAE5B,UAAI,MAAM,SAAS,SAAS,GAAG;AAC7B,cAAMA,aAAY,CAAE;AAEpB,iBAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAK;AACtD,gBAAM,cAAc,cAAc,CAAC;AACnC,gBAAM,SAAS,cAAc;AAE7B,gBAAM,IAAI,MAAM,SAAS,MAAM;AAC/B,gBAAM,IAAI,MAAM,SAAS,SAAS,CAAC;AACnC,gBAAM,IAAI,MAAM,SAAS,SAAS,CAAC;AAEnC,UAAAA,WAAU,KAAK,GAAG,GAAG,CAAC;AAAA,QACvB;AAED,cAAM,oBAAoB,IAAI,uBAAuBA,YAAW,CAAC;AACjE,0BAAkB,OAAO;AAEzB,uBAAe,KAAK,iBAAiB;AAAA,MACtC;AAED,UAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,cAAMC,WAAU,CAAE;AAElB,iBAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAK;AACtD,gBAAM,cAAc,cAAc,CAAC;AACnC,gBAAM,SAAS,cAAc;AAE7B,gBAAM,KAAK,MAAM,QAAQ,MAAM;AAC/B,gBAAM,KAAK,MAAM,QAAQ,SAAS,CAAC;AACnC,gBAAM,KAAK,MAAM,QAAQ,SAAS,CAAC;AAEnC,UAAAA,SAAQ,KAAK,IAAI,IAAI,EAAE;AAAA,QACxB;AAED,cAAM,kBAAkB,IAAI,uBAAuBA,UAAS,CAAC;AAC7D,wBAAgB,OAAO;AAEvB,qBAAa,KAAK,eAAe;AAAA,MAClC;AAAA,IACF;AAED,aAAS,gBAAgB,WAAW;AACpC,aAAS,gBAAgB,SAAS;AAClC,aAAS,uBAAuB;AAEhC,aAAS,aAAa,cAAc,oCAAoC,QAAQ,EAAE;AAElF,WAAO;AAAA,EACR;AACH;"}