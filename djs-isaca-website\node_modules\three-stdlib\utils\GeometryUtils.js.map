{"version": 3, "file": "GeometryUtils.js", "sources": ["../../src/utils/GeometryUtils.ts"], "sourcesContent": ["import { Vector3 } from 'three'\n\n/**\n * Generates 2D-Coordinates in a very fast way.\n *\n * Based on work by:\n * @link http://www.openprocessing.org/sketch/15493\n *\n * @param center     Center of Hilbert curve.\n * @param size       Total width of Hilbert curve.\n * @param iterations Number of subdivisions.\n * @param v0         Corner index -X, -Z.\n * @param v1         Corner index -X, +Z.\n * @param v2         Corner index +X, +Z.\n * @param v3         Corner index +X, -Z.\n */\nconst hilbert2D = (\n  center = new Vector3(0, 0, 0),\n  size = 10,\n  iterations = 1,\n  v0 = 0,\n  v1 = 1,\n  v2 = 2,\n  v3 = 3,\n): Vector3[] => {\n  // Default Vars\n  const half = size / 2\n  const vec_s = [\n    new Vector3(center.x - half, center.y, center.z - half),\n    new Vector3(center.x - half, center.y, center.z + half),\n    new Vector3(center.x + half, center.y, center.z + half),\n    new Vector3(center.x + half, center.y, center.z - half),\n  ]\n\n  const vec = [vec_s[v0], vec_s[v1], vec_s[v2], vec_s[v3]]\n\n  // Recurse iterations\n  if (0 <= --iterations) {\n    const tmp: Vector3[] = []\n\n    Array.prototype.push.apply(tmp, hilbert2D(vec[0], half, iterations, v0, v3, v2, v1))\n    Array.prototype.push.apply(tmp, hilbert2D(vec[1], half, iterations, v0, v1, v2, v3))\n    Array.prototype.push.apply(tmp, hilbert2D(vec[2], half, iterations, v0, v1, v2, v3))\n    Array.prototype.push.apply(tmp, hilbert2D(vec[3], half, iterations, v2, v1, v0, v3))\n\n    // Return recursive call\n    return tmp\n  }\n\n  // Return complete Hilbert Curve.\n  return vec\n}\n\n/**\n * Generates 3D-Coordinates in a very fast way.\n *\n * Based on work by:\n * @link http://www.openprocessing.org/visuals/?visualID=15599\n *\n * @param center     Center of Hilbert curve.\n * @param size       Total width of Hilbert curve.\n * @param iterations Number of subdivisions.\n * @param v0         Corner index -X, +Y, -Z.\n * @param v1         Corner index -X, +Y, +Z.\n * @param v2         Corner index -X, -Y, +Z.\n * @param v3         Corner index -X, -Y, -Z.\n * @param v4         Corner index +X, -Y, -Z.\n * @param v5         Corner index +X, -Y, +Z.\n * @param v6         Corner index +X, +Y, +Z.\n * @param v7         Corner index +X, +Y, -Z.\n */\nconst hilbert3D = (\n  center = new Vector3(0, 0, 0),\n  size = 10,\n  iterations = 1,\n  v0 = 0,\n  v1 = 1,\n  v2 = 2,\n  v3 = 3,\n  v4 = 4,\n  v5 = 5,\n  v6 = 6,\n  v7 = 7,\n): Vector3[] => {\n  // Default Vars\n  const half = size / 2\n  const vec_s = [\n    new Vector3(center.x - half, center.y + half, center.z - half),\n    new Vector3(center.x - half, center.y + half, center.z + half),\n    new Vector3(center.x - half, center.y - half, center.z + half),\n    new Vector3(center.x - half, center.y - half, center.z - half),\n    new Vector3(center.x + half, center.y - half, center.z - half),\n    new Vector3(center.x + half, center.y - half, center.z + half),\n    new Vector3(center.x + half, center.y + half, center.z + half),\n    new Vector3(center.x + half, center.y + half, center.z - half),\n  ]\n\n  const vec = [vec_s[v0], vec_s[v1], vec_s[v2], vec_s[v3], vec_s[v4], vec_s[v5], vec_s[v6], vec_s[v7]]\n\n  // Recurse iterations\n  if (--iterations >= 0) {\n    const tmp: Vector3[] = []\n\n    Array.prototype.push.apply(tmp, hilbert3D(vec[0], half, iterations, v0, v3, v4, v7, v6, v5, v2, v1))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[1], half, iterations, v0, v7, v6, v1, v2, v5, v4, v3))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[2], half, iterations, v0, v7, v6, v1, v2, v5, v4, v3))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[3], half, iterations, v2, v3, v0, v1, v6, v7, v4, v5))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[4], half, iterations, v2, v3, v0, v1, v6, v7, v4, v5))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[5], half, iterations, v4, v3, v2, v5, v6, v1, v0, v7))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[6], half, iterations, v4, v3, v2, v5, v6, v1, v0, v7))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[7], half, iterations, v6, v5, v2, v1, v0, v3, v4, v7))\n\n    // Return recursive call\n    return tmp\n  }\n\n  // Return complete Hilbert Curve.\n  return vec\n}\n\n/**\n * Generates a Gosper curve (lying in the XY plane)\n *\n * https://gist.github.com/nitaku/6521802\n *\n * @param size The size of a single gosper island.\n */\nconst gosper = (size = 1): number[] => {\n  function fractalize(config: { axiom: string; steps: number; rules: Record<string, string> }): string {\n    let output = ''\n    let input = config.axiom\n\n    for (let i = 0, il = config.steps; 0 <= il ? i < il : i > il; 0 <= il ? i++ : i--) {\n      output = ''\n\n      for (let j = 0, jl = input.length; j < jl; j++) {\n        const char = input[j]\n\n        if (char in config.rules) {\n          output += config.rules[char]\n        } else {\n          output += char\n        }\n      }\n\n      input = output\n    }\n\n    return output\n  }\n\n  function toPoints(config: { fractal: string; size: number; angle: number }): number[] {\n    let currX = 0\n    let currY = 0\n    let angle = 0\n    const path = [0, 0, 0]\n    const fractal = config.fractal\n\n    for (let i = 0, l = fractal.length; i < l; i++) {\n      const char = fractal[i]\n\n      if (char === '+') {\n        angle += config.angle\n      } else if (char === '-') {\n        angle -= config.angle\n      } else if (char === 'F') {\n        currX += config.size * Math.cos(angle)\n        currY += -config.size * Math.sin(angle)\n        path.push(currX, currY, 0)\n      }\n    }\n\n    return path\n  }\n\n  //\n\n  const gosper = fractalize({\n    axiom: 'A',\n    steps: 4,\n    rules: {\n      A: 'A+BF++BF-FA--FAFA-BF+',\n      B: '-FA+BFBF++BF+FA--FA-B',\n    },\n  })\n\n  const points = toPoints({\n    fractal: gosper,\n    size: size,\n    angle: Math.PI / 3, // 60 degrees\n  })\n\n  return points\n}\n\nexport const GeometryUtils = {\n  hilbert3D,\n  gosper,\n  hilbert2D,\n}\n"], "names": ["gosper"], "mappings": ";AAgBA,MAAM,YAAY,CAChB,SAAS,IAAI,QAAQ,GAAG,GAAG,CAAC,GAC5B,OAAO,IACP,aAAa,GACb,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,MACS;AAEd,QAAM,OAAO,OAAO;AACpB,QAAM,QAAQ;AAAA,IACZ,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,GAAG,OAAO,IAAI,IAAI;AAAA,IACtD,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,GAAG,OAAO,IAAI,IAAI;AAAA,IACtD,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,GAAG,OAAO,IAAI,IAAI;AAAA,IACtD,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,GAAG,OAAO,IAAI,IAAI;AAAA,EAAA;AAGxD,QAAM,MAAM,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;AAGnD,MAAA,KAAK,EAAE,YAAY;AACrB,UAAM,MAAiB,CAAA;AAEvB,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC;AACnF,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC;AACnF,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC;AACnF,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC;AAG5E,WAAA;AAAA,EACT;AAGO,SAAA;AACT;AAoBA,MAAM,YAAY,CAChB,SAAS,IAAI,QAAQ,GAAG,GAAG,CAAC,GAC5B,OAAO,IACP,aAAa,GACb,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,MACS;AAEd,QAAM,OAAO,OAAO;AACpB,QAAM,QAAQ;AAAA,IACZ,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI;AAAA,IAC7D,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI;AAAA,IAC7D,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI;AAAA,IAC7D,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI;AAAA,IAC7D,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI;AAAA,IAC7D,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI;AAAA,IAC7D,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI;AAAA,IAC7D,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI;AAAA,EAAA;AAGzD,QAAA,MAAM,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;AAG/F,MAAA,EAAE,cAAc,GAAG;AACrB,UAAM,MAAiB,CAAA;AAEvB,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AACnG,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AACnG,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AACnG,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AACnG,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AACnG,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AACnG,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AACnG,UAAM,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM,YAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAG5F,WAAA;AAAA,EACT;AAGO,SAAA;AACT;AASA,MAAM,SAAS,CAAC,OAAO,MAAgB;AACrC,WAAS,WAAW,QAAiF;AACnG,QAAI,SAAS;AACb,QAAI,QAAQ,OAAO;AAEnB,aAAS,IAAI,GAAG,KAAK,OAAO,OAAO,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,KAAK;AACxE,eAAA;AAET,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACxC,cAAA,OAAO,MAAM,CAAC;AAEhB,YAAA,QAAQ,OAAO,OAAO;AACd,oBAAA,OAAO,MAAM,IAAI;AAAA,QAAA,OACtB;AACK,oBAAA;AAAA,QACZ;AAAA,MACF;AAEQ,cAAA;AAAA,IACV;AAEO,WAAA;AAAA,EACT;AAEA,WAAS,SAAS,QAAoE;AACpF,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,UAAM,OAAO,CAAC,GAAG,GAAG,CAAC;AACrB,UAAM,UAAU,OAAO;AAEvB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AACxC,YAAA,OAAO,QAAQ,CAAC;AAEtB,UAAI,SAAS,KAAK;AAChB,iBAAS,OAAO;AAAA,MAAA,WACP,SAAS,KAAK;AACvB,iBAAS,OAAO;AAAA,MAAA,WACP,SAAS,KAAK;AACvB,iBAAS,OAAO,OAAO,KAAK,IAAI,KAAK;AACrC,iBAAS,CAAC,OAAO,OAAO,KAAK,IAAI,KAAK;AACjC,aAAA,KAAK,OAAO,OAAO,CAAC;AAAA,MAC3B;AAAA,IACF;AAEO,WAAA;AAAA,EACT;AAIA,QAAMA,UAAS,WAAW;AAAA,IACxB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EAAA,CACD;AAED,QAAM,SAAS,SAAS;AAAA,IACtB,SAASA;AAAAA,IACT;AAAA,IACA,OAAO,KAAK,KAAK;AAAA;AAAA,EAAA,CAClB;AAEM,SAAA;AACT;AAEO,MAAM,gBAAgB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AACF;"}