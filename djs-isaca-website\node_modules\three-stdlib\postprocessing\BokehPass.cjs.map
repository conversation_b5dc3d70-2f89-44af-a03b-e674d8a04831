{"version": 3, "file": "BokehPass.cjs", "sources": ["../../src/postprocessing/BokehPass.ts"], "sourcesContent": ["/**\n * Depth-of-field post-process with bokeh shader\n */\n\nimport { Pass, FullScreenQuad } from './Pass'\nimport {\n  Color,\n  MeshDepthMaterial,\n  NearestFilter,\n  NoBlending,\n  PerspectiveCamera,\n  RGBADepthPacking,\n  Scene,\n  ShaderMaterial,\n  UniformsUtils,\n  WebGLRenderer,\n  WebGLRenderTarget,\n} from 'three'\nimport { BokehShader } from '../shaders/BokehShader'\n\ntype BokehPassParams = {\n  focus?: number\n  aspect?: number\n  aperture?: number\n  maxblur?: number\n  width?: number\n  height?: number\n}\n\nclass BokehPass extends Pass {\n  public scene: Scene\n  public camera: PerspectiveCamera\n  public renderTargetDepth: WebGLRenderTarget\n  public materialDepth: MeshDepthMaterial\n  public materialBokeh: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  private _oldClearColor: Color\n\n  public uniforms\n\n  constructor(scene: Scene, camera: PerspectiveCamera, params: BokehPassParams) {\n    super()\n    this.scene = scene\n    this.camera = camera\n    const focus = params.focus !== undefined ? params.focus : 1.0\n    const aspect = params.aspect !== undefined ? params.aspect : camera.aspect\n    const aperture = params.aperture !== undefined ? params.aperture : 0.025\n    const maxblur = params.maxblur !== undefined ? params.maxblur : 1.0 // render targets\n\n    const width = params.width || window.innerWidth || 1\n    const height = params.height || window.innerHeight || 1\n    this.renderTargetDepth = new WebGLRenderTarget(width, height, {\n      minFilter: NearestFilter,\n      magFilter: NearestFilter,\n    })\n    this.renderTargetDepth.texture.name = 'BokehPass.depth' // depth material\n\n    this.materialDepth = new MeshDepthMaterial()\n    this.materialDepth.depthPacking = RGBADepthPacking\n    this.materialDepth.blending = NoBlending // bokeh material\n\n    if (BokehShader === undefined) {\n      console.error('BokehPass relies on BokehShader')\n    }\n\n    const bokehShader = BokehShader\n    const bokehUniforms = UniformsUtils.clone(bokehShader.uniforms)\n    bokehUniforms['tDepth'].value = this.renderTargetDepth.texture\n    bokehUniforms['focus'].value = focus\n    bokehUniforms['aspect'].value = aspect\n    bokehUniforms['aperture'].value = aperture\n    bokehUniforms['maxblur'].value = maxblur\n    bokehUniforms['nearClip'].value = camera.near\n    bokehUniforms['farClip'].value = camera.far\n    this.materialBokeh = new ShaderMaterial({\n      defines: Object.assign({}, bokehShader.defines),\n      uniforms: bokehUniforms,\n      vertexShader: bokehShader.vertexShader,\n      fragmentShader: bokehShader.fragmentShader,\n    })\n    this.uniforms = bokehUniforms\n    this.needsSwap = false\n    this.fsQuad = new FullScreenQuad(this.materialBokeh)\n    this._oldClearColor = new Color()\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    // Render depth into texture\n    this.scene.overrideMaterial = this.materialDepth\n    renderer.getClearColor(this._oldClearColor)\n    const oldClearAlpha = renderer.getClearAlpha()\n    const oldAutoClear = renderer.autoClear\n    renderer.autoClear = false\n    renderer.setClearColor(0xffffff)\n    renderer.setClearAlpha(1.0)\n    renderer.setRenderTarget(this.renderTargetDepth)\n    renderer.clear()\n    renderer.render(this.scene, this.camera) // Render bokeh composite\n\n    this.uniforms['tColor'].value = readBuffer.texture\n    this.uniforms['nearClip'].value = this.camera.near\n    this.uniforms['farClip'].value = this.camera.far\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n\n    this.scene.overrideMaterial = null\n    renderer.setClearColor(this._oldClearColor)\n    renderer.setClearAlpha(oldClearAlpha)\n    renderer.autoClear = oldAutoClear\n  }\n}\n\nexport { BokehPass }\n"], "names": ["Pass", "WebGLRenderTarget", "NearestFilter", "MeshDepthMaterial", "RGBADepthPacking", "NoBlending", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "UniformsUtils", "ShaderMaterial", "FullScreenQuad", "Color"], "mappings": ";;;;;;;;;;;AA6BA,MAAM,kBAAkBA,KAAAA,KAAK;AAAA,EAY3B,YAAY,OAAc,QAA2B,QAAyB;AACtE;AAZD;AACA;AACA;AACA;AACA;AACA;AAEC;AAED;AAIL,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,UAAM,QAAQ,OAAO,UAAU,SAAY,OAAO,QAAQ;AAC1D,UAAM,SAAS,OAAO,WAAW,SAAY,OAAO,SAAS,OAAO;AACpE,UAAM,WAAW,OAAO,aAAa,SAAY,OAAO,WAAW;AACnE,UAAM,UAAU,OAAO,YAAY,SAAY,OAAO,UAAU;AAEhE,UAAM,QAAQ,OAAO,SAAS,OAAO,cAAc;AACnD,UAAM,SAAS,OAAO,UAAU,OAAO,eAAe;AACtD,SAAK,oBAAoB,IAAIC,wBAAkB,OAAO,QAAQ;AAAA,MAC5D,WAAWC,MAAA;AAAA,MACX,WAAWA,MAAA;AAAA,IAAA,CACZ;AACI,SAAA,kBAAkB,QAAQ,OAAO;AAEjC,SAAA,gBAAgB,IAAIC,MAAAA;AACzB,SAAK,cAAc,eAAeC;AAClC,SAAK,cAAc,WAAWC;AAE9B,QAAIC,YAAAA,gBAAgB,QAAW;AAC7B,cAAQ,MAAM,iCAAiC;AAAA,IACjD;AAEA,UAAM,cAAcA,YAAAA;AACpB,UAAM,gBAAgBC,MAAA,cAAc,MAAM,YAAY,QAAQ;AAC9D,kBAAc,QAAQ,EAAE,QAAQ,KAAK,kBAAkB;AACzC,kBAAA,OAAO,EAAE,QAAQ;AACjB,kBAAA,QAAQ,EAAE,QAAQ;AAClB,kBAAA,UAAU,EAAE,QAAQ;AACpB,kBAAA,SAAS,EAAE,QAAQ;AACnB,kBAAA,UAAU,EAAE,QAAQ,OAAO;AAC3B,kBAAA,SAAS,EAAE,QAAQ,OAAO;AACnC,SAAA,gBAAgB,IAAIC,qBAAe;AAAA,MACtC,SAAS,OAAO,OAAO,CAAA,GAAI,YAAY,OAAO;AAAA,MAC9C,UAAU;AAAA,MACV,cAAc,YAAY;AAAA,MAC1B,gBAAgB,YAAY;AAAA,IAAA,CAC7B;AACD,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS,IAAIC,KAAe,eAAA,KAAK,aAAa;AAC9C,SAAA,iBAAiB,IAAIC,MAAAA;EAC5B;AAAA,EAEO,OACL,UACA,aACA,YAEM;AAED,SAAA,MAAM,mBAAmB,KAAK;AAC1B,aAAA,cAAc,KAAK,cAAc;AACpC,UAAA,gBAAgB,SAAS;AAC/B,UAAM,eAAe,SAAS;AAC9B,aAAS,YAAY;AACrB,aAAS,cAAc,QAAQ;AAC/B,aAAS,cAAc,CAAG;AACjB,aAAA,gBAAgB,KAAK,iBAAiB;AAC/C,aAAS,MAAM;AACf,aAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAEvC,SAAK,SAAS,QAAQ,EAAE,QAAQ,WAAW;AAC3C,SAAK,SAAS,UAAU,EAAE,QAAQ,KAAK,OAAO;AAC9C,SAAK,SAAS,SAAS,EAAE,QAAQ,KAAK,OAAO;AAE7C,QAAI,KAAK,gBAAgB;AACvB,eAAS,gBAAgB,IAAI;AACxB,WAAA,OAAO,OAAO,QAAQ;AAAA,IAAA,OACtB;AACL,eAAS,gBAAgB,WAAW;AACpC,eAAS,MAAM;AACV,WAAA,OAAO,OAAO,QAAQ;AAAA,IAC7B;AAEA,SAAK,MAAM,mBAAmB;AACrB,aAAA,cAAc,KAAK,cAAc;AAC1C,aAAS,cAAc,aAAa;AACpC,aAAS,YAAY;AAAA,EACvB;AACF;;"}