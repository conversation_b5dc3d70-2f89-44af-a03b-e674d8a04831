{"version": 3, "file": "AssimpLoader.cjs", "sources": ["../../src/loaders/AssimpLoader.js"], "sourcesContent": ["import {\n  Bone,\n  BufferAttribute,\n  BufferGeometry,\n  Color,\n  FileLoader,\n  Loader,\n  LoaderUtils,\n  Matrix4,\n  Mesh,\n  MeshLambertMaterial,\n  MeshPhongMaterial,\n  Object3D,\n  Quaternion,\n  Skeleton,\n  SkinnedMesh,\n  TextureLoader,\n  Vector3,\n} from 'three'\n\nclass AssimpLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    var scope = this\n\n    var path = scope.path === '' ? LoaderUtils.extractUrlBase(url) : scope.path\n\n    var loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(buffer, path) {\n    var textureLoader = new TextureLoader(this.manager)\n    textureLoader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin)\n\n    var Virtulous = {}\n\n    Virtulous.KeyFrame = class {\n      constructor(time, matrix) {\n        this.time = time\n        this.matrix = matrix.clone()\n        this.position = new Vector3()\n        this.quaternion = new Quaternion()\n        this.scale = new Vector3(1, 1, 1)\n        this.matrix.decompose(this.position, this.quaternion, this.scale)\n        this.clone = function () {\n          var n = new Virtulous.KeyFrame(this.time, this.matrix)\n          return n\n        }\n\n        this.lerp = function (nextKey, time) {\n          time -= this.time\n          var dist = nextKey.time - this.time\n          var l = time / dist\n          var l2 = 1 - l\n          var keypos = this.position\n          var keyrot = this.quaternion\n          //      var keyscl =  key.parentspaceScl || key.scl;\n          var key2pos = nextKey.position\n          var key2rot = nextKey.quaternion\n          //  var key2scl =  key2.parentspaceScl || key2.scl;\n          Virtulous.KeyFrame.tempAniPos.x = keypos.x * l2 + key2pos.x * l\n          Virtulous.KeyFrame.tempAniPos.y = keypos.y * l2 + key2pos.y * l\n          Virtulous.KeyFrame.tempAniPos.z = keypos.z * l2 + key2pos.z * l\n          //     tempAniScale.x = keyscl[0] * l2 + key2scl[0] * l;\n          //     tempAniScale.y = keyscl[1] * l2 + key2scl[1] * l;\n          //     tempAniScale.z = keyscl[2] * l2 + key2scl[2] * l;\n          Virtulous.KeyFrame.tempAniQuat.set(keyrot.x, keyrot.y, keyrot.z, keyrot.w)\n          Virtulous.KeyFrame.tempAniQuat.slerp(key2rot, l)\n          return Virtulous.KeyFrame.tempAniMatrix.compose(\n            Virtulous.KeyFrame.tempAniPos,\n            Virtulous.KeyFrame.tempAniQuat,\n            Virtulous.KeyFrame.tempAniScale,\n          )\n        }\n      }\n    }\n\n    Virtulous.KeyFrame.tempAniPos = new Vector3()\n    Virtulous.KeyFrame.tempAniQuat = new Quaternion()\n    Virtulous.KeyFrame.tempAniScale = new Vector3(1, 1, 1)\n    Virtulous.KeyFrame.tempAniMatrix = new Matrix4()\n    Virtulous.KeyFrameTrack = function () {\n      this.keys = []\n      this.target = null\n      this.time = 0\n      this.length = 0\n      this._accelTable = {}\n      this.fps = 20\n      this.addKey = function (key) {\n        this.keys.push(key)\n      }\n\n      this.init = function () {\n        this.sortKeys()\n\n        if (this.keys.length > 0) this.length = this.keys[this.keys.length - 1].time\n        else this.length = 0\n\n        if (!this.fps) return\n\n        for (let j = 0; j < this.length * this.fps; j++) {\n          for (let i = 0; i < this.keys.length; i++) {\n            if (this.keys[i].time == j) {\n              this._accelTable[j] = i\n              break\n            } else if (this.keys[i].time < j / this.fps && this.keys[i + 1] && this.keys[i + 1].time >= j / this.fps) {\n              this._accelTable[j] = i\n              break\n            }\n          }\n        }\n      }\n\n      this.parseFromThree = function (data) {\n        var fps = data.fps\n        this.target = data.node\n        var track = data.hierarchy[0].keys\n        for (let i = 0; i < track.length; i++) {\n          this.addKey(new Virtulous.KeyFrame(i / fps || track[i].time, track[i].targets[0].data))\n        }\n\n        this.init()\n      }\n\n      this.parseFromCollada = function (data) {\n        var track = data.keys\n        var fps = this.fps\n\n        for (let i = 0; i < track.length; i++) {\n          this.addKey(new Virtulous.KeyFrame(i / fps || track[i].time, track[i].matrix))\n        }\n\n        this.init()\n      }\n\n      this.sortKeys = function () {\n        this.keys.sort(this.keySortFunc)\n      }\n\n      this.keySortFunc = function (a, b) {\n        return a.time - b.time\n      }\n\n      this.clone = function () {\n        var t = new Virtulous.KeyFrameTrack()\n        t.target = this.target\n        t.time = this.time\n        t.length = this.length\n\n        for (let i = 0; i < this.keys.length; i++) {\n          t.addKey(this.keys[i].clone())\n        }\n\n        t.init()\n        return t\n      }\n\n      this.reTarget = function (root, compareitor) {\n        if (!compareitor) compareitor = Virtulous.TrackTargetNodeNameCompare\n        this.target = compareitor(root, this.target)\n      }\n\n      this.keySearchAccel = function (time) {\n        time *= this.fps\n        time = Math.floor(time)\n        return this._accelTable[time] || 0\n      }\n\n      this.setTime = function (time) {\n        time = Math.abs(time)\n        if (this.length) time = (time % this.length) + 0.05\n        var key0 = null\n        var key1 = null\n\n        for (let i = this.keySearchAccel(time); i < this.keys.length; i++) {\n          if (this.keys[i].time == time) {\n            key0 = this.keys[i]\n            key1 = this.keys[i]\n            break\n          } else if (this.keys[i].time < time && this.keys[i + 1] && this.keys[i + 1].time > time) {\n            key0 = this.keys[i]\n            key1 = this.keys[i + 1]\n            break\n          } else if (this.keys[i].time < time && i == this.keys.length - 1) {\n            key0 = this.keys[i]\n            key1 = this.keys[0].clone()\n            key1.time += this.length + 0.05\n            break\n          }\n        }\n\n        if (key0 && key1 && key0 !== key1) {\n          this.target.matrixAutoUpdate = false\n          this.target.matrix.copy(key0.lerp(key1, time))\n          this.target.matrixWorldNeedsUpdate = true\n          return\n        }\n\n        if (key0 && key1 && key0 == key1) {\n          this.target.matrixAutoUpdate = false\n          this.target.matrix.copy(key0.matrix)\n          this.target.matrixWorldNeedsUpdate = true\n          return\n        }\n      }\n    }\n\n    Virtulous.TrackTargetNodeNameCompare = function (root, target) {\n      function find(node, name) {\n        if (node.name == name) return node\n\n        for (let i = 0; i < node.children.length; i++) {\n          var r = find(node.children[i], name)\n          if (r) return r\n        }\n\n        return null\n      }\n\n      return find(root, target.name)\n    }\n\n    Virtulous.Animation = function () {\n      this.tracks = []\n      this.length = 0\n\n      this.addTrack = function (track) {\n        this.tracks.push(track)\n        this.length = Math.max(track.length, this.length)\n      }\n\n      this.setTime = function (time) {\n        this.time = time\n\n        for (let i = 0; i < this.tracks.length; i++) this.tracks[i].setTime(time)\n      }\n\n      this.clone = function (target, compareitor) {\n        if (!compareitor) compareitor = Virtulous.TrackTargetNodeNameCompare\n        var n = new Virtulous.Animation()\n        n.target = target\n        for (let i = 0; i < this.tracks.length; i++) {\n          var track = this.tracks[i].clone()\n          track.reTarget(target, compareitor)\n          n.addTrack(track)\n        }\n\n        return n\n      }\n    }\n\n    var ASSBIN_CHUNK_AICAMERA = 0x1234\n    var ASSBIN_CHUNK_AILIGHT = 0x1235\n    var ASSBIN_CHUNK_AITEXTURE = 0x1236\n    var ASSBIN_CHUNK_AIMESH = 0x1237\n    var ASSBIN_CHUNK_AINODEANIM = 0x1238\n    var ASSBIN_CHUNK_AISCENE = 0x1239\n    var ASSBIN_CHUNK_AIBONE = 0x123a\n    var ASSBIN_CHUNK_AIANIMATION = 0x123b\n    var ASSBIN_CHUNK_AINODE = 0x123c\n    var ASSBIN_CHUNK_AIMATERIAL = 0x123d\n    var ASSBIN_CHUNK_AIMATERIALPROPERTY = 0x123e\n    var ASSBIN_MESH_HAS_POSITIONS = 0x1\n    var ASSBIN_MESH_HAS_NORMALS = 0x2\n    var ASSBIN_MESH_HAS_TANGENTS_AND_BITANGENTS = 0x4\n    var ASSBIN_MESH_HAS_TEXCOORD_BASE = 0x100\n    var ASSBIN_MESH_HAS_COLOR_BASE = 0x10000\n    var AI_MAX_NUMBER_OF_COLOR_SETS = 1\n    var AI_MAX_NUMBER_OF_TEXTURECOORDS = 4\n    //var aiLightSource_UNDEFINED = 0x0;\n    //! A directional light source has a well-defined direction\n    //! but is infinitely far away. That's quite a good\n    //! approximation for sun light.\n    var aiLightSource_DIRECTIONAL = 0x1\n    //! A point light source has a well-defined position\n    //! in space but no direction - it emits light in all\n    //! directions. A normal bulb is a point light.\n    //var aiLightSource_POINT = 0x2;\n    //! A spot light source emits light in a specific\n    //! angle. It has a position and a direction it is pointing to.\n    //! A good example for a spot light is a light spot in\n    //! sport arenas.\n    var aiLightSource_SPOT = 0x3\n    //! The generic light level of the world, including the bounces\n    //! of all other lightsources.\n    //! Typically, there's at most one ambient light in a scene.\n    //! This light type doesn't have a valid position, direction, or\n    //! other properties, just a color.\n    //var aiLightSource_AMBIENT = 0x4;\n    /** Flat shading. Shading is done on per-face base,\n     *  diffuse only. Also known as 'faceted shading'.\n     */\n    //var aiShadingMode_Flat = 0x1;\n    /** Simple Gouraud shading.\n     */\n    //var aiShadingMode_Gouraud = 0x2;\n    /** Phong-Shading -\n     */\n    //var aiShadingMode_Phong = 0x3;\n    /** Phong-Blinn-Shading\n     */\n    //var aiShadingMode_Blinn = 0x4;\n    /** Toon-Shading per pixel\n     *\n     *  Also known as 'comic' shader.\n     */\n    //var aiShadingMode_Toon = 0x5;\n    /** OrenNayar-Shading per pixel\n     *\n     *  Extension to standard Lambertian shading, taking the\n     *  roughness of the material into account\n     */\n    //var aiShadingMode_OrenNayar = 0x6;\n    /** Minnaert-Shading per pixel\n     *\n     *  Extension to standard Lambertian shading, taking the\n     *  \"darkness\" of the material into account\n     */\n    //var aiShadingMode_Minnaert = 0x7;\n    /** CookTorrance-Shading per pixel\n     *\n     *  Special shader for metallic surfaces.\n     */\n    //var aiShadingMode_CookTorrance = 0x8;\n    /** No shading at all. Constant light influence of 1.0.\n     */\n    //var aiShadingMode_NoShading = 0x9;\n    /** Fresnel shading\n     */\n    //var aiShadingMode_Fresnel = 0xa;\n    //var aiTextureType_NONE = 0x0;\n    /** The texture is combined with the result of the diffuse\n     *  lighting equation.\n     */\n    var aiTextureType_DIFFUSE = 0x1\n    /** The texture is combined with the result of the specular\n     *  lighting equation.\n     */\n    //var aiTextureType_SPECULAR = 0x2;\n    /** The texture is combined with the result of the ambient\n     *  lighting equation.\n     */\n    //var aiTextureType_AMBIENT = 0x3;\n    /** The texture is added to the result of the lighting\n     *  calculation. It isn't influenced by incoming light.\n     */\n    //var aiTextureType_EMISSIVE = 0x4;\n    /** The texture is a height map.\n     *\n     *  By convention, higher gray-scale values stand for\n     *  higher elevations from the base height.\n     */\n    //var aiTextureType_HEIGHT = 0x5;\n    /** The texture is a (tangent space) normal-map.\n     *\n     *  Again, there are several conventions for tangent-space\n     *  normal maps. Assimp does (intentionally) not\n     *  distinguish here.\n     */\n    var aiTextureType_NORMALS = 0x6\n    /** The texture defines the glossiness of the material.\n     *\n     *  The glossiness is in fact the exponent of the specular\n     *  (phong) lighting equation. Usually there is a conversion\n     *  function defined to map the linear color values in the\n     *  texture to a suitable exponent. Have fun.\n     */\n    //var aiTextureType_SHININESS = 0x7;\n    /** The texture defines per-pixel opacity.\n     *\n     *  Usually 'white' means opaque and 'black' means\n     *  'transparency'. Or quite the opposite. Have fun.\n     */\n    var aiTextureType_OPACITY = 0x8\n    /** Displacement texture\n     *\n     *  The exact purpose and format is application-dependent.\n     *  Higher color values stand for higher vertex displacements.\n     */\n    //var aiTextureType_DISPLACEMENT = 0x9;\n    /** Lightmap texture (aka Ambient Occlusion)\n     *\n     *  Both 'Lightmaps' and dedicated 'ambient occlusion maps' are\n     *  covered by this material property. The texture contains a\n     *  scaling value for the final color value of a pixel. Its\n     *  intensity is not affected by incoming light.\n     */\n    var aiTextureType_LIGHTMAP = 0xa\n    /** Reflection texture\n     *\n     * Contains the color of a perfect mirror reflection.\n     * Rarely used, almost never for real-time applications.\n     */\n    //var aiTextureType_REFLECTION = 0xB;\n    /** Unknown texture\n     *\n     *  A texture reference that does not match any of the definitions\n     *  above is considered to be 'unknown'. It is still imported,\n     *  but is excluded from any further postprocessing.\n     */\n    //var aiTextureType_UNKNOWN = 0xC;\n    var BONESPERVERT = 4\n\n    function ASSBIN_MESH_HAS_TEXCOORD(n) {\n      return ASSBIN_MESH_HAS_TEXCOORD_BASE << n\n    }\n\n    function ASSBIN_MESH_HAS_COLOR(n) {\n      return ASSBIN_MESH_HAS_COLOR_BASE << n\n    }\n\n    function markBones(scene) {\n      for (let i in scene.mMeshes) {\n        var mesh = scene.mMeshes[i]\n        for (let k in mesh.mBones) {\n          var boneNode = scene.findNode(mesh.mBones[k].mName)\n          if (boneNode) boneNode.isBone = true\n        }\n      }\n    }\n\n    function cloneTreeToBones(root, scene) {\n      var rootBone = new Bone()\n      rootBone.matrix.copy(root.matrix)\n      rootBone.matrixWorld.copy(root.matrixWorld)\n      rootBone.position.copy(root.position)\n      rootBone.quaternion.copy(root.quaternion)\n      rootBone.scale.copy(root.scale)\n      scene.nodeCount++\n      rootBone.name = 'bone_' + root.name + scene.nodeCount.toString()\n\n      if (!scene.nodeToBoneMap[root.name]) scene.nodeToBoneMap[root.name] = []\n      scene.nodeToBoneMap[root.name].push(rootBone)\n      for (let i in root.children) {\n        var child = cloneTreeToBones(root.children[i], scene)\n        rootBone.add(child)\n      }\n\n      return rootBone\n    }\n\n    function sortWeights(indexes, weights) {\n      var pairs = []\n\n      for (let i = 0; i < indexes.length; i++) {\n        pairs.push({\n          i: indexes[i],\n          w: weights[i],\n        })\n      }\n\n      pairs.sort(function (a, b) {\n        return b.w - a.w\n      })\n\n      while (pairs.length < 4) {\n        pairs.push({\n          i: 0,\n          w: 0,\n        })\n      }\n\n      if (pairs.length > 4) pairs.length = 4\n      var sum = 0\n\n      for (let i = 0; i < 4; i++) {\n        sum += pairs[i].w * pairs[i].w\n      }\n\n      sum = Math.sqrt(sum)\n\n      for (let i = 0; i < 4; i++) {\n        pairs[i].w = pairs[i].w / sum\n        indexes[i] = pairs[i].i\n        weights[i] = pairs[i].w\n      }\n    }\n\n    function findMatchingBone(root, name) {\n      if (root.name.indexOf('bone_' + name) == 0) return root\n\n      for (let i in root.children) {\n        var ret = findMatchingBone(root.children[i], name)\n\n        if (ret) return ret\n      }\n\n      return undefined\n    }\n\n    class aiMesh {\n      constructor() {\n        this.mPrimitiveTypes = 0\n        this.mNumVertices = 0\n        this.mNumFaces = 0\n        this.mNumBones = 0\n        this.mMaterialIndex = 0\n        this.mVertices = []\n        this.mNormals = []\n        this.mTangents = []\n        this.mBitangents = []\n        this.mColors = [[]]\n        this.mTextureCoords = [[]]\n        this.mFaces = []\n        this.mBones = []\n        this.hookupSkeletons = function (scene) {\n          if (this.mBones.length == 0) return\n\n          var allBones = []\n          var offsetMatrix = []\n          var skeletonRoot = scene.findNode(this.mBones[0].mName)\n\n          while (skeletonRoot.mParent && skeletonRoot.mParent.isBone) {\n            skeletonRoot = skeletonRoot.mParent\n          }\n\n          var threeSkeletonRoot = skeletonRoot.toTHREE(scene)\n          var threeSkeletonRootBone = cloneTreeToBones(threeSkeletonRoot, scene)\n          this.threeNode.add(threeSkeletonRootBone)\n\n          for (let i = 0; i < this.mBones.length; i++) {\n            var bone = findMatchingBone(threeSkeletonRootBone, this.mBones[i].mName)\n\n            if (bone) {\n              var tbone = bone\n              allBones.push(tbone)\n              //tbone.matrixAutoUpdate = false;\n              offsetMatrix.push(this.mBones[i].mOffsetMatrix.toTHREE())\n            } else {\n              var skeletonRoot = scene.findNode(this.mBones[i].mName)\n              if (!skeletonRoot) return\n              var threeSkeletonRoot = skeletonRoot.toTHREE(scene)\n              var threeSkeletonRootBone = cloneTreeToBones(threeSkeletonRoot, scene)\n              this.threeNode.add(threeSkeletonRootBone)\n              var bone = findMatchingBone(threeSkeletonRootBone, this.mBones[i].mName)\n              var tbone = bone\n              allBones.push(tbone)\n              //tbone.matrixAutoUpdate = false;\n              offsetMatrix.push(this.mBones[i].mOffsetMatrix.toTHREE())\n            }\n          }\n\n          var skeleton = new Skeleton(allBones, offsetMatrix)\n\n          this.threeNode.bind(skeleton, new Matrix4())\n          this.threeNode.material.skinning = true\n        }\n\n        this.toTHREE = function (scene) {\n          if (this.threeNode) return this.threeNode\n          var geometry = new BufferGeometry()\n          var mat\n          if (scene.mMaterials[this.mMaterialIndex]) mat = scene.mMaterials[this.mMaterialIndex].toTHREE(scene)\n          else mat = new MeshLambertMaterial()\n          geometry.setIndex(new BufferAttribute(new Uint32Array(this.mIndexArray), 1))\n          geometry.setAttribute('position', new BufferAttribute(this.mVertexBuffer, 3))\n          if (this.mNormalBuffer && this.mNormalBuffer.length > 0) {\n            geometry.setAttribute('normal', new BufferAttribute(this.mNormalBuffer, 3))\n          }\n          if (this.mColorBuffer && this.mColorBuffer.length > 0) {\n            geometry.setAttribute('color', new BufferAttribute(this.mColorBuffer, 4))\n          }\n          if (this.mTexCoordsBuffers[0] && this.mTexCoordsBuffers[0].length > 0) {\n            geometry.setAttribute('uv', new BufferAttribute(new Float32Array(this.mTexCoordsBuffers[0]), 2))\n          }\n          if (this.mTexCoordsBuffers[1] && this.mTexCoordsBuffers[1].length > 0) {\n            geometry.setAttribute('uv1', new BufferAttribute(new Float32Array(this.mTexCoordsBuffers[1]), 2))\n          }\n          if (this.mTangentBuffer && this.mTangentBuffer.length > 0) {\n            geometry.setAttribute('tangents', new BufferAttribute(this.mTangentBuffer, 3))\n          }\n          if (this.mBitangentBuffer && this.mBitangentBuffer.length > 0) {\n            geometry.setAttribute('bitangents', new BufferAttribute(this.mBitangentBuffer, 3))\n          }\n          if (this.mBones.length > 0) {\n            var weights = []\n            var bones = []\n\n            for (let i = 0; i < this.mBones.length; i++) {\n              for (let j = 0; j < this.mBones[i].mWeights.length; j++) {\n                var weight = this.mBones[i].mWeights[j]\n                if (weight) {\n                  if (!weights[weight.mVertexId]) weights[weight.mVertexId] = []\n                  if (!bones[weight.mVertexId]) bones[weight.mVertexId] = []\n                  weights[weight.mVertexId].push(weight.mWeight)\n                  bones[weight.mVertexId].push(parseInt(i))\n                }\n              }\n            }\n\n            for (let i in bones) {\n              sortWeights(bones[i], weights[i])\n            }\n\n            var _weights = []\n            var _bones = []\n\n            for (let i = 0; i < weights.length; i++) {\n              for (let j = 0; j < 4; j++) {\n                if (weights[i] && bones[i]) {\n                  _weights.push(weights[i][j])\n                  _bones.push(bones[i][j])\n                } else {\n                  _weights.push(0)\n                  _bones.push(0)\n                }\n              }\n            }\n\n            geometry.setAttribute('skinWeight', new BufferAttribute(new Float32Array(_weights), BONESPERVERT))\n            geometry.setAttribute('skinIndex', new BufferAttribute(new Float32Array(_bones), BONESPERVERT))\n          }\n\n          var mesh\n\n          if (this.mBones.length == 0) mesh = new Mesh(geometry, mat)\n\n          if (this.mBones.length > 0) {\n            mesh = new SkinnedMesh(geometry, mat)\n            mesh.normalizeSkinWeights()\n          }\n\n          this.threeNode = mesh\n          //mesh.matrixAutoUpdate = false;\n          return mesh\n        }\n      }\n    }\n\n    class aiFace {\n      constructor() {\n        this.mNumIndices = 0\n        this.mIndices = []\n      }\n    }\n\n    class aiVector3D {\n      constructor() {\n        this.x = 0\n        this.y = 0\n        this.z = 0\n\n        this.toTHREE = function () {\n          return new Vector3(this.x, this.y, this.z)\n        }\n      }\n    }\n\n    class aiColor3D {\n      constructor() {\n        this.r = 0\n        this.g = 0\n        this.b = 0\n        this.a = 0\n        this.toTHREE = function () {\n          return new Color(this.r, this.g, this.b)\n        }\n      }\n    }\n\n    class aiQuaternion {\n      constructor() {\n        this.x = 0\n        this.y = 0\n        this.z = 0\n        this.w = 0\n        this.toTHREE = function () {\n          return new Quaternion(this.x, this.y, this.z, this.w)\n        }\n      }\n    }\n\n    class aiVertexWeight {\n      constructor() {\n        this.mVertexId = 0\n        this.mWeight = 0\n      }\n    }\n\n    class aiString {\n      constructor() {\n        this.data = []\n        this.toString = function () {\n          var str = ''\n          this.data.forEach(function (i) {\n            str += String.fromCharCode(i)\n          })\n          return str.replace(/[^\\x20-\\x7E]+/g, '')\n        }\n      }\n    }\n\n    class aiVectorKey {\n      constructor() {\n        this.mTime = 0\n        this.mValue = null\n      }\n    }\n\n    class aiQuatKey {\n      constructor() {\n        this.mTime = 0\n        this.mValue = null\n      }\n    }\n\n    class aiNode {\n      constructor() {\n        this.mName = ''\n        this.mTransformation = []\n        this.mNumChildren = 0\n        this.mNumMeshes = 0\n        this.mMeshes = []\n        this.mChildren = []\n        this.toTHREE = function (scene) {\n          if (this.threeNode) return this.threeNode\n          var o = new Object3D()\n          o.name = this.mName\n          o.matrix = this.mTransformation.toTHREE()\n\n          for (let i = 0; i < this.mChildren.length; i++) {\n            o.add(this.mChildren[i].toTHREE(scene))\n          }\n\n          for (let i = 0; i < this.mMeshes.length; i++) {\n            o.add(scene.mMeshes[this.mMeshes[i]].toTHREE(scene))\n          }\n\n          this.threeNode = o\n          //o.matrixAutoUpdate = false;\n          o.matrix.decompose(o.position, o.quaternion, o.scale)\n          return o\n        }\n      }\n    }\n\n    class aiBone {\n      constructor() {\n        this.mName = ''\n        this.mNumWeights = 0\n        this.mOffsetMatrix = 0\n      }\n    }\n\n    class aiMaterialProperty {\n      constructor() {\n        this.mKey = ''\n        this.mSemantic = 0\n        this.mIndex = 0\n        this.mData = []\n        this.mDataLength = 0\n        this.mType = 0\n        this.dataAsColor = function () {\n          var array = new Uint8Array(this.mData).buffer\n          var reader = new DataView(array)\n          var r = reader.getFloat32(0, true)\n          var g = reader.getFloat32(4, true)\n          var b = reader.getFloat32(8, true)\n          //var a = reader.getFloat32(12, true);\n          return new Color(r, g, b)\n        }\n\n        this.dataAsFloat = function () {\n          var array = new Uint8Array(this.mData).buffer\n          var reader = new DataView(array)\n          var r = reader.getFloat32(0, true)\n          return r\n        }\n\n        this.dataAsBool = function () {\n          var array = new Uint8Array(this.mData).buffer\n          var reader = new DataView(array)\n          var r = reader.getFloat32(0, true)\n          return !!r\n        }\n\n        this.dataAsString = function () {\n          var s = new aiString()\n          s.data = this.mData\n          return s.toString()\n        }\n\n        this.dataAsMap = function () {\n          var s = new aiString()\n          s.data = this.mData\n          var path = s.toString()\n          path = path.replace(/\\\\/g, '/')\n\n          if (path.indexOf('/') != -1) {\n            path = path.substr(path.lastIndexOf('/') + 1)\n          }\n\n          return textureLoader.load(path)\n        }\n      }\n    }\n\n    var namePropMapping = {\n      '?mat.name': 'name',\n      '$mat.shadingm': 'shading',\n      '$mat.twosided': 'twoSided',\n      '$mat.wireframe': 'wireframe',\n      '$clr.ambient': 'ambient',\n      '$clr.diffuse': 'color',\n      '$clr.specular': 'specular',\n      '$clr.emissive': 'emissive',\n      '$clr.transparent': 'transparent',\n      '$clr.reflective': 'reflect',\n      '$mat.shininess': 'shininess',\n      '$mat.reflectivity': 'reflectivity',\n      '$mat.refracti': 'refraction',\n      '$tex.file': 'map',\n    }\n\n    var nameTypeMapping = {\n      '?mat.name': 'string',\n      '$mat.shadingm': 'bool',\n      '$mat.twosided': 'bool',\n      '$mat.wireframe': 'bool',\n      '$clr.ambient': 'color',\n      '$clr.diffuse': 'color',\n      '$clr.specular': 'color',\n      '$clr.emissive': 'color',\n      '$clr.transparent': 'color',\n      '$clr.reflective': 'color',\n      '$mat.shininess': 'float',\n      '$mat.reflectivity': 'float',\n      '$mat.refracti': 'float',\n      '$tex.file': 'map',\n    }\n\n    class aiMaterial {\n      constructor() {\n        this.mNumAllocated = 0\n        this.mNumProperties = 0\n        this.mProperties = []\n        this.toTHREE = function () {\n          var mat = new MeshPhongMaterial()\n\n          for (let i = 0; i < this.mProperties.length; i++) {\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'float') {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsFloat()\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'color') {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsColor()\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'bool') {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsBool()\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'string') {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsString()\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'map') {\n              var prop = this.mProperties[i]\n              if (prop.mSemantic == aiTextureType_DIFFUSE) mat.map = this.mProperties[i].dataAsMap()\n              if (prop.mSemantic == aiTextureType_NORMALS) mat.normalMap = this.mProperties[i].dataAsMap()\n              if (prop.mSemantic == aiTextureType_LIGHTMAP) mat.lightMap = this.mProperties[i].dataAsMap()\n              if (prop.mSemantic == aiTextureType_OPACITY) mat.alphaMap = this.mProperties[i].dataAsMap()\n            }\n          }\n\n          mat.ambient.r = 0.53\n          mat.ambient.g = 0.53\n          mat.ambient.b = 0.53\n          mat.color.r = 1\n          mat.color.g = 1\n          mat.color.b = 1\n          return mat\n        }\n      }\n    }\n\n    function veclerp(v1, v2, l) {\n      var v = new Vector3()\n      var lm1 = 1 - l\n      v.x = v1.x * l + v2.x * lm1\n      v.y = v1.y * l + v2.y * lm1\n      v.z = v1.z * l + v2.z * lm1\n      return v\n    }\n\n    function quatlerp(q1, q2, l) {\n      return q1.clone().slerp(q2, 1 - l)\n    }\n\n    function sampleTrack(keys, time, lne, lerp) {\n      if (keys.length == 1) return keys[0].mValue.toTHREE()\n\n      var dist = Infinity\n      var key = null\n      var nextKey = null\n\n      for (let i = 0; i < keys.length; i++) {\n        var timeDist = Math.abs(keys[i].mTime - time)\n\n        if (timeDist < dist && keys[i].mTime <= time) {\n          dist = timeDist\n          key = keys[i]\n          nextKey = keys[i + 1]\n        }\n      }\n\n      if (!key) {\n        return null\n      } else if (nextKey) {\n        var dT = nextKey.mTime - key.mTime\n        var T = key.mTime - time\n        var l = T / dT\n\n        return lerp(key.mValue.toTHREE(), nextKey.mValue.toTHREE(), l)\n      } else {\n        nextKey = keys[0].clone()\n        nextKey.mTime += lne\n\n        var dT = nextKey.mTime - key.mTime\n        var T = key.mTime - time\n        var l = T / dT\n\n        return lerp(key.mValue.toTHREE(), nextKey.mValue.toTHREE(), l)\n      }\n    }\n\n    class aiNodeAnim {\n      constructor() {\n        this.mNodeName = ''\n        this.mNumPositionKeys = 0\n        this.mNumRotationKeys = 0\n        this.mNumScalingKeys = 0\n        this.mPositionKeys = []\n        this.mRotationKeys = []\n        this.mScalingKeys = []\n        this.mPreState = ''\n        this.mPostState = ''\n        this.init = function (tps) {\n          if (!tps) tps = 1\n\n          function t(t) {\n            t.mTime /= tps\n          }\n\n          this.mPositionKeys.forEach(t)\n          this.mRotationKeys.forEach(t)\n          this.mScalingKeys.forEach(t)\n        }\n\n        this.sortKeys = function () {\n          function comp(a, b) {\n            return a.mTime - b.mTime\n          }\n\n          this.mPositionKeys.sort(comp)\n          this.mRotationKeys.sort(comp)\n          this.mScalingKeys.sort(comp)\n        }\n\n        this.getLength = function () {\n          return Math.max(\n            Math.max.apply(\n              null,\n              this.mPositionKeys.map(function (a) {\n                return a.mTime\n              }),\n            ),\n            Math.max.apply(\n              null,\n              this.mRotationKeys.map(function (a) {\n                return a.mTime\n              }),\n            ),\n            Math.max.apply(\n              null,\n              this.mScalingKeys.map(function (a) {\n                return a.mTime\n              }),\n            ),\n          )\n        }\n\n        this.toTHREE = function (o) {\n          this.sortKeys()\n          var length = this.getLength()\n          var track = new Virtulous.KeyFrameTrack()\n\n          for (let i = 0; i < length; i += 0.05) {\n            var matrix = new Matrix4()\n            var time = i\n            var pos = sampleTrack(this.mPositionKeys, time, length, veclerp)\n            var scale = sampleTrack(this.mScalingKeys, time, length, veclerp)\n            var rotation = sampleTrack(this.mRotationKeys, time, length, quatlerp)\n            matrix.compose(pos, rotation, scale)\n\n            var key = new Virtulous.KeyFrame(time, matrix)\n            track.addKey(key)\n          }\n\n          track.target = o.findNode(this.mNodeName).toTHREE()\n\n          var tracks = [track]\n\n          if (o.nodeToBoneMap[this.mNodeName]) {\n            for (let i = 0; i < o.nodeToBoneMap[this.mNodeName].length; i++) {\n              var t2 = track.clone()\n              t2.target = o.nodeToBoneMap[this.mNodeName][i]\n              tracks.push(t2)\n            }\n          }\n\n          return tracks\n        }\n      }\n    }\n\n    class aiAnimation {\n      constructor() {\n        this.mName = ''\n        this.mDuration = 0\n        this.mTicksPerSecond = 0\n        this.mNumChannels = 0\n        this.mChannels = []\n        this.toTHREE = function (root) {\n          var animationHandle = new Virtulous.Animation()\n\n          for (let i in this.mChannels) {\n            this.mChannels[i].init(this.mTicksPerSecond)\n\n            var tracks = this.mChannels[i].toTHREE(root)\n\n            for (let j in tracks) {\n              tracks[j].init()\n              animationHandle.addTrack(tracks[j])\n            }\n          }\n\n          animationHandle.length = Math.max.apply(\n            null,\n            animationHandle.tracks.map(function (e) {\n              return e.length\n            }),\n          )\n          return animationHandle\n        }\n      }\n    }\n\n    class aiTexture {\n      constructor() {\n        this.mWidth = 0\n        this.mHeight = 0\n        this.texAchFormatHint = []\n        this.pcData = []\n      }\n    }\n\n    class aiLight {\n      constructor() {\n        this.mName = ''\n        this.mType = 0\n        this.mAttenuationConstant = 0\n        this.mAttenuationLinear = 0\n        this.mAttenuationQuadratic = 0\n        this.mAngleInnerCone = 0\n        this.mAngleOuterCone = 0\n        this.mColorDiffuse = null\n        this.mColorSpecular = null\n        this.mColorAmbient = null\n      }\n    }\n\n    class aiCamera {\n      constructor() {\n        this.mName = ''\n        this.mPosition = null\n        this.mLookAt = null\n        this.mUp = null\n        this.mHorizontalFOV = 0\n        this.mClipPlaneNear = 0\n        this.mClipPlaneFar = 0\n        this.mAspect = 0\n      }\n    }\n\n    class aiScene {\n      constructor() {\n        this.versionMajor = 0\n        this.versionMinor = 0\n        this.versionRevision = 0\n        this.compileFlags = 0\n        this.mFlags = 0\n        this.mNumMeshes = 0\n        this.mNumMaterials = 0\n        this.mNumAnimations = 0\n        this.mNumTextures = 0\n        this.mNumLights = 0\n        this.mNumCameras = 0\n        this.mRootNode = null\n        this.mMeshes = []\n        this.mMaterials = []\n        this.mAnimations = []\n        this.mLights = []\n        this.mCameras = []\n        this.nodeToBoneMap = {}\n        this.findNode = function (name, root) {\n          if (!root) {\n            root = this.mRootNode\n          }\n\n          if (root.mName == name) {\n            return root\n          }\n\n          for (let i = 0; i < root.mChildren.length; i++) {\n            var ret = this.findNode(name, root.mChildren[i])\n            if (ret) return ret\n          }\n\n          return null\n        }\n\n        this.toTHREE = function () {\n          this.nodeCount = 0\n\n          markBones(this)\n\n          var o = this.mRootNode.toTHREE(this)\n\n          for (let i in this.mMeshes) this.mMeshes[i].hookupSkeletons(this)\n\n          if (this.mAnimations.length > 0) {\n            var a = this.mAnimations[0].toTHREE(this)\n          }\n\n          return { object: o, animation: a }\n        }\n      }\n    }\n\n    class aiMatrix4 {\n      constructor() {\n        this.elements = [[], [], [], []]\n        this.toTHREE = function () {\n          var m = new Matrix4()\n\n          for (let i = 0; i < 4; ++i) {\n            for (let i2 = 0; i2 < 4; ++i2) {\n              m.elements[i * 4 + i2] = this.elements[i2][i]\n            }\n          }\n\n          return m\n        }\n      }\n    }\n\n    var littleEndian = true\n\n    function readFloat(dataview) {\n      var val = dataview.getFloat32(dataview.readOffset, littleEndian)\n      dataview.readOffset += 4\n      return val\n    }\n\n    function Read_double(dataview) {\n      var val = dataview.getFloat64(dataview.readOffset, littleEndian)\n      dataview.readOffset += 8\n      return val\n    }\n\n    function Read_uint8_t(dataview) {\n      var val = dataview.getUint8(dataview.readOffset)\n      dataview.readOffset += 1\n      return val\n    }\n\n    function Read_uint16_t(dataview) {\n      var val = dataview.getUint16(dataview.readOffset, littleEndian)\n      dataview.readOffset += 2\n      return val\n    }\n\n    function Read_unsigned_int(dataview) {\n      var val = dataview.getUint32(dataview.readOffset, littleEndian)\n      dataview.readOffset += 4\n      return val\n    }\n\n    function Read_uint32_t(dataview) {\n      var val = dataview.getUint32(dataview.readOffset, littleEndian)\n      dataview.readOffset += 4\n      return val\n    }\n\n    function Read_aiVector3D(stream) {\n      var v = new aiVector3D()\n      v.x = readFloat(stream)\n      v.y = readFloat(stream)\n      v.z = readFloat(stream)\n      return v\n    }\n\n    function Read_aiColor3D(stream) {\n      var c = new aiColor3D()\n      c.r = readFloat(stream)\n      c.g = readFloat(stream)\n      c.b = readFloat(stream)\n      return c\n    }\n\n    function Read_aiQuaternion(stream) {\n      var v = new aiQuaternion()\n      v.w = readFloat(stream)\n      v.x = readFloat(stream)\n      v.y = readFloat(stream)\n      v.z = readFloat(stream)\n      return v\n    }\n\n    function Read_aiString(stream) {\n      var s = new aiString()\n      var stringlengthbytes = Read_unsigned_int(stream)\n      stream.ReadBytes(s.data, 1, stringlengthbytes)\n      return s.toString()\n    }\n\n    function Read_aiVertexWeight(stream) {\n      var w = new aiVertexWeight()\n      w.mVertexId = Read_unsigned_int(stream)\n      w.mWeight = readFloat(stream)\n      return w\n    }\n\n    function Read_aiMatrix4x4(stream) {\n      var m = new aiMatrix4()\n\n      for (let i = 0; i < 4; ++i) {\n        for (let i2 = 0; i2 < 4; ++i2) {\n          m.elements[i][i2] = readFloat(stream)\n        }\n      }\n\n      return m\n    }\n\n    function Read_aiVectorKey(stream) {\n      var v = new aiVectorKey()\n      v.mTime = Read_double(stream)\n      v.mValue = Read_aiVector3D(stream)\n      return v\n    }\n\n    function Read_aiQuatKey(stream) {\n      var v = new aiQuatKey()\n      v.mTime = Read_double(stream)\n      v.mValue = Read_aiQuaternion(stream)\n      return v\n    }\n\n    function ReadArray_aiVertexWeight(stream, data, size) {\n      for (let i = 0; i < size; i++) data[i] = Read_aiVertexWeight(stream)\n    }\n\n    function ReadArray_aiVectorKey(stream, data, size) {\n      for (let i = 0; i < size; i++) data[i] = Read_aiVectorKey(stream)\n    }\n\n    function ReadArray_aiQuatKey(stream, data, size) {\n      for (let i = 0; i < size; i++) data[i] = Read_aiQuatKey(stream)\n    }\n\n    function ReadBounds(stream, T /*p*/, n) {\n      // not sure what to do here, the data isn't really useful.\n      return stream.Seek(sizeof(T) * n, aiOrigin_CUR)\n    }\n\n    function ai_assert(bool) {\n      if (!bool) throw 'asset failed'\n    }\n\n    function ReadBinaryNode(stream, parent, depth) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AINODE)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      var node = new aiNode()\n      node.mParent = parent\n      node.mDepth = depth\n      node.mName = Read_aiString(stream)\n      node.mTransformation = Read_aiMatrix4x4(stream)\n      node.mNumChildren = Read_unsigned_int(stream)\n      node.mNumMeshes = Read_unsigned_int(stream)\n\n      if (node.mNumMeshes) {\n        node.mMeshes = []\n\n        for (let i = 0; i < node.mNumMeshes; ++i) {\n          node.mMeshes[i] = Read_unsigned_int(stream)\n        }\n      }\n\n      if (node.mNumChildren) {\n        node.mChildren = []\n\n        for (let i = 0; i < node.mNumChildren; ++i) {\n          var node2 = ReadBinaryNode(stream, node, depth++)\n          node.mChildren[i] = node2\n        }\n      }\n\n      return node\n    }\n\n    // -----------------------------------------------------------------------------------\n\n    function ReadBinaryBone(stream, b) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIBONE)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      b.mName = Read_aiString(stream)\n      b.mNumWeights = Read_unsigned_int(stream)\n      b.mOffsetMatrix = Read_aiMatrix4x4(stream)\n      // for the moment we write dumb min/max values for the bones, too.\n      // maybe I'll add a better, hash-like solution later\n      if (shortened) {\n        ReadBounds(stream, b.mWeights, b.mNumWeights)\n      } else {\n        // else write as usual\n\n        b.mWeights = []\n        ReadArray_aiVertexWeight(stream, b.mWeights, b.mNumWeights)\n      }\n\n      return b\n    }\n\n    function ReadBinaryMesh(stream, mesh) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIMESH)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      mesh.mPrimitiveTypes = Read_unsigned_int(stream)\n      mesh.mNumVertices = Read_unsigned_int(stream)\n      mesh.mNumFaces = Read_unsigned_int(stream)\n      mesh.mNumBones = Read_unsigned_int(stream)\n      mesh.mMaterialIndex = Read_unsigned_int(stream)\n      mesh.mNumUVComponents = []\n      // first of all, write bits for all existent vertex components\n      var c = Read_unsigned_int(stream)\n\n      if (c & ASSBIN_MESH_HAS_POSITIONS) {\n        if (shortened) {\n          ReadBounds(stream, mesh.mVertices, mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mVertices = []\n          mesh.mVertexBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4)\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR)\n        }\n      }\n\n      if (c & ASSBIN_MESH_HAS_NORMALS) {\n        if (shortened) {\n          ReadBounds(stream, mesh.mNormals, mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mNormals = []\n          mesh.mNormalBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4)\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR)\n        }\n      }\n\n      if (c & ASSBIN_MESH_HAS_TANGENTS_AND_BITANGENTS) {\n        if (shortened) {\n          ReadBounds(stream, mesh.mTangents, mesh.mNumVertices)\n          ReadBounds(stream, mesh.mBitangents, mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mTangents = []\n          mesh.mTangentBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4)\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR)\n          mesh.mBitangents = []\n          mesh.mBitangentBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4)\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR)\n        }\n      }\n\n      for (let n = 0; n < AI_MAX_NUMBER_OF_COLOR_SETS; ++n) {\n        if (!(c & ASSBIN_MESH_HAS_COLOR(n))) break\n\n        if (shortened) {\n          ReadBounds(stream, mesh.mColors[n], mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mColors[n] = []\n          mesh.mColorBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 4 * 4)\n          stream.Seek(mesh.mNumVertices * 4 * 4, aiOrigin_CUR)\n        }\n      }\n\n      mesh.mTexCoordsBuffers = []\n\n      for (let n = 0; n < AI_MAX_NUMBER_OF_TEXTURECOORDS; ++n) {\n        if (!(c & ASSBIN_MESH_HAS_TEXCOORD(n))) break\n\n        // write number of UV components\n        mesh.mNumUVComponents[n] = Read_unsigned_int(stream)\n\n        if (shortened) {\n          ReadBounds(stream, mesh.mTextureCoords[n], mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mTextureCoords[n] = []\n          //note that assbin always writes 3d texcoords\n          mesh.mTexCoordsBuffers[n] = []\n\n          for (let uv = 0; uv < mesh.mNumVertices; uv++) {\n            mesh.mTexCoordsBuffers[n].push(readFloat(stream))\n            mesh.mTexCoordsBuffers[n].push(readFloat(stream))\n            readFloat(stream)\n          }\n        }\n      }\n\n      // write faces. There are no floating-point calculations involved\n      // in these, so we can write a simple hash over the face data\n      // to the dump file. We generate a single 32 Bit hash for 512 faces\n      // using Assimp's standard hashing function.\n      if (shortened) {\n        Read_unsigned_int(stream)\n      } else {\n        // else write as usual\n\n        // if there are less than 2^16 vertices, we can simply use 16 bit integers ...\n        mesh.mFaces = []\n        mesh.mIndexArray = []\n\n        for (let i = 0; i < mesh.mNumFaces; ++i) {\n          var f = (mesh.mFaces[i] = new aiFace())\n          // BOOST_STATIC_ASSERT(AI_MAX_FACE_INDICES <= 0xffff);\n          f.mNumIndices = Read_uint16_t(stream)\n          f.mIndices = []\n\n          for (let a = 0; a < f.mNumIndices; ++a) {\n            if (mesh.mNumVertices < 1 << 16) {\n              f.mIndices[a] = Read_uint16_t(stream)\n            } else {\n              f.mIndices[a] = Read_unsigned_int(stream)\n            }\n          }\n\n          if (f.mNumIndices === 3) {\n            mesh.mIndexArray.push(f.mIndices[0])\n            mesh.mIndexArray.push(f.mIndices[1])\n            mesh.mIndexArray.push(f.mIndices[2])\n          } else if (f.mNumIndices === 4) {\n            mesh.mIndexArray.push(f.mIndices[0])\n            mesh.mIndexArray.push(f.mIndices[1])\n            mesh.mIndexArray.push(f.mIndices[2])\n            mesh.mIndexArray.push(f.mIndices[2])\n            mesh.mIndexArray.push(f.mIndices[3])\n            mesh.mIndexArray.push(f.mIndices[0])\n          } else {\n            throw new Error(\"Sorry, can't currently triangulate polys. Use the triangulate preprocessor in Assimp.\")\n          }\n        }\n      }\n\n      // write bones\n      if (mesh.mNumBones) {\n        mesh.mBones = []\n\n        for (let a = 0; a < mesh.mNumBones; ++a) {\n          mesh.mBones[a] = new aiBone()\n          ReadBinaryBone(stream, mesh.mBones[a])\n        }\n      }\n    }\n\n    function ReadBinaryMaterialProperty(stream, prop) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIMATERIALPROPERTY)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      prop.mKey = Read_aiString(stream)\n      prop.mSemantic = Read_unsigned_int(stream)\n      prop.mIndex = Read_unsigned_int(stream)\n      prop.mDataLength = Read_unsigned_int(stream)\n      prop.mType = Read_unsigned_int(stream)\n      prop.mData = []\n      stream.ReadBytes(prop.mData, 1, prop.mDataLength)\n    }\n\n    // -----------------------------------------------------------------------------------\n\n    function ReadBinaryMaterial(stream, mat) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIMATERIAL)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      mat.mNumAllocated = mat.mNumProperties = Read_unsigned_int(stream)\n\n      if (mat.mNumProperties) {\n        if (mat.mProperties) {\n          delete mat.mProperties\n        }\n\n        mat.mProperties = []\n\n        for (let i = 0; i < mat.mNumProperties; ++i) {\n          mat.mProperties[i] = new aiMaterialProperty()\n          ReadBinaryMaterialProperty(stream, mat.mProperties[i])\n        }\n      }\n    }\n\n    function ReadBinaryNodeAnim(stream, nd) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AINODEANIM)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      nd.mNodeName = Read_aiString(stream)\n      nd.mNumPositionKeys = Read_unsigned_int(stream)\n      nd.mNumRotationKeys = Read_unsigned_int(stream)\n      nd.mNumScalingKeys = Read_unsigned_int(stream)\n      nd.mPreState = Read_unsigned_int(stream)\n      nd.mPostState = Read_unsigned_int(stream)\n\n      if (nd.mNumPositionKeys) {\n        if (shortened) {\n          ReadBounds(stream, nd.mPositionKeys, nd.mNumPositionKeys)\n        } else {\n          // else write as usual\n\n          nd.mPositionKeys = []\n          ReadArray_aiVectorKey(stream, nd.mPositionKeys, nd.mNumPositionKeys)\n        }\n      }\n\n      if (nd.mNumRotationKeys) {\n        if (shortened) {\n          ReadBounds(stream, nd.mRotationKeys, nd.mNumRotationKeys)\n        } else {\n          // else write as usual\n\n          nd.mRotationKeys = []\n          ReadArray_aiQuatKey(stream, nd.mRotationKeys, nd.mNumRotationKeys)\n        }\n      }\n\n      if (nd.mNumScalingKeys) {\n        if (shortened) {\n          ReadBounds(stream, nd.mScalingKeys, nd.mNumScalingKeys)\n        } else {\n          // else write as usual\n\n          nd.mScalingKeys = []\n          ReadArray_aiVectorKey(stream, nd.mScalingKeys, nd.mNumScalingKeys)\n        }\n      }\n    }\n\n    function ReadBinaryAnim(stream, anim) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIANIMATION)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      anim.mName = Read_aiString(stream)\n      anim.mDuration = Read_double(stream)\n      anim.mTicksPerSecond = Read_double(stream)\n      anim.mNumChannels = Read_unsigned_int(stream)\n\n      if (anim.mNumChannels) {\n        anim.mChannels = []\n\n        for (let a = 0; a < anim.mNumChannels; ++a) {\n          anim.mChannels[a] = new aiNodeAnim()\n          ReadBinaryNodeAnim(stream, anim.mChannels[a])\n        }\n      }\n    }\n\n    function ReadBinaryTexture(stream, tex) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AITEXTURE)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      tex.mWidth = Read_unsigned_int(stream)\n      tex.mHeight = Read_unsigned_int(stream)\n      stream.ReadBytes(tex.achFormatHint, 1, 4)\n\n      if (!shortened) {\n        if (!tex.mHeight) {\n          tex.pcData = []\n          stream.ReadBytes(tex.pcData, 1, tex.mWidth)\n        } else {\n          tex.pcData = []\n          stream.ReadBytes(tex.pcData, 1, tex.mWidth * tex.mHeight * 4)\n        }\n      }\n    }\n\n    function ReadBinaryLight(stream, l) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AILIGHT)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      l.mName = Read_aiString(stream)\n      l.mType = Read_unsigned_int(stream)\n\n      if (l.mType != aiLightSource_DIRECTIONAL) {\n        l.mAttenuationConstant = readFloat(stream)\n        l.mAttenuationLinear = readFloat(stream)\n        l.mAttenuationQuadratic = readFloat(stream)\n      }\n\n      l.mColorDiffuse = Read_aiColor3D(stream)\n      l.mColorSpecular = Read_aiColor3D(stream)\n      l.mColorAmbient = Read_aiColor3D(stream)\n\n      if (l.mType == aiLightSource_SPOT) {\n        l.mAngleInnerCone = readFloat(stream)\n        l.mAngleOuterCone = readFloat(stream)\n      }\n    }\n\n    function ReadBinaryCamera(stream, cam) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AICAMERA)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      cam.mName = Read_aiString(stream)\n      cam.mPosition = Read_aiVector3D(stream)\n      cam.mLookAt = Read_aiVector3D(stream)\n      cam.mUp = Read_aiVector3D(stream)\n      cam.mHorizontalFOV = readFloat(stream)\n      cam.mClipPlaneNear = readFloat(stream)\n      cam.mClipPlaneFar = readFloat(stream)\n      cam.mAspect = readFloat(stream)\n    }\n\n    function ReadBinaryScene(stream, scene) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AISCENE)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      scene.mFlags = Read_unsigned_int(stream)\n      scene.mNumMeshes = Read_unsigned_int(stream)\n      scene.mNumMaterials = Read_unsigned_int(stream)\n      scene.mNumAnimations = Read_unsigned_int(stream)\n      scene.mNumTextures = Read_unsigned_int(stream)\n      scene.mNumLights = Read_unsigned_int(stream)\n      scene.mNumCameras = Read_unsigned_int(stream)\n      // Read node graph\n      scene.mRootNode = new aiNode()\n      scene.mRootNode = ReadBinaryNode(stream, null, 0)\n      // Read all meshes\n      if (scene.mNumMeshes) {\n        scene.mMeshes = []\n\n        for (let i = 0; i < scene.mNumMeshes; ++i) {\n          scene.mMeshes[i] = new aiMesh()\n          ReadBinaryMesh(stream, scene.mMeshes[i])\n        }\n      }\n\n      // Read materials\n      if (scene.mNumMaterials) {\n        scene.mMaterials = []\n\n        for (let i = 0; i < scene.mNumMaterials; ++i) {\n          scene.mMaterials[i] = new aiMaterial()\n          ReadBinaryMaterial(stream, scene.mMaterials[i])\n        }\n      }\n\n      // Read all animations\n      if (scene.mNumAnimations) {\n        scene.mAnimations = []\n\n        for (let i = 0; i < scene.mNumAnimations; ++i) {\n          scene.mAnimations[i] = new aiAnimation()\n          ReadBinaryAnim(stream, scene.mAnimations[i])\n        }\n      }\n\n      // Read all textures\n      if (scene.mNumTextures) {\n        scene.mTextures = []\n\n        for (let i = 0; i < scene.mNumTextures; ++i) {\n          scene.mTextures[i] = new aiTexture()\n          ReadBinaryTexture(stream, scene.mTextures[i])\n        }\n      }\n\n      // Read lights\n      if (scene.mNumLights) {\n        scene.mLights = []\n\n        for (let i = 0; i < scene.mNumLights; ++i) {\n          scene.mLights[i] = new aiLight()\n          ReadBinaryLight(stream, scene.mLights[i])\n        }\n      }\n\n      // Read cameras\n      if (scene.mNumCameras) {\n        scene.mCameras = []\n\n        for (let i = 0; i < scene.mNumCameras; ++i) {\n          scene.mCameras[i] = new aiCamera()\n          ReadBinaryCamera(stream, scene.mCameras[i])\n        }\n      }\n    }\n\n    var aiOrigin_CUR = 0\n    var aiOrigin_BEG = 1\n\n    function extendStream(stream) {\n      stream.readOffset = 0\n      stream.Seek = function (off, ori) {\n        if (ori == aiOrigin_CUR) {\n          stream.readOffset += off\n        }\n\n        if (ori == aiOrigin_BEG) {\n          stream.readOffset = off\n        }\n      }\n\n      stream.ReadBytes = function (buff, size, n) {\n        var bytes = size * n\n        for (let i = 0; i < bytes; i++) buff[i] = Read_uint8_t(this)\n      }\n\n      stream.subArray32 = function (start, end) {\n        var buff = this.buffer\n        var newbuff = buff.slice(start, end)\n        return new Float32Array(newbuff)\n      }\n\n      stream.subArrayUint16 = function (start, end) {\n        var buff = this.buffer\n        var newbuff = buff.slice(start, end)\n        return new Uint16Array(newbuff)\n      }\n\n      stream.subArrayUint8 = function (start, end) {\n        var buff = this.buffer\n        var newbuff = buff.slice(start, end)\n        return new Uint8Array(newbuff)\n      }\n\n      stream.subArrayUint32 = function (start, end) {\n        var buff = this.buffer\n        var newbuff = buff.slice(start, end)\n        return new Uint32Array(newbuff)\n      }\n    }\n\n    var shortened, compressed\n\n    function InternReadFile(pFiledata) {\n      var pScene = new aiScene()\n      var stream = new DataView(pFiledata)\n      extendStream(stream)\n      stream.Seek(44, aiOrigin_CUR) // signature\n      /*unsigned int versionMajor =*/\n      pScene.versionMajor = Read_unsigned_int(stream)\n      /*unsigned int versionMinor =*/\n      pScene.versionMinor = Read_unsigned_int(stream)\n      /*unsigned int versionRevision =*/\n      pScene.versionRevision = Read_unsigned_int(stream)\n      /*unsigned int compileFlags =*/\n      pScene.compileFlags = Read_unsigned_int(stream)\n      shortened = Read_uint16_t(stream) > 0\n      compressed = Read_uint16_t(stream) > 0\n      if (shortened) throw 'Shortened binaries are not supported!'\n      stream.Seek(256, aiOrigin_CUR) // original filename\n      stream.Seek(128, aiOrigin_CUR) // options\n      stream.Seek(64, aiOrigin_CUR) // padding\n      if (compressed) {\n        var uncompressedSize = Read_uint32_t(stream)\n        var compressedSize = stream.FileSize() - stream.Tell()\n        var compressedData = []\n        stream.Read(compressedData, 1, compressedSize)\n        var uncompressedData = []\n        uncompress(uncompressedData, uncompressedSize, compressedData, compressedSize)\n        var buff = new ArrayBuffer(uncompressedData)\n        ReadBinaryScene(buff, pScene)\n      } else {\n        ReadBinaryScene(stream, pScene)\n      }\n\n      return pScene.toTHREE()\n    }\n\n    return InternReadFile(buffer)\n  }\n}\n\nexport { AssimpLoader }\n"], "names": ["Loader", "LoaderUtils", "<PERSON><PERSON><PERSON><PERSON>", "TextureLoader", "Vector3", "Quaternion", "time", "Matrix4", "Bone", "Skeleton", "BufferGeometry", "MeshLambertMaterial", "BufferAttribute", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Color", "Object3D", "path", "MeshPhongMaterial", "t"], "mappings": ";;;AAoBA,MAAM,qBAAqBA,MAAAA,OAAO;AAAA,EAChC,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,QAAI,QAAQ;AAEZ,QAAI,OAAO,MAAM,SAAS,KAAKC,MAAW,YAAC,eAAe,GAAG,IAAI,MAAM;AAEvE,QAAI,SAAS,IAAIC,iBAAW,MAAM,OAAO;AACzC,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAE/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,QAAQ;AAChB,YAAI;AACF,iBAAO,MAAM,MAAM,QAAQ,IAAI,CAAC;AAAA,QACjC,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,QAAQ,MAAM;AAClB,QAAI,gBAAgB,IAAIC,oBAAc,KAAK,OAAO;AAClD,kBAAc,QAAQ,KAAK,gBAAgB,IAAI,EAAE,eAAe,KAAK,WAAW;AAEhF,QAAI,YAAY,CAAE;AAElB,cAAU,WAAW,MAAM;AAAA,MACzB,YAAY,MAAM,QAAQ;AACxB,aAAK,OAAO;AACZ,aAAK,SAAS,OAAO,MAAO;AAC5B,aAAK,WAAW,IAAIC,cAAS;AAC7B,aAAK,aAAa,IAAIC,iBAAY;AAClC,aAAK,QAAQ,IAAID,MAAAA,QAAQ,GAAG,GAAG,CAAC;AAChC,aAAK,OAAO,UAAU,KAAK,UAAU,KAAK,YAAY,KAAK,KAAK;AAChE,aAAK,QAAQ,WAAY;AACvB,cAAI,IAAI,IAAI,UAAU,SAAS,KAAK,MAAM,KAAK,MAAM;AACrD,iBAAO;AAAA,QACR;AAED,aAAK,OAAO,SAAU,SAASE,OAAM;AACnC,UAAAA,SAAQ,KAAK;AACb,cAAI,OAAO,QAAQ,OAAO,KAAK;AAC/B,cAAI,IAAIA,QAAO;AACf,cAAI,KAAK,IAAI;AACb,cAAI,SAAS,KAAK;AAClB,cAAI,SAAS,KAAK;AAElB,cAAI,UAAU,QAAQ;AACtB,cAAI,UAAU,QAAQ;AAEtB,oBAAU,SAAS,WAAW,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI;AAC9D,oBAAU,SAAS,WAAW,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI;AAC9D,oBAAU,SAAS,WAAW,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI;AAI9D,oBAAU,SAAS,YAAY,IAAI,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AACzE,oBAAU,SAAS,YAAY,MAAM,SAAS,CAAC;AAC/C,iBAAO,UAAU,SAAS,cAAc;AAAA,YACtC,UAAU,SAAS;AAAA,YACnB,UAAU,SAAS;AAAA,YACnB,UAAU,SAAS;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAED,cAAU,SAAS,aAAa,IAAIF,cAAS;AAC7C,cAAU,SAAS,cAAc,IAAIC,iBAAY;AACjD,cAAU,SAAS,eAAe,IAAID,MAAO,QAAC,GAAG,GAAG,CAAC;AACrD,cAAU,SAAS,gBAAgB,IAAIG,cAAS;AAChD,cAAU,gBAAgB,WAAY;AACpC,WAAK,OAAO,CAAE;AACd,WAAK,SAAS;AACd,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,cAAc,CAAE;AACrB,WAAK,MAAM;AACX,WAAK,SAAS,SAAU,KAAK;AAC3B,aAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAED,WAAK,OAAO,WAAY;AACtB,aAAK,SAAU;AAEf,YAAI,KAAK,KAAK,SAAS;AAAG,eAAK,SAAS,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,EAAE;AAAA;AACnE,eAAK,SAAS;AAEnB,YAAI,CAAC,KAAK;AAAK;AAEf,iBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,KAAK,KAAK,KAAK;AAC/C,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACzC,gBAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,GAAG;AAC1B,mBAAK,YAAY,CAAC,IAAI;AACtB;AAAA,YACd,WAAuB,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI,KAAK,OAAO,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,EAAE,QAAQ,IAAI,KAAK,KAAK;AACxG,mBAAK,YAAY,CAAC,IAAI;AACtB;AAAA,YACD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAED,WAAK,iBAAiB,SAAU,MAAM;AACpC,YAAI,MAAM,KAAK;AACf,aAAK,SAAS,KAAK;AACnB,YAAI,QAAQ,KAAK,UAAU,CAAC,EAAE;AAC9B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,eAAK,OAAO,IAAI,UAAU,SAAS,IAAI,OAAO,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC;AAAA,QACvF;AAED,aAAK,KAAM;AAAA,MACZ;AAED,WAAK,mBAAmB,SAAU,MAAM;AACtC,YAAI,QAAQ,KAAK;AACjB,YAAI,MAAM,KAAK;AAEf,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,eAAK,OAAO,IAAI,UAAU,SAAS,IAAI,OAAO,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,MAAM,CAAC;AAAA,QAC9E;AAED,aAAK,KAAM;AAAA,MACZ;AAED,WAAK,WAAW,WAAY;AAC1B,aAAK,KAAK,KAAK,KAAK,WAAW;AAAA,MAChC;AAED,WAAK,cAAc,SAAU,GAAG,GAAG;AACjC,eAAO,EAAE,OAAO,EAAE;AAAA,MACnB;AAED,WAAK,QAAQ,WAAY;AACvB,YAAI,IAAI,IAAI,UAAU,cAAe;AACrC,UAAE,SAAS,KAAK;AAChB,UAAE,OAAO,KAAK;AACd,UAAE,SAAS,KAAK;AAEhB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACzC,YAAE,OAAO,KAAK,KAAK,CAAC,EAAE,OAAO;AAAA,QAC9B;AAED,UAAE,KAAM;AACR,eAAO;AAAA,MACR;AAED,WAAK,WAAW,SAAU,MAAM,aAAa;AAC3C,YAAI,CAAC;AAAa,wBAAc,UAAU;AAC1C,aAAK,SAAS,YAAY,MAAM,KAAK,MAAM;AAAA,MAC5C;AAED,WAAK,iBAAiB,SAAU,MAAM;AACpC,gBAAQ,KAAK;AACb,eAAO,KAAK,MAAM,IAAI;AACtB,eAAO,KAAK,YAAY,IAAI,KAAK;AAAA,MAClC;AAED,WAAK,UAAU,SAAU,MAAM;AAC7B,eAAO,KAAK,IAAI,IAAI;AACpB,YAAI,KAAK;AAAQ,iBAAQ,OAAO,KAAK,SAAU;AAC/C,YAAI,OAAO;AACX,YAAI,OAAO;AAEX,iBAAS,IAAI,KAAK,eAAe,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACjE,cAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,MAAM;AAC7B,mBAAO,KAAK,KAAK,CAAC;AAClB,mBAAO,KAAK,KAAK,CAAC;AAClB;AAAA,UACZ,WAAqB,KAAK,KAAK,CAAC,EAAE,OAAO,QAAQ,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,EAAE,OAAO,MAAM;AACvF,mBAAO,KAAK,KAAK,CAAC;AAClB,mBAAO,KAAK,KAAK,IAAI,CAAC;AACtB;AAAA,UACD,WAAU,KAAK,KAAK,CAAC,EAAE,OAAO,QAAQ,KAAK,KAAK,KAAK,SAAS,GAAG;AAChE,mBAAO,KAAK,KAAK,CAAC;AAClB,mBAAO,KAAK,KAAK,CAAC,EAAE,MAAO;AAC3B,iBAAK,QAAQ,KAAK,SAAS;AAC3B;AAAA,UACD;AAAA,QACF;AAED,YAAI,QAAQ,QAAQ,SAAS,MAAM;AACjC,eAAK,OAAO,mBAAmB;AAC/B,eAAK,OAAO,OAAO,KAAK,KAAK,KAAK,MAAM,IAAI,CAAC;AAC7C,eAAK,OAAO,yBAAyB;AACrC;AAAA,QACD;AAED,YAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,eAAK,OAAO,mBAAmB;AAC/B,eAAK,OAAO,OAAO,KAAK,KAAK,MAAM;AACnC,eAAK,OAAO,yBAAyB;AACrC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAED,cAAU,6BAA6B,SAAU,MAAM,QAAQ;AAC7D,eAAS,KAAK,MAAM,MAAM;AACxB,YAAI,KAAK,QAAQ;AAAM,iBAAO;AAE9B,iBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,cAAI,IAAI,KAAK,KAAK,SAAS,CAAC,GAAG,IAAI;AACnC,cAAI;AAAG,mBAAO;AAAA,QACf;AAED,eAAO;AAAA,MACR;AAED,aAAO,KAAK,MAAM,OAAO,IAAI;AAAA,IAC9B;AAED,cAAU,YAAY,WAAY;AAChC,WAAK,SAAS,CAAE;AAChB,WAAK,SAAS;AAEd,WAAK,WAAW,SAAU,OAAO;AAC/B,aAAK,OAAO,KAAK,KAAK;AACtB,aAAK,SAAS,KAAK,IAAI,MAAM,QAAQ,KAAK,MAAM;AAAA,MACjD;AAED,WAAK,UAAU,SAAU,MAAM;AAC7B,aAAK,OAAO;AAEZ,iBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ;AAAK,eAAK,OAAO,CAAC,EAAE,QAAQ,IAAI;AAAA,MACzE;AAED,WAAK,QAAQ,SAAU,QAAQ,aAAa;AAC1C,YAAI,CAAC;AAAa,wBAAc,UAAU;AAC1C,YAAI,IAAI,IAAI,UAAU,UAAW;AACjC,UAAE,SAAS;AACX,iBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,cAAI,QAAQ,KAAK,OAAO,CAAC,EAAE,MAAO;AAClC,gBAAM,SAAS,QAAQ,WAAW;AAClC,YAAE,SAAS,KAAK;AAAA,QACjB;AAED,eAAO;AAAA,MACR;AAAA,IACF;AAED,QAAI,wBAAwB;AAC5B,QAAI,uBAAuB;AAC3B,QAAI,yBAAyB;AAC7B,QAAI,sBAAsB;AAC1B,QAAI,0BAA0B;AAC9B,QAAI,uBAAuB;AAC3B,QAAI,sBAAsB;AAC1B,QAAI,2BAA2B;AAC/B,QAAI,sBAAsB;AAC1B,QAAI,0BAA0B;AAC9B,QAAI,kCAAkC;AACtC,QAAI,4BAA4B;AAChC,QAAI,0BAA0B;AAC9B,QAAI,0CAA0C;AAC9C,QAAI,gCAAgC;AACpC,QAAI,6BAA6B;AACjC,QAAI,8BAA8B;AAClC,QAAI,iCAAiC;AAAA,IAEzC;AAAA,IACA;AAAA,IACA;AACI,QAAI,4BAA4B;AAAA,IACpC;AAAA,IACA;AAAA,IACA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AACI,QAAI,qBAAqB;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AA+CI,QAAI,wBAAwB;AAyB5B,QAAI,wBAAwB;AAc5B,QAAI,wBAAwB;AAc5B,QAAI,yBAAyB;AAc7B,QAAI,eAAe;AAEnB,aAAS,yBAAyB,GAAG;AACnC,aAAO,iCAAiC;AAAA,IACzC;AAED,aAAS,sBAAsB,GAAG;AAChC,aAAO,8BAA8B;AAAA,IACtC;AAED,aAAS,UAAU,OAAO;AACxB,eAAS,KAAK,MAAM,SAAS;AAC3B,YAAI,OAAO,MAAM,QAAQ,CAAC;AAC1B,iBAAS,KAAK,KAAK,QAAQ;AACzB,cAAI,WAAW,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE,KAAK;AAClD,cAAI;AAAU,qBAAS,SAAS;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAED,aAAS,iBAAiB,MAAM,OAAO;AACrC,UAAI,WAAW,IAAIC,WAAM;AACzB,eAAS,OAAO,KAAK,KAAK,MAAM;AAChC,eAAS,YAAY,KAAK,KAAK,WAAW;AAC1C,eAAS,SAAS,KAAK,KAAK,QAAQ;AACpC,eAAS,WAAW,KAAK,KAAK,UAAU;AACxC,eAAS,MAAM,KAAK,KAAK,KAAK;AAC9B,YAAM;AACN,eAAS,OAAO,UAAU,KAAK,OAAO,MAAM,UAAU,SAAU;AAEhE,UAAI,CAAC,MAAM,cAAc,KAAK,IAAI;AAAG,cAAM,cAAc,KAAK,IAAI,IAAI,CAAE;AACxE,YAAM,cAAc,KAAK,IAAI,EAAE,KAAK,QAAQ;AAC5C,eAAS,KAAK,KAAK,UAAU;AAC3B,YAAI,QAAQ,iBAAiB,KAAK,SAAS,CAAC,GAAG,KAAK;AACpD,iBAAS,IAAI,KAAK;AAAA,MACnB;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAY,SAAS,SAAS;AACrC,UAAI,QAAQ,CAAE;AAEd,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAM,KAAK;AAAA,UACT,GAAG,QAAQ,CAAC;AAAA,UACZ,GAAG,QAAQ,CAAC;AAAA,QACtB,CAAS;AAAA,MACF;AAED,YAAM,KAAK,SAAU,GAAG,GAAG;AACzB,eAAO,EAAE,IAAI,EAAE;AAAA,MACvB,CAAO;AAED,aAAO,MAAM,SAAS,GAAG;AACvB,cAAM,KAAK;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACb,CAAS;AAAA,MACF;AAED,UAAI,MAAM,SAAS;AAAG,cAAM,SAAS;AACrC,UAAI,MAAM;AAEV,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAO,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE;AAAA,MAC9B;AAED,YAAM,KAAK,KAAK,GAAG;AAEnB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,IAAI;AAC1B,gBAAQ,CAAC,IAAI,MAAM,CAAC,EAAE;AACtB,gBAAQ,CAAC,IAAI,MAAM,CAAC,EAAE;AAAA,MACvB;AAAA,IACF;AAED,aAAS,iBAAiB,MAAM,MAAM;AACpC,UAAI,KAAK,KAAK,QAAQ,UAAU,IAAI,KAAK;AAAG,eAAO;AAEnD,eAAS,KAAK,KAAK,UAAU;AAC3B,YAAI,MAAM,iBAAiB,KAAK,SAAS,CAAC,GAAG,IAAI;AAEjD,YAAI;AAAK,iBAAO;AAAA,MACjB;AAED,aAAO;AAAA,IACR;AAED,UAAM,OAAO;AAAA,MACX,cAAc;AACZ,aAAK,kBAAkB;AACvB,aAAK,eAAe;AACpB,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,iBAAiB;AACtB,aAAK,YAAY,CAAE;AACnB,aAAK,WAAW,CAAE;AAClB,aAAK,YAAY,CAAE;AACnB,aAAK,cAAc,CAAE;AACrB,aAAK,UAAU,CAAC,EAAE;AAClB,aAAK,iBAAiB,CAAC,EAAE;AACzB,aAAK,SAAS,CAAE;AAChB,aAAK,SAAS,CAAE;AAChB,aAAK,kBAAkB,SAAU,OAAO;AACtC,cAAI,KAAK,OAAO,UAAU;AAAG;AAE7B,cAAI,WAAW,CAAE;AACjB,cAAI,eAAe,CAAE;AACrB,cAAI,eAAe,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE,KAAK;AAEtD,iBAAO,aAAa,WAAW,aAAa,QAAQ,QAAQ;AAC1D,2BAAe,aAAa;AAAA,UAC7B;AAED,cAAI,oBAAoB,aAAa,QAAQ,KAAK;AAClD,cAAI,wBAAwB,iBAAiB,mBAAmB,KAAK;AACrE,eAAK,UAAU,IAAI,qBAAqB;AAExC,mBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,gBAAI,OAAO,iBAAiB,uBAAuB,KAAK,OAAO,CAAC,EAAE,KAAK;AAEvE,gBAAI,MAAM;AACR,kBAAI,QAAQ;AACZ,uBAAS,KAAK,KAAK;AAEnB,2BAAa,KAAK,KAAK,OAAO,CAAC,EAAE,cAAc,SAAS;AAAA,YACtE,OAAmB;AACL,kBAAI,eAAe,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE,KAAK;AACtD,kBAAI,CAAC;AAAc;AACnB,kBAAI,oBAAoB,aAAa,QAAQ,KAAK;AAClD,kBAAI,wBAAwB,iBAAiB,mBAAmB,KAAK;AACrE,mBAAK,UAAU,IAAI,qBAAqB;AACxC,kBAAI,OAAO,iBAAiB,uBAAuB,KAAK,OAAO,CAAC,EAAE,KAAK;AACvE,kBAAI,QAAQ;AACZ,uBAAS,KAAK,KAAK;AAEnB,2BAAa,KAAK,KAAK,OAAO,CAAC,EAAE,cAAc,SAAS;AAAA,YACzD;AAAA,UACF;AAED,cAAI,WAAW,IAAIC,eAAS,UAAU,YAAY;AAElD,eAAK,UAAU,KAAK,UAAU,IAAIF,MAAO,QAAA,CAAE;AAC3C,eAAK,UAAU,SAAS,WAAW;AAAA,QACpC;AAED,aAAK,UAAU,SAAU,OAAO;AAC9B,cAAI,KAAK;AAAW,mBAAO,KAAK;AAChC,cAAI,WAAW,IAAIG,qBAAgB;AACnC,cAAI;AACJ,cAAI,MAAM,WAAW,KAAK,cAAc;AAAG,kBAAM,MAAM,WAAW,KAAK,cAAc,EAAE,QAAQ,KAAK;AAAA;AAC/F,kBAAM,IAAIC,MAAAA,oBAAqB;AACpC,mBAAS,SAAS,IAAIC,sBAAgB,IAAI,YAAY,KAAK,WAAW,GAAG,CAAC,CAAC;AAC3E,mBAAS,aAAa,YAAY,IAAIA,MAAe,gBAAC,KAAK,eAAe,CAAC,CAAC;AAC5E,cAAI,KAAK,iBAAiB,KAAK,cAAc,SAAS,GAAG;AACvD,qBAAS,aAAa,UAAU,IAAIA,MAAe,gBAAC,KAAK,eAAe,CAAC,CAAC;AAAA,UAC3E;AACD,cAAI,KAAK,gBAAgB,KAAK,aAAa,SAAS,GAAG;AACrD,qBAAS,aAAa,SAAS,IAAIA,MAAe,gBAAC,KAAK,cAAc,CAAC,CAAC;AAAA,UACzE;AACD,cAAI,KAAK,kBAAkB,CAAC,KAAK,KAAK,kBAAkB,CAAC,EAAE,SAAS,GAAG;AACrE,qBAAS,aAAa,MAAM,IAAIA,MAAe,gBAAC,IAAI,aAAa,KAAK,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA,UAChG;AACD,cAAI,KAAK,kBAAkB,CAAC,KAAK,KAAK,kBAAkB,CAAC,EAAE,SAAS,GAAG;AACrE,qBAAS,aAAa,OAAO,IAAIA,MAAe,gBAAC,IAAI,aAAa,KAAK,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA,UACjG;AACD,cAAI,KAAK,kBAAkB,KAAK,eAAe,SAAS,GAAG;AACzD,qBAAS,aAAa,YAAY,IAAIA,MAAe,gBAAC,KAAK,gBAAgB,CAAC,CAAC;AAAA,UAC9E;AACD,cAAI,KAAK,oBAAoB,KAAK,iBAAiB,SAAS,GAAG;AAC7D,qBAAS,aAAa,cAAc,IAAIA,MAAe,gBAAC,KAAK,kBAAkB,CAAC,CAAC;AAAA,UAClF;AACD,cAAI,KAAK,OAAO,SAAS,GAAG;AAC1B,gBAAI,UAAU,CAAE;AAChB,gBAAI,QAAQ,CAAE;AAEd,qBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,uBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,CAAC,EAAE,SAAS,QAAQ,KAAK;AACvD,oBAAI,SAAS,KAAK,OAAO,CAAC,EAAE,SAAS,CAAC;AACtC,oBAAI,QAAQ;AACV,sBAAI,CAAC,QAAQ,OAAO,SAAS;AAAG,4BAAQ,OAAO,SAAS,IAAI,CAAE;AAC9D,sBAAI,CAAC,MAAM,OAAO,SAAS;AAAG,0BAAM,OAAO,SAAS,IAAI,CAAE;AAC1D,0BAAQ,OAAO,SAAS,EAAE,KAAK,OAAO,OAAO;AAC7C,wBAAM,OAAO,SAAS,EAAE,KAAK,SAAS,CAAC,CAAC;AAAA,gBACzC;AAAA,cACF;AAAA,YACF;AAED,qBAAS,KAAK,OAAO;AACnB,0BAAY,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC;AAAA,YACjC;AAED,gBAAI,WAAW,CAAE;AACjB,gBAAI,SAAS,CAAE;AAEf,qBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,oBAAI,QAAQ,CAAC,KAAK,MAAM,CAAC,GAAG;AAC1B,2BAAS,KAAK,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC3B,yBAAO,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC;AAAA,gBACzC,OAAuB;AACL,2BAAS,KAAK,CAAC;AACf,yBAAO,KAAK,CAAC;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAED,qBAAS,aAAa,cAAc,IAAIA,MAAAA,gBAAgB,IAAI,aAAa,QAAQ,GAAG,YAAY,CAAC;AACjG,qBAAS,aAAa,aAAa,IAAIA,MAAAA,gBAAgB,IAAI,aAAa,MAAM,GAAG,YAAY,CAAC;AAAA,UAC/F;AAED,cAAI;AAEJ,cAAI,KAAK,OAAO,UAAU;AAAG,mBAAO,IAAIC,MAAAA,KAAK,UAAU,GAAG;AAE1D,cAAI,KAAK,OAAO,SAAS,GAAG;AAC1B,mBAAO,IAAIC,MAAAA,YAAY,UAAU,GAAG;AACpC,iBAAK,qBAAsB;AAAA,UAC5B;AAED,eAAK,YAAY;AAEjB,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAED,UAAM,OAAO;AAAA,MACX,cAAc;AACZ,aAAK,cAAc;AACnB,aAAK,WAAW,CAAE;AAAA,MACnB;AAAA,IACF;AAED,UAAM,WAAW;AAAA,MACf,cAAc;AACZ,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AAET,aAAK,UAAU,WAAY;AACzB,iBAAO,IAAIV,MAAO,QAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,QAC1C;AAAA,MACF;AAAA,IACF;AAED,UAAM,UAAU;AAAA,MACd,cAAc;AACZ,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,UAAU,WAAY;AACzB,iBAAO,IAAIW,MAAK,MAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAED,UAAM,aAAa;AAAA,MACjB,cAAc;AACZ,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,UAAU,WAAY;AACzB,iBAAO,IAAIV,MAAAA,WAAW,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,QACrD;AAAA,MACF;AAAA,IACF;AAED,UAAM,eAAe;AAAA,MACnB,cAAc;AACZ,aAAK,YAAY;AACjB,aAAK,UAAU;AAAA,MAChB;AAAA,IACF;AAED,UAAM,SAAS;AAAA,MACb,cAAc;AACZ,aAAK,OAAO,CAAE;AACd,aAAK,WAAW,WAAY;AAC1B,cAAI,MAAM;AACV,eAAK,KAAK,QAAQ,SAAU,GAAG;AAC7B,mBAAO,OAAO,aAAa,CAAC;AAAA,UACxC,CAAW;AACD,iBAAO,IAAI,QAAQ,kBAAkB,EAAE;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAED,UAAM,YAAY;AAAA,MAChB,cAAc;AACZ,aAAK,QAAQ;AACb,aAAK,SAAS;AAAA,MACf;AAAA,IACF;AAED,UAAM,UAAU;AAAA,MACd,cAAc;AACZ,aAAK,QAAQ;AACb,aAAK,SAAS;AAAA,MACf;AAAA,IACF;AAED,UAAM,OAAO;AAAA,MACX,cAAc;AACZ,aAAK,QAAQ;AACb,aAAK,kBAAkB,CAAE;AACzB,aAAK,eAAe;AACpB,aAAK,aAAa;AAClB,aAAK,UAAU,CAAE;AACjB,aAAK,YAAY,CAAE;AACnB,aAAK,UAAU,SAAU,OAAO;AAC9B,cAAI,KAAK;AAAW,mBAAO,KAAK;AAChC,cAAI,IAAI,IAAIW,eAAU;AACtB,YAAE,OAAO,KAAK;AACd,YAAE,SAAS,KAAK,gBAAgB,QAAS;AAEzC,mBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,cAAE,IAAI,KAAK,UAAU,CAAC,EAAE,QAAQ,KAAK,CAAC;AAAA,UACvC;AAED,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,cAAE,IAAI,MAAM,QAAQ,KAAK,QAAQ,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC;AAAA,UACpD;AAED,eAAK,YAAY;AAEjB,YAAE,OAAO,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,KAAK;AACpD,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAED,UAAM,OAAO;AAAA,MACX,cAAc;AACZ,aAAK,QAAQ;AACb,aAAK,cAAc;AACnB,aAAK,gBAAgB;AAAA,MACtB;AAAA,IACF;AAED,UAAM,mBAAmB;AAAA,MACvB,cAAc;AACZ,aAAK,OAAO;AACZ,aAAK,YAAY;AACjB,aAAK,SAAS;AACd,aAAK,QAAQ,CAAE;AACf,aAAK,cAAc;AACnB,aAAK,QAAQ;AACb,aAAK,cAAc,WAAY;AAC7B,cAAI,QAAQ,IAAI,WAAW,KAAK,KAAK,EAAE;AACvC,cAAI,SAAS,IAAI,SAAS,KAAK;AAC/B,cAAI,IAAI,OAAO,WAAW,GAAG,IAAI;AACjC,cAAI,IAAI,OAAO,WAAW,GAAG,IAAI;AACjC,cAAI,IAAI,OAAO,WAAW,GAAG,IAAI;AAEjC,iBAAO,IAAID,MAAK,MAAC,GAAG,GAAG,CAAC;AAAA,QACzB;AAED,aAAK,cAAc,WAAY;AAC7B,cAAI,QAAQ,IAAI,WAAW,KAAK,KAAK,EAAE;AACvC,cAAI,SAAS,IAAI,SAAS,KAAK;AAC/B,cAAI,IAAI,OAAO,WAAW,GAAG,IAAI;AACjC,iBAAO;AAAA,QACR;AAED,aAAK,aAAa,WAAY;AAC5B,cAAI,QAAQ,IAAI,WAAW,KAAK,KAAK,EAAE;AACvC,cAAI,SAAS,IAAI,SAAS,KAAK;AAC/B,cAAI,IAAI,OAAO,WAAW,GAAG,IAAI;AACjC,iBAAO,CAAC,CAAC;AAAA,QACV;AAED,aAAK,eAAe,WAAY;AAC9B,cAAI,IAAI,IAAI,SAAU;AACtB,YAAE,OAAO,KAAK;AACd,iBAAO,EAAE,SAAU;AAAA,QACpB;AAED,aAAK,YAAY,WAAY;AAC3B,cAAI,IAAI,IAAI,SAAU;AACtB,YAAE,OAAO,KAAK;AACd,cAAIE,QAAO,EAAE,SAAU;AACvB,UAAAA,QAAOA,MAAK,QAAQ,OAAO,GAAG;AAE9B,cAAIA,MAAK,QAAQ,GAAG,KAAK,IAAI;AAC3B,YAAAA,QAAOA,MAAK,OAAOA,MAAK,YAAY,GAAG,IAAI,CAAC;AAAA,UAC7C;AAED,iBAAO,cAAc,KAAKA,KAAI;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAED,QAAI,kBAAkB;AAAA,MACpB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACd;AAED,QAAI,kBAAkB;AAAA,MACpB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACd;AAED,UAAM,WAAW;AAAA,MACf,cAAc;AACZ,aAAK,gBAAgB;AACrB,aAAK,iBAAiB;AACtB,aAAK,cAAc,CAAE;AACrB,aAAK,UAAU,WAAY;AACzB,cAAI,MAAM,IAAIC,wBAAmB;AAEjC,mBAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,gBAAI,gBAAgB,KAAK,YAAY,CAAC,EAAE,IAAI,KAAK,SAAS;AACxD,kBAAI,gBAAgB,KAAK,YAAY,CAAC,EAAE,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,YAAa;AAAA,YACnF;AACD,gBAAI,gBAAgB,KAAK,YAAY,CAAC,EAAE,IAAI,KAAK,SAAS;AACxD,kBAAI,gBAAgB,KAAK,YAAY,CAAC,EAAE,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,YAAa;AAAA,YACnF;AACD,gBAAI,gBAAgB,KAAK,YAAY,CAAC,EAAE,IAAI,KAAK,QAAQ;AACvD,kBAAI,gBAAgB,KAAK,YAAY,CAAC,EAAE,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,WAAY;AAAA,YAClF;AACD,gBAAI,gBAAgB,KAAK,YAAY,CAAC,EAAE,IAAI,KAAK,UAAU;AACzD,kBAAI,gBAAgB,KAAK,YAAY,CAAC,EAAE,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,aAAc;AAAA,YACpF;AACD,gBAAI,gBAAgB,KAAK,YAAY,CAAC,EAAE,IAAI,KAAK,OAAO;AACtD,kBAAI,OAAO,KAAK,YAAY,CAAC;AAC7B,kBAAI,KAAK,aAAa;AAAuB,oBAAI,MAAM,KAAK,YAAY,CAAC,EAAE,UAAW;AACtF,kBAAI,KAAK,aAAa;AAAuB,oBAAI,YAAY,KAAK,YAAY,CAAC,EAAE,UAAW;AAC5F,kBAAI,KAAK,aAAa;AAAwB,oBAAI,WAAW,KAAK,YAAY,CAAC,EAAE,UAAW;AAC5F,kBAAI,KAAK,aAAa;AAAuB,oBAAI,WAAW,KAAK,YAAY,CAAC,EAAE,UAAW;AAAA,YAC5F;AAAA,UACF;AAED,cAAI,QAAQ,IAAI;AAChB,cAAI,QAAQ,IAAI;AAChB,cAAI,QAAQ,IAAI;AAChB,cAAI,MAAM,IAAI;AACd,cAAI,MAAM,IAAI;AACd,cAAI,MAAM,IAAI;AACd,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAED,aAAS,QAAQ,IAAI,IAAI,GAAG;AAC1B,UAAI,IAAI,IAAId,cAAS;AACrB,UAAI,MAAM,IAAI;AACd,QAAE,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI;AACxB,QAAE,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI;AACxB,QAAE,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI;AACxB,aAAO;AAAA,IACR;AAED,aAAS,SAAS,IAAI,IAAI,GAAG;AAC3B,aAAO,GAAG,MAAO,EAAC,MAAM,IAAI,IAAI,CAAC;AAAA,IAClC;AAED,aAAS,YAAY,MAAM,MAAM,KAAK,MAAM;AAC1C,UAAI,KAAK,UAAU;AAAG,eAAO,KAAK,CAAC,EAAE,OAAO,QAAS;AAErD,UAAI,OAAO;AACX,UAAI,MAAM;AACV,UAAI,UAAU;AAEd,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,WAAW,KAAK,IAAI,KAAK,CAAC,EAAE,QAAQ,IAAI;AAE5C,YAAI,WAAW,QAAQ,KAAK,CAAC,EAAE,SAAS,MAAM;AAC5C,iBAAO;AACP,gBAAM,KAAK,CAAC;AACZ,oBAAU,KAAK,IAAI,CAAC;AAAA,QACrB;AAAA,MACF;AAED,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACR,WAAU,SAAS;AAClB,YAAI,KAAK,QAAQ,QAAQ,IAAI;AAC7B,YAAI,IAAI,IAAI,QAAQ;AACpB,YAAI,IAAI,IAAI;AAEZ,eAAO,KAAK,IAAI,OAAO,QAAO,GAAI,QAAQ,OAAO,QAAS,GAAE,CAAC;AAAA,MACrE,OAAa;AACL,kBAAU,KAAK,CAAC,EAAE,MAAO;AACzB,gBAAQ,SAAS;AAEjB,YAAI,KAAK,QAAQ,QAAQ,IAAI;AAC7B,YAAI,IAAI,IAAI,QAAQ;AACpB,YAAI,IAAI,IAAI;AAEZ,eAAO,KAAK,IAAI,OAAO,QAAO,GAAI,QAAQ,OAAO,QAAS,GAAE,CAAC;AAAA,MAC9D;AAAA,IACF;AAED,UAAM,WAAW;AAAA,MACf,cAAc;AACZ,aAAK,YAAY;AACjB,aAAK,mBAAmB;AACxB,aAAK,mBAAmB;AACxB,aAAK,kBAAkB;AACvB,aAAK,gBAAgB,CAAE;AACvB,aAAK,gBAAgB,CAAE;AACvB,aAAK,eAAe,CAAE;AACtB,aAAK,YAAY;AACjB,aAAK,aAAa;AAClB,aAAK,OAAO,SAAU,KAAK;AACzB,cAAI,CAAC;AAAK,kBAAM;AAEhB,mBAAS,EAAEe,IAAG;AACZ,YAAAA,GAAE,SAAS;AAAA,UACZ;AAED,eAAK,cAAc,QAAQ,CAAC;AAC5B,eAAK,cAAc,QAAQ,CAAC;AAC5B,eAAK,aAAa,QAAQ,CAAC;AAAA,QAC5B;AAED,aAAK,WAAW,WAAY;AAC1B,mBAAS,KAAK,GAAG,GAAG;AAClB,mBAAO,EAAE,QAAQ,EAAE;AAAA,UACpB;AAED,eAAK,cAAc,KAAK,IAAI;AAC5B,eAAK,cAAc,KAAK,IAAI;AAC5B,eAAK,aAAa,KAAK,IAAI;AAAA,QAC5B;AAED,aAAK,YAAY,WAAY;AAC3B,iBAAO,KAAK;AAAA,YACV,KAAK,IAAI;AAAA,cACP;AAAA,cACA,KAAK,cAAc,IAAI,SAAU,GAAG;AAClC,uBAAO,EAAE;AAAA,cACzB,CAAe;AAAA,YACF;AAAA,YACD,KAAK,IAAI;AAAA,cACP;AAAA,cACA,KAAK,cAAc,IAAI,SAAU,GAAG;AAClC,uBAAO,EAAE;AAAA,cACzB,CAAe;AAAA,YACF;AAAA,YACD,KAAK,IAAI;AAAA,cACP;AAAA,cACA,KAAK,aAAa,IAAI,SAAU,GAAG;AACjC,uBAAO,EAAE;AAAA,cACzB,CAAe;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAED,aAAK,UAAU,SAAU,GAAG;AAC1B,eAAK,SAAU;AACf,cAAI,SAAS,KAAK,UAAW;AAC7B,cAAI,QAAQ,IAAI,UAAU,cAAe;AAEzC,mBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,MAAM;AACrC,gBAAI,SAAS,IAAIZ,cAAS;AAC1B,gBAAI,OAAO;AACX,gBAAI,MAAM,YAAY,KAAK,eAAe,MAAM,QAAQ,OAAO;AAC/D,gBAAI,QAAQ,YAAY,KAAK,cAAc,MAAM,QAAQ,OAAO;AAChE,gBAAI,WAAW,YAAY,KAAK,eAAe,MAAM,QAAQ,QAAQ;AACrE,mBAAO,QAAQ,KAAK,UAAU,KAAK;AAEnC,gBAAI,MAAM,IAAI,UAAU,SAAS,MAAM,MAAM;AAC7C,kBAAM,OAAO,GAAG;AAAA,UACjB;AAED,gBAAM,SAAS,EAAE,SAAS,KAAK,SAAS,EAAE,QAAS;AAEnD,cAAI,SAAS,CAAC,KAAK;AAEnB,cAAI,EAAE,cAAc,KAAK,SAAS,GAAG;AACnC,qBAAS,IAAI,GAAG,IAAI,EAAE,cAAc,KAAK,SAAS,EAAE,QAAQ,KAAK;AAC/D,kBAAI,KAAK,MAAM,MAAO;AACtB,iBAAG,SAAS,EAAE,cAAc,KAAK,SAAS,EAAE,CAAC;AAC7C,qBAAO,KAAK,EAAE;AAAA,YACf;AAAA,UACF;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAED,UAAM,YAAY;AAAA,MAChB,cAAc;AACZ,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,kBAAkB;AACvB,aAAK,eAAe;AACpB,aAAK,YAAY,CAAE;AACnB,aAAK,UAAU,SAAU,MAAM;AAC7B,cAAI,kBAAkB,IAAI,UAAU,UAAW;AAE/C,mBAAS,KAAK,KAAK,WAAW;AAC5B,iBAAK,UAAU,CAAC,EAAE,KAAK,KAAK,eAAe;AAE3C,gBAAI,SAAS,KAAK,UAAU,CAAC,EAAE,QAAQ,IAAI;AAE3C,qBAAS,KAAK,QAAQ;AACpB,qBAAO,CAAC,EAAE,KAAM;AAChB,8BAAgB,SAAS,OAAO,CAAC,CAAC;AAAA,YACnC;AAAA,UACF;AAED,0BAAgB,SAAS,KAAK,IAAI;AAAA,YAChC;AAAA,YACA,gBAAgB,OAAO,IAAI,SAAU,GAAG;AACtC,qBAAO,EAAE;AAAA,YACvB,CAAa;AAAA,UACF;AACD,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAED,UAAM,UAAU;AAAA,MACd,cAAc;AACZ,aAAK,SAAS;AACd,aAAK,UAAU;AACf,aAAK,mBAAmB,CAAE;AAC1B,aAAK,SAAS,CAAE;AAAA,MACjB;AAAA,IACF;AAED,UAAM,QAAQ;AAAA,MACZ,cAAc;AACZ,aAAK,QAAQ;AACb,aAAK,QAAQ;AACb,aAAK,uBAAuB;AAC5B,aAAK,qBAAqB;AAC1B,aAAK,wBAAwB;AAC7B,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AACvB,aAAK,gBAAgB;AACrB,aAAK,iBAAiB;AACtB,aAAK,gBAAgB;AAAA,MACtB;AAAA,IACF;AAED,UAAM,SAAS;AAAA,MACb,cAAc;AACZ,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,aAAK,MAAM;AACX,aAAK,iBAAiB;AACtB,aAAK,iBAAiB;AACtB,aAAK,gBAAgB;AACrB,aAAK,UAAU;AAAA,MAChB;AAAA,IACF;AAED,UAAM,QAAQ;AAAA,MACZ,cAAc;AACZ,aAAK,eAAe;AACpB,aAAK,eAAe;AACpB,aAAK,kBAAkB;AACvB,aAAK,eAAe;AACpB,aAAK,SAAS;AACd,aAAK,aAAa;AAClB,aAAK,gBAAgB;AACrB,aAAK,iBAAiB;AACtB,aAAK,eAAe;AACpB,aAAK,aAAa;AAClB,aAAK,cAAc;AACnB,aAAK,YAAY;AACjB,aAAK,UAAU,CAAE;AACjB,aAAK,aAAa,CAAE;AACpB,aAAK,cAAc,CAAE;AACrB,aAAK,UAAU,CAAE;AACjB,aAAK,WAAW,CAAE;AAClB,aAAK,gBAAgB,CAAE;AACvB,aAAK,WAAW,SAAU,MAAM,MAAM;AACpC,cAAI,CAAC,MAAM;AACT,mBAAO,KAAK;AAAA,UACb;AAED,cAAI,KAAK,SAAS,MAAM;AACtB,mBAAO;AAAA,UACR;AAED,mBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,gBAAI,MAAM,KAAK,SAAS,MAAM,KAAK,UAAU,CAAC,CAAC;AAC/C,gBAAI;AAAK,qBAAO;AAAA,UACjB;AAED,iBAAO;AAAA,QACR;AAED,aAAK,UAAU,WAAY;AACzB,eAAK,YAAY;AAEjB,oBAAU,IAAI;AAEd,cAAI,IAAI,KAAK,UAAU,QAAQ,IAAI;AAEnC,mBAAS,KAAK,KAAK;AAAS,iBAAK,QAAQ,CAAC,EAAE,gBAAgB,IAAI;AAEhE,cAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,gBAAI,IAAI,KAAK,YAAY,CAAC,EAAE,QAAQ,IAAI;AAAA,UACzC;AAED,iBAAO,EAAE,QAAQ,GAAG,WAAW,EAAG;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAED,UAAM,UAAU;AAAA,MACd,cAAc;AACZ,aAAK,WAAW,CAAC,CAAA,GAAI,CAAA,GAAI,CAAA,GAAI,CAAA,CAAE;AAC/B,aAAK,UAAU,WAAY;AACzB,cAAI,IAAI,IAAIA,cAAS;AAErB,mBAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,qBAAS,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI;AAC7B,gBAAE,SAAS,IAAI,IAAI,EAAE,IAAI,KAAK,SAAS,EAAE,EAAE,CAAC;AAAA,YAC7C;AAAA,UACF;AAED,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAED,QAAI,eAAe;AAEnB,aAAS,UAAU,UAAU;AAC3B,UAAI,MAAM,SAAS,WAAW,SAAS,YAAY,YAAY;AAC/D,eAAS,cAAc;AACvB,aAAO;AAAA,IACR;AAED,aAAS,YAAY,UAAU;AAC7B,UAAI,MAAM,SAAS,WAAW,SAAS,YAAY,YAAY;AAC/D,eAAS,cAAc;AACvB,aAAO;AAAA,IACR;AAED,aAAS,aAAa,UAAU;AAC9B,UAAI,MAAM,SAAS,SAAS,SAAS,UAAU;AAC/C,eAAS,cAAc;AACvB,aAAO;AAAA,IACR;AAED,aAAS,cAAc,UAAU;AAC/B,UAAI,MAAM,SAAS,UAAU,SAAS,YAAY,YAAY;AAC9D,eAAS,cAAc;AACvB,aAAO;AAAA,IACR;AAED,aAAS,kBAAkB,UAAU;AACnC,UAAI,MAAM,SAAS,UAAU,SAAS,YAAY,YAAY;AAC9D,eAAS,cAAc;AACvB,aAAO;AAAA,IACR;AAED,aAAS,cAAc,UAAU;AAC/B,UAAI,MAAM,SAAS,UAAU,SAAS,YAAY,YAAY;AAC9D,eAAS,cAAc;AACvB,aAAO;AAAA,IACR;AAED,aAAS,gBAAgB,QAAQ;AAC/B,UAAI,IAAI,IAAI,WAAY;AACxB,QAAE,IAAI,UAAU,MAAM;AACtB,QAAE,IAAI,UAAU,MAAM;AACtB,QAAE,IAAI,UAAU,MAAM;AACtB,aAAO;AAAA,IACR;AAED,aAAS,eAAe,QAAQ;AAC9B,UAAI,IAAI,IAAI,UAAW;AACvB,QAAE,IAAI,UAAU,MAAM;AACtB,QAAE,IAAI,UAAU,MAAM;AACtB,QAAE,IAAI,UAAU,MAAM;AACtB,aAAO;AAAA,IACR;AAED,aAAS,kBAAkB,QAAQ;AACjC,UAAI,IAAI,IAAI,aAAc;AAC1B,QAAE,IAAI,UAAU,MAAM;AACtB,QAAE,IAAI,UAAU,MAAM;AACtB,QAAE,IAAI,UAAU,MAAM;AACtB,QAAE,IAAI,UAAU,MAAM;AACtB,aAAO;AAAA,IACR;AAED,aAAS,cAAc,QAAQ;AAC7B,UAAI,IAAI,IAAI,SAAU;AACtB,UAAI,oBAAoB,kBAAkB,MAAM;AAChD,aAAO,UAAU,EAAE,MAAM,GAAG,iBAAiB;AAC7C,aAAO,EAAE,SAAU;AAAA,IACpB;AAED,aAAS,oBAAoB,QAAQ;AACnC,UAAI,IAAI,IAAI,eAAgB;AAC5B,QAAE,YAAY,kBAAkB,MAAM;AACtC,QAAE,UAAU,UAAU,MAAM;AAC5B,aAAO;AAAA,IACR;AAED,aAAS,iBAAiB,QAAQ;AAChC,UAAI,IAAI,IAAI,UAAW;AAEvB,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,iBAAS,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI;AAC7B,YAAE,SAAS,CAAC,EAAE,EAAE,IAAI,UAAU,MAAM;AAAA,QACrC;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,iBAAiB,QAAQ;AAChC,UAAI,IAAI,IAAI,YAAa;AACzB,QAAE,QAAQ,YAAY,MAAM;AAC5B,QAAE,SAAS,gBAAgB,MAAM;AACjC,aAAO;AAAA,IACR;AAED,aAAS,eAAe,QAAQ;AAC9B,UAAI,IAAI,IAAI,UAAW;AACvB,QAAE,QAAQ,YAAY,MAAM;AAC5B,QAAE,SAAS,kBAAkB,MAAM;AACnC,aAAO;AAAA,IACR;AAED,aAAS,yBAAyB,QAAQ,MAAM,MAAM;AACpD,eAAS,IAAI,GAAG,IAAI,MAAM;AAAK,aAAK,CAAC,IAAI,oBAAoB,MAAM;AAAA,IACpE;AAED,aAAS,sBAAsB,QAAQ,MAAM,MAAM;AACjD,eAAS,IAAI,GAAG,IAAI,MAAM;AAAK,aAAK,CAAC,IAAI,iBAAiB,MAAM;AAAA,IACjE;AAED,aAAS,oBAAoB,QAAQ,MAAM,MAAM;AAC/C,eAAS,IAAI,GAAG,IAAI,MAAM;AAAK,aAAK,CAAC,IAAI,eAAe,MAAM;AAAA,IAC/D;AAED,aAAS,WAAW,QAAQ,GAAS,GAAG;AAEtC,aAAO,OAAO,KAAK,OAAO,CAAC,IAAI,GAAG,YAAY;AAAA,IAC/C;AAED,aAAS,UAAU,MAAM;AACvB,UAAI,CAAC;AAAM,cAAM;AAAA,IAClB;AAED,aAAS,eAAe,QAAQ,QAAQ,OAAO;AAC7C,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,mBAAmB;AAExC,oBAAc,MAAM;AACpB,UAAI,OAAO,IAAI,OAAQ;AACvB,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,QAAQ,cAAc,MAAM;AACjC,WAAK,kBAAkB,iBAAiB,MAAM;AAC9C,WAAK,eAAe,kBAAkB,MAAM;AAC5C,WAAK,aAAa,kBAAkB,MAAM;AAE1C,UAAI,KAAK,YAAY;AACnB,aAAK,UAAU,CAAE;AAEjB,iBAAS,IAAI,GAAG,IAAI,KAAK,YAAY,EAAE,GAAG;AACxC,eAAK,QAAQ,CAAC,IAAI,kBAAkB,MAAM;AAAA,QAC3C;AAAA,MACF;AAED,UAAI,KAAK,cAAc;AACrB,aAAK,YAAY,CAAE;AAEnB,iBAAS,IAAI,GAAG,IAAI,KAAK,cAAc,EAAE,GAAG;AAC1C,cAAI,QAAQ,eAAe,QAAQ,MAAM,OAAO;AAChD,eAAK,UAAU,CAAC,IAAI;AAAA,QACrB;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAID,aAAS,eAAe,QAAQ,GAAG;AACjC,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,mBAAmB;AAExC,oBAAc,MAAM;AACpB,QAAE,QAAQ,cAAc,MAAM;AAC9B,QAAE,cAAc,kBAAkB,MAAM;AACxC,QAAE,gBAAgB,iBAAiB,MAAM;AAGzC,UAAI,WAAW;AACb,mBAAW,QAAQ,EAAE,UAAU,EAAE,WAAW;AAAA,MACpD,OAAa;AAGL,UAAE,WAAW,CAAE;AACf,iCAAyB,QAAQ,EAAE,UAAU,EAAE,WAAW;AAAA,MAC3D;AAED,aAAO;AAAA,IACR;AAED,aAAS,eAAe,QAAQ,MAAM;AACpC,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,mBAAmB;AAExC,oBAAc,MAAM;AACpB,WAAK,kBAAkB,kBAAkB,MAAM;AAC/C,WAAK,eAAe,kBAAkB,MAAM;AAC5C,WAAK,YAAY,kBAAkB,MAAM;AACzC,WAAK,YAAY,kBAAkB,MAAM;AACzC,WAAK,iBAAiB,kBAAkB,MAAM;AAC9C,WAAK,mBAAmB,CAAE;AAE1B,UAAI,IAAI,kBAAkB,MAAM;AAEhC,UAAI,IAAI,2BAA2B;AACjC,YAAI,WAAW;AACb,qBAAW,QAAQ,KAAK,WAAW,KAAK,YAAY;AAAA,QAC9D,OAAe;AAGL,eAAK,YAAY,CAAE;AACnB,eAAK,gBAAgB,OAAO,WAAW,OAAO,YAAY,OAAO,aAAa,KAAK,eAAe,IAAI,CAAC;AACvG,iBAAO,KAAK,KAAK,eAAe,IAAI,GAAG,YAAY;AAAA,QACpD;AAAA,MACF;AAED,UAAI,IAAI,yBAAyB;AAC/B,YAAI,WAAW;AACb,qBAAW,QAAQ,KAAK,UAAU,KAAK,YAAY;AAAA,QAC7D,OAAe;AAGL,eAAK,WAAW,CAAE;AAClB,eAAK,gBAAgB,OAAO,WAAW,OAAO,YAAY,OAAO,aAAa,KAAK,eAAe,IAAI,CAAC;AACvG,iBAAO,KAAK,KAAK,eAAe,IAAI,GAAG,YAAY;AAAA,QACpD;AAAA,MACF;AAED,UAAI,IAAI,yCAAyC;AAC/C,YAAI,WAAW;AACb,qBAAW,QAAQ,KAAK,WAAW,KAAK,YAAY;AACpD,qBAAW,QAAQ,KAAK,aAAa,KAAK,YAAY;AAAA,QAChE,OAAe;AAGL,eAAK,YAAY,CAAE;AACnB,eAAK,iBAAiB,OAAO,WAAW,OAAO,YAAY,OAAO,aAAa,KAAK,eAAe,IAAI,CAAC;AACxG,iBAAO,KAAK,KAAK,eAAe,IAAI,GAAG,YAAY;AACnD,eAAK,cAAc,CAAE;AACrB,eAAK,mBAAmB,OAAO,WAAW,OAAO,YAAY,OAAO,aAAa,KAAK,eAAe,IAAI,CAAC;AAC1G,iBAAO,KAAK,KAAK,eAAe,IAAI,GAAG,YAAY;AAAA,QACpD;AAAA,MACF;AAED,eAAS,IAAI,GAAG,IAAI,6BAA6B,EAAE,GAAG;AACpD,YAAI,EAAE,IAAI,sBAAsB,CAAC;AAAI;AAErC,YAAI,WAAW;AACb,qBAAW,QAAQ,KAAK,QAAQ,CAAC,GAAG,KAAK,YAAY;AAAA,QAC/D,OAAe;AAGL,eAAK,QAAQ,CAAC,IAAI,CAAE;AACpB,eAAK,eAAe,OAAO,WAAW,OAAO,YAAY,OAAO,aAAa,KAAK,eAAe,IAAI,CAAC;AACtG,iBAAO,KAAK,KAAK,eAAe,IAAI,GAAG,YAAY;AAAA,QACpD;AAAA,MACF;AAED,WAAK,oBAAoB,CAAE;AAE3B,eAAS,IAAI,GAAG,IAAI,gCAAgC,EAAE,GAAG;AACvD,YAAI,EAAE,IAAI,yBAAyB,CAAC;AAAI;AAGxC,aAAK,iBAAiB,CAAC,IAAI,kBAAkB,MAAM;AAEnD,YAAI,WAAW;AACb,qBAAW,QAAQ,KAAK,eAAe,CAAC,GAAG,KAAK,YAAY;AAAA,QACtE,OAAe;AAGL,eAAK,eAAe,CAAC,IAAI,CAAE;AAE3B,eAAK,kBAAkB,CAAC,IAAI,CAAE;AAE9B,mBAAS,KAAK,GAAG,KAAK,KAAK,cAAc,MAAM;AAC7C,iBAAK,kBAAkB,CAAC,EAAE,KAAK,UAAU,MAAM,CAAC;AAChD,iBAAK,kBAAkB,CAAC,EAAE,KAAK,UAAU,MAAM,CAAC;AAChD,sBAAU,MAAM;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAMD,UAAI,WAAW;AACb,0BAAkB,MAAM;AAAA,MAChC,OAAa;AAIL,aAAK,SAAS,CAAE;AAChB,aAAK,cAAc,CAAE;AAErB,iBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,EAAE,GAAG;AACvC,cAAI,IAAK,KAAK,OAAO,CAAC,IAAI,IAAI;AAE9B,YAAE,cAAc,cAAc,MAAM;AACpC,YAAE,WAAW,CAAE;AAEf,mBAAS,IAAI,GAAG,IAAI,EAAE,aAAa,EAAE,GAAG;AACtC,gBAAI,KAAK,eAAe,KAAK,IAAI;AAC/B,gBAAE,SAAS,CAAC,IAAI,cAAc,MAAM;AAAA,YAClD,OAAmB;AACL,gBAAE,SAAS,CAAC,IAAI,kBAAkB,MAAM;AAAA,YACzC;AAAA,UACF;AAED,cAAI,EAAE,gBAAgB,GAAG;AACvB,iBAAK,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC;AACnC,iBAAK,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC;AACnC,iBAAK,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC;AAAA,UAC/C,WAAqB,EAAE,gBAAgB,GAAG;AAC9B,iBAAK,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC;AACnC,iBAAK,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC;AACnC,iBAAK,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC;AACnC,iBAAK,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC;AACnC,iBAAK,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC;AACnC,iBAAK,YAAY,KAAK,EAAE,SAAS,CAAC,CAAC;AAAA,UAC/C,OAAiB;AACL,kBAAM,IAAI,MAAM,uFAAuF;AAAA,UACxG;AAAA,QACF;AAAA,MACF;AAGD,UAAI,KAAK,WAAW;AAClB,aAAK,SAAS,CAAE;AAEhB,iBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,EAAE,GAAG;AACvC,eAAK,OAAO,CAAC,IAAI,IAAI,OAAQ;AAC7B,yBAAe,QAAQ,KAAK,OAAO,CAAC,CAAC;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAED,aAAS,2BAA2B,QAAQ,MAAM;AAChD,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,+BAA+B;AAEpD,oBAAc,MAAM;AACpB,WAAK,OAAO,cAAc,MAAM;AAChC,WAAK,YAAY,kBAAkB,MAAM;AACzC,WAAK,SAAS,kBAAkB,MAAM;AACtC,WAAK,cAAc,kBAAkB,MAAM;AAC3C,WAAK,QAAQ,kBAAkB,MAAM;AACrC,WAAK,QAAQ,CAAE;AACf,aAAO,UAAU,KAAK,OAAO,GAAG,KAAK,WAAW;AAAA,IACjD;AAID,aAAS,mBAAmB,QAAQ,KAAK;AACvC,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,uBAAuB;AAE5C,oBAAc,MAAM;AACpB,UAAI,gBAAgB,IAAI,iBAAiB,kBAAkB,MAAM;AAEjE,UAAI,IAAI,gBAAgB;AACtB,YAAI,IAAI,aAAa;AACnB,iBAAO,IAAI;AAAA,QACZ;AAED,YAAI,cAAc,CAAE;AAEpB,iBAAS,IAAI,GAAG,IAAI,IAAI,gBAAgB,EAAE,GAAG;AAC3C,cAAI,YAAY,CAAC,IAAI,IAAI,mBAAoB;AAC7C,qCAA2B,QAAQ,IAAI,YAAY,CAAC,CAAC;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAED,aAAS,mBAAmB,QAAQ,IAAI;AACtC,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,uBAAuB;AAE5C,oBAAc,MAAM;AACpB,SAAG,YAAY,cAAc,MAAM;AACnC,SAAG,mBAAmB,kBAAkB,MAAM;AAC9C,SAAG,mBAAmB,kBAAkB,MAAM;AAC9C,SAAG,kBAAkB,kBAAkB,MAAM;AAC7C,SAAG,YAAY,kBAAkB,MAAM;AACvC,SAAG,aAAa,kBAAkB,MAAM;AAExC,UAAI,GAAG,kBAAkB;AACvB,YAAI,WAAW;AACb,qBAAW,QAAQ,GAAG,eAAe,GAAG,gBAAgB;AAAA,QAClE,OAAe;AAGL,aAAG,gBAAgB,CAAE;AACrB,gCAAsB,QAAQ,GAAG,eAAe,GAAG,gBAAgB;AAAA,QACpE;AAAA,MACF;AAED,UAAI,GAAG,kBAAkB;AACvB,YAAI,WAAW;AACb,qBAAW,QAAQ,GAAG,eAAe,GAAG,gBAAgB;AAAA,QAClE,OAAe;AAGL,aAAG,gBAAgB,CAAE;AACrB,8BAAoB,QAAQ,GAAG,eAAe,GAAG,gBAAgB;AAAA,QAClE;AAAA,MACF;AAED,UAAI,GAAG,iBAAiB;AACtB,YAAI,WAAW;AACb,qBAAW,QAAQ,GAAG,cAAc,GAAG,eAAe;AAAA,QAChE,OAAe;AAGL,aAAG,eAAe,CAAE;AACpB,gCAAsB,QAAQ,GAAG,cAAc,GAAG,eAAe;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAED,aAAS,eAAe,QAAQ,MAAM;AACpC,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,wBAAwB;AAE7C,oBAAc,MAAM;AACpB,WAAK,QAAQ,cAAc,MAAM;AACjC,WAAK,YAAY,YAAY,MAAM;AACnC,WAAK,kBAAkB,YAAY,MAAM;AACzC,WAAK,eAAe,kBAAkB,MAAM;AAE5C,UAAI,KAAK,cAAc;AACrB,aAAK,YAAY,CAAE;AAEnB,iBAAS,IAAI,GAAG,IAAI,KAAK,cAAc,EAAE,GAAG;AAC1C,eAAK,UAAU,CAAC,IAAI,IAAI,WAAY;AACpC,6BAAmB,QAAQ,KAAK,UAAU,CAAC,CAAC;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAED,aAAS,kBAAkB,QAAQ,KAAK;AACtC,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,sBAAsB;AAE3C,oBAAc,MAAM;AACpB,UAAI,SAAS,kBAAkB,MAAM;AACrC,UAAI,UAAU,kBAAkB,MAAM;AACtC,aAAO,UAAU,IAAI,eAAe,GAAG,CAAC;AAExC,UAAI,CAAC,WAAW;AACd,YAAI,CAAC,IAAI,SAAS;AAChB,cAAI,SAAS,CAAE;AACf,iBAAO,UAAU,IAAI,QAAQ,GAAG,IAAI,MAAM;AAAA,QACpD,OAAe;AACL,cAAI,SAAS,CAAE;AACf,iBAAO,UAAU,IAAI,QAAQ,GAAG,IAAI,SAAS,IAAI,UAAU,CAAC;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AAED,aAAS,gBAAgB,QAAQ,GAAG;AAClC,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,oBAAoB;AAEzC,oBAAc,MAAM;AACpB,QAAE,QAAQ,cAAc,MAAM;AAC9B,QAAE,QAAQ,kBAAkB,MAAM;AAElC,UAAI,EAAE,SAAS,2BAA2B;AACxC,UAAE,uBAAuB,UAAU,MAAM;AACzC,UAAE,qBAAqB,UAAU,MAAM;AACvC,UAAE,wBAAwB,UAAU,MAAM;AAAA,MAC3C;AAED,QAAE,gBAAgB,eAAe,MAAM;AACvC,QAAE,iBAAiB,eAAe,MAAM;AACxC,QAAE,gBAAgB,eAAe,MAAM;AAEvC,UAAI,EAAE,SAAS,oBAAoB;AACjC,UAAE,kBAAkB,UAAU,MAAM;AACpC,UAAE,kBAAkB,UAAU,MAAM;AAAA,MACrC;AAAA,IACF;AAED,aAAS,iBAAiB,QAAQ,KAAK;AACrC,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,qBAAqB;AAE1C,oBAAc,MAAM;AACpB,UAAI,QAAQ,cAAc,MAAM;AAChC,UAAI,YAAY,gBAAgB,MAAM;AACtC,UAAI,UAAU,gBAAgB,MAAM;AACpC,UAAI,MAAM,gBAAgB,MAAM;AAChC,UAAI,iBAAiB,UAAU,MAAM;AACrC,UAAI,iBAAiB,UAAU,MAAM;AACrC,UAAI,gBAAgB,UAAU,MAAM;AACpC,UAAI,UAAU,UAAU,MAAM;AAAA,IAC/B;AAED,aAAS,gBAAgB,QAAQ,OAAO;AACtC,UAAI,UAAU,cAAc,MAAM;AAClC,gBAAU,WAAW,oBAAoB;AAEzC,oBAAc,MAAM;AACpB,YAAM,SAAS,kBAAkB,MAAM;AACvC,YAAM,aAAa,kBAAkB,MAAM;AAC3C,YAAM,gBAAgB,kBAAkB,MAAM;AAC9C,YAAM,iBAAiB,kBAAkB,MAAM;AAC/C,YAAM,eAAe,kBAAkB,MAAM;AAC7C,YAAM,aAAa,kBAAkB,MAAM;AAC3C,YAAM,cAAc,kBAAkB,MAAM;AAE5C,YAAM,YAAY,IAAI,OAAQ;AAC9B,YAAM,YAAY,eAAe,QAAQ,MAAM,CAAC;AAEhD,UAAI,MAAM,YAAY;AACpB,cAAM,UAAU,CAAE;AAElB,iBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,EAAE,GAAG;AACzC,gBAAM,QAAQ,CAAC,IAAI,IAAI,OAAQ;AAC/B,yBAAe,QAAQ,MAAM,QAAQ,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAGD,UAAI,MAAM,eAAe;AACvB,cAAM,aAAa,CAAE;AAErB,iBAAS,IAAI,GAAG,IAAI,MAAM,eAAe,EAAE,GAAG;AAC5C,gBAAM,WAAW,CAAC,IAAI,IAAI,WAAY;AACtC,6BAAmB,QAAQ,MAAM,WAAW,CAAC,CAAC;AAAA,QAC/C;AAAA,MACF;AAGD,UAAI,MAAM,gBAAgB;AACxB,cAAM,cAAc,CAAE;AAEtB,iBAAS,IAAI,GAAG,IAAI,MAAM,gBAAgB,EAAE,GAAG;AAC7C,gBAAM,YAAY,CAAC,IAAI,IAAI,YAAa;AACxC,yBAAe,QAAQ,MAAM,YAAY,CAAC,CAAC;AAAA,QAC5C;AAAA,MACF;AAGD,UAAI,MAAM,cAAc;AACtB,cAAM,YAAY,CAAE;AAEpB,iBAAS,IAAI,GAAG,IAAI,MAAM,cAAc,EAAE,GAAG;AAC3C,gBAAM,UAAU,CAAC,IAAI,IAAI,UAAW;AACpC,4BAAkB,QAAQ,MAAM,UAAU,CAAC,CAAC;AAAA,QAC7C;AAAA,MACF;AAGD,UAAI,MAAM,YAAY;AACpB,cAAM,UAAU,CAAE;AAElB,iBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,EAAE,GAAG;AACzC,gBAAM,QAAQ,CAAC,IAAI,IAAI,QAAS;AAChC,0BAAgB,QAAQ,MAAM,QAAQ,CAAC,CAAC;AAAA,QACzC;AAAA,MACF;AAGD,UAAI,MAAM,aAAa;AACrB,cAAM,WAAW,CAAE;AAEnB,iBAAS,IAAI,GAAG,IAAI,MAAM,aAAa,EAAE,GAAG;AAC1C,gBAAM,SAAS,CAAC,IAAI,IAAI,SAAU;AAClC,2BAAiB,QAAQ,MAAM,SAAS,CAAC,CAAC;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AAED,QAAI,eAAe;AACnB,QAAI,eAAe;AAEnB,aAAS,aAAa,QAAQ;AAC5B,aAAO,aAAa;AACpB,aAAO,OAAO,SAAU,KAAK,KAAK;AAChC,YAAI,OAAO,cAAc;AACvB,iBAAO,cAAc;AAAA,QACtB;AAED,YAAI,OAAO,cAAc;AACvB,iBAAO,aAAa;AAAA,QACrB;AAAA,MACF;AAED,aAAO,YAAY,SAAU,MAAM,MAAM,GAAG;AAC1C,YAAI,QAAQ,OAAO;AACnB,iBAAS,IAAI,GAAG,IAAI,OAAO;AAAK,eAAK,CAAC,IAAI,aAAa,IAAI;AAAA,MAC5D;AAED,aAAO,aAAa,SAAU,OAAO,KAAK;AACxC,YAAI,OAAO,KAAK;AAChB,YAAI,UAAU,KAAK,MAAM,OAAO,GAAG;AACnC,eAAO,IAAI,aAAa,OAAO;AAAA,MAChC;AAED,aAAO,iBAAiB,SAAU,OAAO,KAAK;AAC5C,YAAI,OAAO,KAAK;AAChB,YAAI,UAAU,KAAK,MAAM,OAAO,GAAG;AACnC,eAAO,IAAI,YAAY,OAAO;AAAA,MAC/B;AAED,aAAO,gBAAgB,SAAU,OAAO,KAAK;AAC3C,YAAI,OAAO,KAAK;AAChB,YAAI,UAAU,KAAK,MAAM,OAAO,GAAG;AACnC,eAAO,IAAI,WAAW,OAAO;AAAA,MAC9B;AAED,aAAO,iBAAiB,SAAU,OAAO,KAAK;AAC5C,YAAI,OAAO,KAAK;AAChB,YAAI,UAAU,KAAK,MAAM,OAAO,GAAG;AACnC,eAAO,IAAI,YAAY,OAAO;AAAA,MAC/B;AAAA,IACF;AAED,QAAI,WAAW;AAEf,aAAS,eAAe,WAAW;AACjC,UAAI,SAAS,IAAI,QAAS;AAC1B,UAAI,SAAS,IAAI,SAAS,SAAS;AACnC,mBAAa,MAAM;AACnB,aAAO,KAAK,IAAI,YAAY;AAE5B,aAAO,eAAe,kBAAkB,MAAM;AAE9C,aAAO,eAAe,kBAAkB,MAAM;AAE9C,aAAO,kBAAkB,kBAAkB,MAAM;AAEjD,aAAO,eAAe,kBAAkB,MAAM;AAC9C,kBAAY,cAAc,MAAM,IAAI;AACpC,mBAAa,cAAc,MAAM,IAAI;AACrC,UAAI;AAAW,cAAM;AACrB,aAAO,KAAK,KAAK,YAAY;AAC7B,aAAO,KAAK,KAAK,YAAY;AAC7B,aAAO,KAAK,IAAI,YAAY;AAC5B,UAAI,YAAY;AACd,YAAI,mBAAmB,cAAc,MAAM;AAC3C,YAAI,iBAAiB,OAAO,SAAQ,IAAK,OAAO,KAAM;AACtD,YAAI,iBAAiB,CAAE;AACvB,eAAO,KAAK,gBAAgB,GAAG,cAAc;AAC7C,YAAI,mBAAmB,CAAE;AACzB,mBAAW,kBAAkB,kBAAkB,gBAAgB,cAAc;AAC7E,YAAI,OAAO,IAAI,YAAY,gBAAgB;AAC3C,wBAAgB,MAAM,MAAM;AAAA,MACpC,OAAa;AACL,wBAAgB,QAAQ,MAAM;AAAA,MAC/B;AAED,aAAO,OAAO,QAAS;AAAA,IACxB;AAED,WAAO,eAAe,MAAM;AAAA,EAC7B;AACH;;"}