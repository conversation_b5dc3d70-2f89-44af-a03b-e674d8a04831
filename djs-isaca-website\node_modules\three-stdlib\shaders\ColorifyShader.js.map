{"version": 3, "file": "ColorifyShader.js", "sources": ["../../src/shaders/ColorifyShader.ts"], "sourcesContent": ["import { Color } from 'three'\n\n/**\n * Colorify shader\n */\n\nexport const ColorifyShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    color: { value: /* @__PURE__ */ new Color(0xffffff) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform vec3 color;\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tvec3 luma = vec3( 0.299, 0.587, 0.114 );\n    \tfloat v = dot( texel.xyz, luma );\n\n    \tgl_FragColor = vec4( v * color, texel.w );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";AAMO,MAAM,iBAAiB;AAAA,EAC5B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,OAAO,EAAE,OAA2B,oBAAA,MAAM,QAAQ,EAAE;AAAA,EACtD;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB7B;"}