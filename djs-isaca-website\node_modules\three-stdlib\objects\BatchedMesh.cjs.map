{"version": 3, "file": "BatchedMesh.cjs", "sources": ["../../src/objects/BatchedMesh.ts"], "sourcesContent": ["import {\n  Matrix4,\n  <PERSON><PERSON>er<PERSON><PERSON>ri<PERSON>e,\n  InterleavedBufferAttribute,\n  Mesh,\n  BufferGeometry,\n  Material,\n  DataTexture,\n  IUniform,\n  MathUtils,\n  RGBAFormat,\n  FloatType,\n} from 'three'\n\nconst ID_ATTR_NAME = '_batch_id_'\nconst _identityMatrix = /* @__PURE__ */ new Matrix4()\nconst _zeroScaleMatrix = /* @__PURE__ */ (() => new Matrix4().set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1))()\n\n// Custom shaders\nconst batchingParsVertex = /* glsl */ `\n#ifdef BATCHING\n\tattribute float ${ID_ATTR_NAME};\n\tuniform highp sampler2D batchingTexture;\n\tmat4 getBatchingMatrix( const in float i ) {\n\n\t\tint size = textureSize( batchingTexture, 0 ).x;\n\t\tint j = int( i ) * 4;\n\t\tint x = j % size;\n\t\tint y = j / size;\n\t\tvec4 v1 = texelFetch( batchingTexture, ivec2( x, y ), 0 );\n\t\tvec4 v2 = texelFetch( batchingTexture, ivec2( x + 1, y ), 0 );\n\t\tvec4 v3 = texelFetch( batchingTexture, ivec2( x + 2, y ), 0 );\n\t\tvec4 v4 = texelFetch( batchingTexture, ivec2( x + 3, y ), 0 );\n\t\treturn mat4( v1, v2, v3, v4 );\n\n\t}\n#endif\n`\n\nconst batchingbaseVertex = /* glsl */ `\n#ifdef BATCHING\n\tmat4 batchingMatrix = getBatchingMatrix( ${ID_ATTR_NAME} );\n#endif\n`\n\nconst batchingnormalVertex = /* glsl */ `\n#ifdef BATCHING\n\tobjectNormal = vec4( batchingMatrix * vec4( objectNormal, 0.0 ) ).xyz;\n\t#ifdef USE_TANGENT\n\t\tobjectTangent = vec4( batchingMatrix * vec4( objectTangent, 0.0 ) ).xyz;\n\t#endif\n#endif\n`\n\nconst batchingVertex = /* glsl */ `\n#ifdef BATCHING\n\ttransformed = ( batchingMatrix * vec4( transformed, 1.0 ) ).xyz;\n#endif\n`\n\n// @TODO: SkinnedMesh support?\n// @TODO: Future work if needed. Move into the core. Can be optimized more with WEBGL_multi_draw.\n\n// copies data from attribute \"src\" into \"target\" starting at \"targetOffset\"\nfunction copyAttributeData(\n  src: BufferAttribute | InterleavedBufferAttribute,\n  target: BufferAttribute | InterleavedBufferAttribute,\n  targetOffset = 0,\n): void {\n  const itemSize = target.itemSize\n  if (\n    (src as InterleavedBufferAttribute).isInterleavedBufferAttribute ||\n    src.array.constructor !== target.array.constructor\n  ) {\n    // use the component getters and setters if the array data cannot\n    // be copied directly\n    const vertexCount = src.count\n    for (let i = 0; i < vertexCount; i++) {\n      for (let c = 0; c < itemSize; c++) {\n        // @ts-ignore\n        target.setComponent(i + targetOffset, c, src.getComponent(i, c))\n      }\n    }\n  } else {\n    // faster copy approach using typed array set function\n    // @ts-ignore\n    target.array.set(src.array, targetOffset * itemSize)\n  }\n\n  target.needsUpdate = true\n}\n\nclass BatchedMesh extends Mesh<BufferGeometry, Material> {\n  _vertexStarts: number[]\n  _vertexCounts: number[]\n  _indexStarts: number[]\n  _indexCounts: number[]\n  _reservedRanges: { vertexStart: number; vertexCount: number; indexStart: number; indexCount: number }[]\n  _visible: boolean[]\n  _active: boolean[]\n  _maxGeometryCount: number\n  _maxVertexCount: number\n  _maxIndexCount: number\n  _geometryInitialized: boolean\n  _geometryCount: number\n  _matrices: Matrix4[]\n  _matricesTexture: DataTexture | null\n  _customUniforms: Record<string, IUniform>\n\n  constructor(\n    maxGeometryCount: number,\n    maxVertexCount: number,\n    maxIndexCount = maxVertexCount * 2,\n    material?: Material,\n  ) {\n    super(new BufferGeometry(), material)\n\n    this._vertexStarts = []\n    this._vertexCounts = []\n    this._indexStarts = []\n    this._indexCounts = []\n    this._reservedRanges = []\n\n    this._visible = []\n    this._active = []\n\n    this._maxGeometryCount = maxGeometryCount\n    this._maxVertexCount = maxVertexCount\n    this._maxIndexCount = maxIndexCount\n\n    this._geometryInitialized = false\n    this._geometryCount = 0\n\n    // Local matrix per geometry by using data texture\n    // @TODO: Support uniform parameter per geometry\n\n    this._matrices = []\n    this._matricesTexture = null!\n\n    // @TODO: Calculate the entire binding box and make frustumCulled true\n    this.frustumCulled = false\n\n    this._customUniforms = {\n      batchingTexture: { value: null },\n    }\n\n    this._initMatricesTexture()\n    this._initShader()\n\n    this.onBeforeRender = function () {\n      if (this.material.defines) {\n        this.material.defines.BATCHING = true\n      }\n\n      // @TODO: Implement frustum culling for each geometry\n    }\n\n    this.onAfterRender = function () {\n      if (this.material.defines) {\n        this.material.defines.BATCHING = false\n      }\n    }\n  }\n\n  _initMatricesTexture(): void {\n    // layout (1 matrix = 4 pixels)\n    //      RGBA RGBA RGBA RGBA (=> column1, column2, column3, column4)\n    //  with  8x8  pixel texture max   16 matrices * 4 pixels =  (8 * 8)\n    //       16x16 pixel texture max   64 matrices * 4 pixels = (16 * 16)\n    //       32x32 pixel texture max  256 matrices * 4 pixels = (32 * 32)\n    //       64x64 pixel texture max 1024 matrices * 4 pixels = (64 * 64)\n\n    let size = Math.sqrt(this._maxGeometryCount * 4) // 4 pixels needed for 1 matrix\n    size = MathUtils.ceilPowerOfTwo(size)\n    size = Math.max(size, 4)\n\n    const matricesArray = new Float32Array(size * size * 4) // 4 floats per RGBA pixel\n    const matricesTexture = new DataTexture(matricesArray, size, size, RGBAFormat, FloatType)\n\n    this._matricesTexture = matricesTexture\n    this._customUniforms.batchingTexture.value = this._matricesTexture\n  }\n\n  _initShader(): void {\n    const material = this.material\n    const currentOnBeforeCompile = material.onBeforeCompile\n    const customUniforms = this._customUniforms\n\n    material.onBeforeCompile = function onBeforeCompile(parameters, renderer) {\n      // Is this replacement stable across any materials?\n      parameters.vertexShader = parameters.vertexShader\n        .replace('#include <skinning_pars_vertex>', '#include <skinning_pars_vertex>\\n' + batchingParsVertex)\n        .replace('#include <uv_vertex>', '#include <uv_vertex>\\n' + batchingbaseVertex)\n        .replace('#include <skinnormal_vertex>', '#include <skinnormal_vertex>\\n' + batchingnormalVertex)\n        .replace('#include <skinning_vertex>', '#include <skinning_vertex>\\n' + batchingVertex)\n\n      for (const uniformName in customUniforms) {\n        parameters.uniforms[uniformName] = customUniforms[uniformName]\n      }\n\n      currentOnBeforeCompile.call(this, parameters, renderer)\n    }\n\n    material.defines = material.defines || {}\n    material.defines.BATCHING = false\n  }\n\n  _initializeGeometry(reference: BufferGeometry): void {\n    // @TODO: geometry.groups support?\n    // @TODO: geometry.drawRange support?\n    // @TODO: geometry.morphAttributes support?\n\n    const geometry = this.geometry\n    const maxVertexCount = this._maxVertexCount\n    const maxGeometryCount = this._maxGeometryCount\n    const maxIndexCount = this._maxIndexCount\n    if (this._geometryInitialized === false) {\n      for (const attributeName in reference.attributes) {\n        const srcAttribute = reference.getAttribute(attributeName)\n        const { array, itemSize, normalized } = srcAttribute\n\n        const dstArray = new (array.constructor as Float32ArrayConstructor)(maxVertexCount * itemSize)\n        const dstAttribute = new (srcAttribute.constructor as any)(dstArray, itemSize, normalized)\n\n        // TODO: add usage in @types/three\n        // @ts-ignore\n        dstAttribute.setUsage(srcAttribute.usage)\n\n        geometry.setAttribute(attributeName, dstAttribute)\n      }\n\n      if (reference.getIndex() !== null) {\n        const indexArray = maxVertexCount > 65536 ? new Uint32Array(maxIndexCount) : new Uint16Array(maxIndexCount)\n\n        geometry.setIndex(new BufferAttribute(indexArray, 1))\n      }\n\n      const idArray = maxGeometryCount > 65536 ? new Uint32Array(maxVertexCount) : new Uint16Array(maxVertexCount)\n      geometry.setAttribute(ID_ATTR_NAME, new BufferAttribute(idArray, 1))\n\n      this._geometryInitialized = true\n    }\n  }\n\n  // Make sure the geometry is compatible with the existing combined geometry atributes\n  _validateGeometry(geometry: BufferGeometry): void {\n    // check that the geometry doesn't have a version of our reserved id attribute\n    if (geometry.getAttribute(ID_ATTR_NAME)) {\n      throw new Error(`BatchedMesh: Geometry cannot use attribute \"${ID_ATTR_NAME}\"`)\n    }\n\n    // check to ensure the geometries are using consistent attributes and indices\n    const batchGeometry = this.geometry\n    if (Boolean(geometry.getIndex()) !== Boolean(batchGeometry.getIndex())) {\n      throw new Error('BatchedMesh: All geometries must consistently have \"index\".')\n    }\n\n    for (const attributeName in batchGeometry.attributes) {\n      if (attributeName === ID_ATTR_NAME) {\n        continue\n      }\n\n      if (!geometry.hasAttribute(attributeName)) {\n        throw new Error(\n          `BatchedMesh: Added geometry missing \"${attributeName}\". All geometries must have consistent attributes.`,\n        )\n      }\n\n      const srcAttribute = geometry.getAttribute(attributeName)\n      const dstAttribute = batchGeometry.getAttribute(attributeName)\n      if (srcAttribute.itemSize !== dstAttribute.itemSize || srcAttribute.normalized !== dstAttribute.normalized) {\n        throw new Error('BatchedMesh: All attributes must have a consistent itemSize and normalized value.')\n      }\n    }\n  }\n\n  getGeometryCount(): number {\n    return this._geometryCount\n  }\n\n  getVertexCount(): number {\n    const reservedRanges = this._reservedRanges\n    if (reservedRanges.length === 0) {\n      return 0\n    } else {\n      const finalRange = reservedRanges[reservedRanges.length - 1]\n      return finalRange.vertexStart + finalRange.vertexCount\n    }\n  }\n\n  getIndexCount(): number {\n    const reservedRanges = this._reservedRanges\n    const geometry = this.geometry\n    if (geometry.getIndex() === null || reservedRanges.length === 0) {\n      return 0\n    } else {\n      const finalRange = reservedRanges[reservedRanges.length - 1]\n      return finalRange.indexStart + finalRange.indexCount\n    }\n  }\n\n  addGeometry(geometry: BufferGeometry, vertexCount = -1, indexCount = -1): number {\n    this._initializeGeometry(geometry)\n\n    this._validateGeometry(geometry)\n\n    // ensure we're not over geometry\n    if (this._geometryCount >= this._maxGeometryCount) {\n      throw new Error('BatchedMesh: Maximum geometry count reached.')\n    }\n\n    // get the necessary range fo the geometry\n    const range = {\n      vertexStart: -1,\n      vertexCount: -1,\n      indexStart: -1,\n      indexCount: -1,\n    }\n\n    let lastRange = null\n    const reservedRanges = this._reservedRanges\n    if (this._geometryCount !== 0) {\n      lastRange = reservedRanges[reservedRanges.length - 1]\n    }\n\n    if (vertexCount === -1) {\n      range.vertexCount = geometry.getAttribute('position').count\n    } else {\n      range.vertexCount = vertexCount\n    }\n\n    if (lastRange === null) {\n      range.vertexStart = 0\n    } else {\n      range.vertexStart = lastRange.vertexStart + lastRange.vertexCount\n    }\n\n    if (geometry.getIndex() !== null) {\n      if (indexCount === -1) {\n        range.indexCount = geometry.getIndex()!.count\n      } else {\n        range.indexCount = indexCount\n      }\n\n      if (lastRange === null) {\n        range.indexStart = 0\n      } else {\n        range.indexStart = lastRange.indexStart + lastRange.indexCount\n      }\n    }\n\n    if (\n      (range.indexStart !== -1 && range.indexStart + range.indexCount > this._maxIndexCount) ||\n      range.vertexStart + range.vertexCount > this._maxVertexCount\n    ) {\n      throw new Error('BatchedMesh: Reserved space request exceeds the maximum buffer size.')\n    }\n\n    const indexCounts = this._indexCounts\n    const indexStarts = this._indexStarts\n    const vertexCounts = this._vertexCounts\n    const vertexStarts = this._vertexStarts\n\n    const visible = this._visible\n    const active = this._active\n    const matricesTexture = this._matricesTexture\n    const matrices = this._matrices\n    const matricesArray = this._matricesTexture!.image.data\n\n    // push new visibility states\n    visible.push(true)\n    active.push(true)\n\n    // update id\n    const geometryId = this._geometryCount\n    this._geometryCount++\n\n    // initialize matrix information\n    matrices.push(new Matrix4())\n    _identityMatrix.toArray(matricesArray, geometryId * 16)\n    matricesTexture!.needsUpdate = true\n\n    // add the reserved range\n    reservedRanges.push(range)\n\n    // push new geometry data range\n    vertexStarts.push(range.vertexStart)\n    vertexCounts.push(range.vertexCount)\n\n    if (geometry.getIndex() !== null) {\n      // push new index range\n      indexStarts.push(range.indexCount)\n      indexCounts.push(range.indexCount)\n    }\n\n    // set the id for the geometry\n    const idAttribute = this.geometry.getAttribute(ID_ATTR_NAME)\n    for (let i = 0; i < range.vertexCount; i++) {\n      idAttribute.setX(range.vertexStart + i, geometryId)\n    }\n\n    idAttribute.needsUpdate = true\n\n    // update the geometry\n    this.setGeometryAt(geometryId, geometry)\n\n    return geometryId\n  }\n\n  /**\n   * @deprecated use `addGeometry` instead.\n   */\n  applyGeometry(geometry: BufferGeometry): number {\n    return this.addGeometry(geometry)\n  }\n\n  setGeometryAt(id: number, geometry: BufferGeometry): number {\n    if (id >= this._geometryCount) {\n      throw new Error('BatchedMesh: Maximum geometry count reached.')\n    }\n\n    this._validateGeometry(geometry)\n\n    const range = this._reservedRanges[id]\n    if (\n      (geometry.getIndex() !== null && geometry.getIndex()!.count > range.indexCount) ||\n      geometry.attributes.position.count > range.vertexCount\n    ) {\n      throw new Error('BatchedMesh: Reserved space not large enough for provided geometry.')\n    }\n\n    // copy geometry over\n    const batchGeometry = this.geometry\n    const srcPositionAttribute = geometry.getAttribute('position')\n    const hasIndex = batchGeometry.getIndex() !== null\n    const dstIndex = batchGeometry.getIndex()!\n    const srcIndex = geometry.getIndex()!\n\n    // copy attribute data over\n    const vertexStart = range.vertexStart\n    const vertexCount = range.vertexCount\n    for (const attributeName in batchGeometry.attributes) {\n      if (attributeName === ID_ATTR_NAME) {\n        continue\n      }\n\n      const srcAttribute = geometry.getAttribute(attributeName)\n      const dstAttribute = batchGeometry.getAttribute(attributeName)\n      copyAttributeData(srcAttribute, dstAttribute, vertexStart)\n\n      // fill the rest in with zeroes\n      const itemSize = srcAttribute.itemSize\n      for (let i = srcAttribute.count, l = vertexCount; i < l; i++) {\n        const index = vertexStart + i\n        for (let c = 0; c < itemSize; c++) {\n          // @ts-ignore\n          dstAttribute.setComponent(index, c, 0)\n        }\n      }\n\n      dstAttribute.needsUpdate = true\n    }\n\n    this._vertexCounts[id] = srcPositionAttribute.count\n\n    if (hasIndex) {\n      // fill the rest in with zeroes\n      const indexStart = range.indexStart\n\n      // copy index data over\n      for (let i = 0; i < srcIndex.count; i++) {\n        dstIndex.setX(indexStart + i, vertexStart + srcIndex.getX(i))\n      }\n\n      // fill the rest in with zeroes\n      for (let i = srcIndex.count, l = range.indexCount; i < l; i++) {\n        dstIndex.setX(indexStart + i, vertexStart)\n      }\n\n      dstIndex.needsUpdate = true\n      this._indexCounts[id] = srcIndex.count\n    }\n\n    return id\n  }\n\n  deleteGeometry(geometryId: number): this {\n    // Note: User needs to call optimize() afterward to pack the data.\n\n    const active = this._active\n    const matricesTexture = this._matricesTexture!\n    const matricesArray = matricesTexture.image.data\n    if (geometryId >= active.length || active[geometryId] === false) {\n      return this\n    }\n\n    active[geometryId] = false\n    _zeroScaleMatrix.toArray(matricesArray, geometryId * 16)\n    matricesTexture!.needsUpdate = true\n\n    return this\n  }\n\n  optimize(): never {\n    throw new Error('BatchedMesh: Optimize function not implemented.')\n  }\n\n  setMatrixAt(geometryId: number, matrix: Matrix4): this {\n    // @TODO: Map geometryId to index of the arrays because\n    //        optimize() can make geometryId mismatch the index\n\n    const visible = this._visible\n    const active = this._active\n    const matricesTexture = this._matricesTexture!\n    const matrices = this._matrices\n    const matricesArray = matricesTexture.image.data\n    if (geometryId >= matrices.length || active[geometryId] === false) {\n      return this\n    }\n\n    if (visible[geometryId] === true) {\n      matrix.toArray(matricesArray, geometryId * 16)\n      matricesTexture.needsUpdate = true\n    }\n\n    matrices[geometryId].copy(matrix)\n\n    return this\n  }\n\n  getMatrixAt(geometryId: number, matrix: Matrix4): Matrix4 {\n    const matrices = this._matrices\n    const active = this._active\n    if (geometryId >= matrices.length || active[geometryId] === false) {\n      return matrix\n    }\n\n    return matrix.copy(matrices[geometryId])\n  }\n\n  setVisibleAt(geometryId: number, value: boolean): this {\n    const visible = this._visible\n    const active = this._active\n    const matricesTexture = this._matricesTexture!\n    const matrices = this._matrices\n    const matricesArray = matricesTexture.image.data\n\n    // if the geometry is out of range, not active, or visibility state\n    // does not change then return early\n    if (geometryId >= visible.length || active[geometryId] === false || visible[geometryId] === value) {\n      return this\n    }\n\n    // scale the matrix to zero if it's hidden\n    if (value === true) {\n      matrices[geometryId].toArray(matricesArray, geometryId * 16)\n    } else {\n      _zeroScaleMatrix.toArray(matricesArray, geometryId * 16)\n    }\n\n    matricesTexture.needsUpdate = true\n    visible[geometryId] = value\n\n    return this\n  }\n\n  getVisibleAt(geometryId: number): boolean {\n    const visible = this._visible\n    const active = this._active\n\n    // return early if the geometry is out of range or not active\n    if (geometryId >= visible.length || active[geometryId] === false) {\n      return false\n    }\n\n    return visible[geometryId]\n  }\n\n  raycast(): void {\n    console.warn('BatchedMesh: Raycast function not implemented.')\n  }\n\n  copy(): never {\n    // super.copy( source );\n\n    throw new Error('BatchedMesh: Copy function not implemented.')\n  }\n\n  toJSON(): never {\n    throw new Error('BatchedMesh: toJSON function not implemented.')\n  }\n\n  dispose(): this {\n    // Assuming the geometry is not shared with other meshes\n    this.geometry.dispose()\n\n    this._matricesTexture!.dispose()\n    this._matricesTexture = null!\n\n    return this\n  }\n}\n\nexport { BatchedMesh }\n"], "names": ["Matrix4", "<PERSON><PERSON>", "BufferGeometry", "MathUtils", "DataTexture", "RGBAFormat", "FloatType", "BufferAttribute"], "mappings": ";;;;;;;;;AAcA,MAAM,eAAe;AACrB,MAAM,sCAAsCA,MAAAA;AAC5C,MAAM,0CAA0C,IAAIA,MAAAA,UAAU,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAGhH,MAAM;AAAA;AAAA,EAAgC;AAAA;AAAA,mBAEnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBnB,MAAM;AAAA;AAAA,EAAgC;AAAA;AAAA,4CAEM;AAAA;AAAA;AAAA;AAI5C,MAAM;AAAA;AAAA,EAAkC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASxC,MAAM;AAAA;AAAA,EAA4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAUlC,SAAS,kBACP,KACA,QACA,eAAe,GACT;AACN,QAAM,WAAW,OAAO;AACxB,MACG,IAAmC,gCACpC,IAAI,MAAM,gBAAgB,OAAO,MAAM,aACvC;AAGA,UAAM,cAAc,IAAI;AACxB,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAE1B,eAAA,aAAa,IAAI,cAAc,GAAG,IAAI,aAAa,GAAG,CAAC,CAAC;AAAA,MACjE;AAAA,IACF;AAAA,EAAA,OACK;AAGL,WAAO,MAAM,IAAI,IAAI,OAAO,eAAe,QAAQ;AAAA,EACrD;AAEA,SAAO,cAAc;AACvB;AAEA,MAAM,oBAAoBC,MAAAA,KAA+B;AAAA,EAiBvD,YACE,kBACA,gBACA,gBAAgB,iBAAiB,GACjC,UACA;AACM,UAAA,IAAIC,MAAAA,kBAAkB,QAAQ;AAtBtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAUE,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AAEvB,SAAK,WAAW;AAChB,SAAK,UAAU;AAEf,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AAEtB,SAAK,uBAAuB;AAC5B,SAAK,iBAAiB;AAKtB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AAGxB,SAAK,gBAAgB;AAErB,SAAK,kBAAkB;AAAA,MACrB,iBAAiB,EAAE,OAAO,KAAK;AAAA,IAAA;AAGjC,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AAEjB,SAAK,iBAAiB,WAAY;AAC5B,UAAA,KAAK,SAAS,SAAS;AACpB,aAAA,SAAS,QAAQ,WAAW;AAAA,MACnC;AAAA,IAAA;AAKF,SAAK,gBAAgB,WAAY;AAC3B,UAAA,KAAK,SAAS,SAAS;AACpB,aAAA,SAAS,QAAQ,WAAW;AAAA,MACnC;AAAA,IAAA;AAAA,EAEJ;AAAA,EAEA,uBAA6B;AAQ3B,QAAI,OAAO,KAAK,KAAK,KAAK,oBAAoB,CAAC;AACxC,WAAAC,MAAA,UAAU,eAAe,IAAI;AAC7B,WAAA,KAAK,IAAI,MAAM,CAAC;AAEvB,UAAM,gBAAgB,IAAI,aAAa,OAAO,OAAO,CAAC;AACtD,UAAM,kBAAkB,IAAIC,MAAAA,YAAY,eAAe,MAAM,MAAMC,MAAAA,YAAYC,MAAAA,SAAS;AAExF,SAAK,mBAAmB;AACnB,SAAA,gBAAgB,gBAAgB,QAAQ,KAAK;AAAA,EACpD;AAAA,EAEA,cAAoB;AAClB,UAAM,WAAW,KAAK;AACtB,UAAM,yBAAyB,SAAS;AACxC,UAAM,iBAAiB,KAAK;AAE5B,aAAS,kBAAkB,SAAS,gBAAgB,YAAY,UAAU;AAE7D,iBAAA,eAAe,WAAW,aAClC,QAAQ,mCAAmC,sCAAsC,kBAAkB,EACnG,QAAQ,wBAAwB,2BAA2B,kBAAkB,EAC7E,QAAQ,gCAAgC,mCAAmC,oBAAoB,EAC/F,QAAQ,8BAA8B,iCAAiC,cAAc;AAExF,iBAAW,eAAe,gBAAgB;AACxC,mBAAW,SAAS,WAAW,IAAI,eAAe,WAAW;AAAA,MAC/D;AAEuB,6BAAA,KAAK,MAAM,YAAY,QAAQ;AAAA,IAAA;AAG/C,aAAA,UAAU,SAAS,WAAW,CAAA;AACvC,aAAS,QAAQ,WAAW;AAAA,EAC9B;AAAA,EAEA,oBAAoB,WAAiC;AAKnD,UAAM,WAAW,KAAK;AACtB,UAAM,iBAAiB,KAAK;AAC5B,UAAM,mBAAmB,KAAK;AAC9B,UAAM,gBAAgB,KAAK;AACvB,QAAA,KAAK,yBAAyB,OAAO;AAC5B,iBAAA,iBAAiB,UAAU,YAAY;AAC1C,cAAA,eAAe,UAAU,aAAa,aAAa;AACzD,cAAM,EAAE,OAAO,UAAU,WAAA,IAAe;AAExC,cAAM,WAAW,IAAK,MAAM,YAAwC,iBAAiB,QAAQ;AAC7F,cAAM,eAAe,IAAK,aAAa,YAAoB,UAAU,UAAU,UAAU;AAI5E,qBAAA,SAAS,aAAa,KAAK;AAE/B,iBAAA,aAAa,eAAe,YAAY;AAAA,MACnD;AAEI,UAAA,UAAU,SAAS,MAAM,MAAM;AAC3B,cAAA,aAAa,iBAAiB,QAAQ,IAAI,YAAY,aAAa,IAAI,IAAI,YAAY,aAAa;AAE1G,iBAAS,SAAS,IAAIC,MAAAA,gBAAgB,YAAY,CAAC,CAAC;AAAA,MACtD;AAEM,YAAA,UAAU,mBAAmB,QAAQ,IAAI,YAAY,cAAc,IAAI,IAAI,YAAY,cAAc;AAC3G,eAAS,aAAa,cAAc,IAAIA,MAAgB,gBAAA,SAAS,CAAC,CAAC;AAEnE,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAGA,kBAAkB,UAAgC;AAE5C,QAAA,SAAS,aAAa,YAAY,GAAG;AACjC,YAAA,IAAI,MAAM,+CAA+C,eAAe;AAAA,IAChF;AAGA,UAAM,gBAAgB,KAAK;AACvB,QAAA,QAAQ,SAAS,SAAU,CAAA,MAAM,QAAQ,cAAc,SAAS,CAAC,GAAG;AAChE,YAAA,IAAI,MAAM,6DAA6D;AAAA,IAC/E;AAEW,eAAA,iBAAiB,cAAc,YAAY;AACpD,UAAI,kBAAkB,cAAc;AAClC;AAAA,MACF;AAEA,UAAI,CAAC,SAAS,aAAa,aAAa,GAAG;AACzC,cAAM,IAAI;AAAA,UACR,wCAAwC;AAAA,QAAA;AAAA,MAE5C;AAEM,YAAA,eAAe,SAAS,aAAa,aAAa;AAClD,YAAA,eAAe,cAAc,aAAa,aAAa;AAC7D,UAAI,aAAa,aAAa,aAAa,YAAY,aAAa,eAAe,aAAa,YAAY;AACpG,cAAA,IAAI,MAAM,mFAAmF;AAAA,MACrG;AAAA,IACF;AAAA,EACF;AAAA,EAEA,mBAA2B;AACzB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,iBAAyB;AACvB,UAAM,iBAAiB,KAAK;AACxB,QAAA,eAAe,WAAW,GAAG;AACxB,aAAA;AAAA,IAAA,OACF;AACL,YAAM,aAAa,eAAe,eAAe,SAAS,CAAC;AACpD,aAAA,WAAW,cAAc,WAAW;AAAA,IAC7C;AAAA,EACF;AAAA,EAEA,gBAAwB;AACtB,UAAM,iBAAiB,KAAK;AAC5B,UAAM,WAAW,KAAK;AACtB,QAAI,SAAS,SAAS,MAAM,QAAQ,eAAe,WAAW,GAAG;AACxD,aAAA;AAAA,IAAA,OACF;AACL,YAAM,aAAa,eAAe,eAAe,SAAS,CAAC;AACpD,aAAA,WAAW,aAAa,WAAW;AAAA,IAC5C;AAAA,EACF;AAAA,EAEA,YAAY,UAA0B,cAAc,IAAI,aAAa,IAAY;AAC/E,SAAK,oBAAoB,QAAQ;AAEjC,SAAK,kBAAkB,QAAQ;AAG3B,QAAA,KAAK,kBAAkB,KAAK,mBAAmB;AAC3C,YAAA,IAAI,MAAM,8CAA8C;AAAA,IAChE;AAGA,UAAM,QAAQ;AAAA,MACZ,aAAa;AAAA,MACb,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,YAAY;AAAA,IAAA;AAGd,QAAI,YAAY;AAChB,UAAM,iBAAiB,KAAK;AACxB,QAAA,KAAK,mBAAmB,GAAG;AACjB,kBAAA,eAAe,eAAe,SAAS,CAAC;AAAA,IACtD;AAEA,QAAI,gBAAgB,IAAI;AACtB,YAAM,cAAc,SAAS,aAAa,UAAU,EAAE;AAAA,IAAA,OACjD;AACL,YAAM,cAAc;AAAA,IACtB;AAEA,QAAI,cAAc,MAAM;AACtB,YAAM,cAAc;AAAA,IAAA,OACf;AACC,YAAA,cAAc,UAAU,cAAc,UAAU;AAAA,IACxD;AAEI,QAAA,SAAS,SAAS,MAAM,MAAM;AAChC,UAAI,eAAe,IAAI;AACf,cAAA,aAAa,SAAS,SAAA,EAAY;AAAA,MAAA,OACnC;AACL,cAAM,aAAa;AAAA,MACrB;AAEA,UAAI,cAAc,MAAM;AACtB,cAAM,aAAa;AAAA,MAAA,OACd;AACC,cAAA,aAAa,UAAU,aAAa,UAAU;AAAA,MACtD;AAAA,IACF;AAEA,QACG,MAAM,eAAe,MAAM,MAAM,aAAa,MAAM,aAAa,KAAK,kBACvE,MAAM,cAAc,MAAM,cAAc,KAAK,iBAC7C;AACM,YAAA,IAAI,MAAM,sEAAsE;AAAA,IACxF;AAEA,UAAM,cAAc,KAAK;AACzB,UAAM,cAAc,KAAK;AACzB,UAAM,eAAe,KAAK;AAC1B,UAAM,eAAe,KAAK;AAE1B,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,WAAW,KAAK;AAChB,UAAA,gBAAgB,KAAK,iBAAkB,MAAM;AAGnD,YAAQ,KAAK,IAAI;AACjB,WAAO,KAAK,IAAI;AAGhB,UAAM,aAAa,KAAK;AACnB,SAAA;AAGI,aAAA,KAAK,IAAIP,MAAA,QAAA,CAAS;AACX,oBAAA,QAAQ,eAAe,aAAa,EAAE;AACtD,oBAAiB,cAAc;AAG/B,mBAAe,KAAK,KAAK;AAGZ,iBAAA,KAAK,MAAM,WAAW;AACtB,iBAAA,KAAK,MAAM,WAAW;AAE/B,QAAA,SAAS,SAAS,MAAM,MAAM;AAEpB,kBAAA,KAAK,MAAM,UAAU;AACrB,kBAAA,KAAK,MAAM,UAAU;AAAA,IACnC;AAGA,UAAM,cAAc,KAAK,SAAS,aAAa,YAAY;AAC3D,aAAS,IAAI,GAAG,IAAI,MAAM,aAAa,KAAK;AAC1C,kBAAY,KAAK,MAAM,cAAc,GAAG,UAAU;AAAA,IACpD;AAEA,gBAAY,cAAc;AAGrB,SAAA,cAAc,YAAY,QAAQ;AAEhC,WAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,UAAkC;AACvC,WAAA,KAAK,YAAY,QAAQ;AAAA,EAClC;AAAA,EAEA,cAAc,IAAY,UAAkC;AACtD,QAAA,MAAM,KAAK,gBAAgB;AACvB,YAAA,IAAI,MAAM,8CAA8C;AAAA,IAChE;AAEA,SAAK,kBAAkB,QAAQ;AAEzB,UAAA,QAAQ,KAAK,gBAAgB,EAAE;AACrC,QACG,SAAS,SAAe,MAAA,QAAQ,SAAS,SAAS,EAAG,QAAQ,MAAM,cACpE,SAAS,WAAW,SAAS,QAAQ,MAAM,aAC3C;AACM,YAAA,IAAI,MAAM,qEAAqE;AAAA,IACvF;AAGA,UAAM,gBAAgB,KAAK;AACrB,UAAA,uBAAuB,SAAS,aAAa,UAAU;AACvD,UAAA,WAAW,cAAc,SAAA,MAAe;AACxC,UAAA,WAAW,cAAc;AACzB,UAAA,WAAW,SAAS;AAG1B,UAAM,cAAc,MAAM;AAC1B,UAAM,cAAc,MAAM;AACf,eAAA,iBAAiB,cAAc,YAAY;AACpD,UAAI,kBAAkB,cAAc;AAClC;AAAA,MACF;AAEM,YAAA,eAAe,SAAS,aAAa,aAAa;AAClD,YAAA,eAAe,cAAc,aAAa,aAAa;AAC3C,wBAAA,cAAc,cAAc,WAAW;AAGzD,YAAM,WAAW,aAAa;AAC9B,eAAS,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,GAAG,KAAK;AAC5D,cAAM,QAAQ,cAAc;AAC5B,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAEpB,uBAAA,aAAa,OAAO,GAAG,CAAC;AAAA,QACvC;AAAA,MACF;AAEA,mBAAa,cAAc;AAAA,IAC7B;AAEK,SAAA,cAAc,EAAE,IAAI,qBAAqB;AAE9C,QAAI,UAAU;AAEZ,YAAM,aAAa,MAAM;AAGzB,eAAS,IAAI,GAAG,IAAI,SAAS,OAAO,KAAK;AACvC,iBAAS,KAAK,aAAa,GAAG,cAAc,SAAS,KAAK,CAAC,CAAC;AAAA,MAC9D;AAGS,eAAA,IAAI,SAAS,OAAO,IAAI,MAAM,YAAY,IAAI,GAAG,KAAK;AACpD,iBAAA,KAAK,aAAa,GAAG,WAAW;AAAA,MAC3C;AAEA,eAAS,cAAc;AAClB,WAAA,aAAa,EAAE,IAAI,SAAS;AAAA,IACnC;AAEO,WAAA;AAAA,EACT;AAAA,EAEA,eAAe,YAA0B;AAGvC,UAAM,SAAS,KAAK;AACpB,UAAM,kBAAkB,KAAK;AACvB,UAAA,gBAAgB,gBAAgB,MAAM;AAC5C,QAAI,cAAc,OAAO,UAAU,OAAO,UAAU,MAAM,OAAO;AACxD,aAAA;AAAA,IACT;AAEA,WAAO,UAAU,IAAI;AACJ,qBAAA,QAAQ,eAAe,aAAa,EAAE;AACvD,oBAAiB,cAAc;AAExB,WAAA;AAAA,EACT;AAAA,EAEA,WAAkB;AACV,UAAA,IAAI,MAAM,iDAAiD;AAAA,EACnE;AAAA,EAEA,YAAY,YAAoB,QAAuB;AAIrD,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,WAAW,KAAK;AAChB,UAAA,gBAAgB,gBAAgB,MAAM;AAC5C,QAAI,cAAc,SAAS,UAAU,OAAO,UAAU,MAAM,OAAO;AAC1D,aAAA;AAAA,IACT;AAEI,QAAA,QAAQ,UAAU,MAAM,MAAM;AACzB,aAAA,QAAQ,eAAe,aAAa,EAAE;AAC7C,sBAAgB,cAAc;AAAA,IAChC;AAES,aAAA,UAAU,EAAE,KAAK,MAAM;AAEzB,WAAA;AAAA,EACT;AAAA,EAEA,YAAY,YAAoB,QAA0B;AACxD,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,KAAK;AACpB,QAAI,cAAc,SAAS,UAAU,OAAO,UAAU,MAAM,OAAO;AAC1D,aAAA;AAAA,IACT;AAEA,WAAO,OAAO,KAAK,SAAS,UAAU,CAAC;AAAA,EACzC;AAAA,EAEA,aAAa,YAAoB,OAAsB;AACrD,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,WAAW,KAAK;AAChB,UAAA,gBAAgB,gBAAgB,MAAM;AAIxC,QAAA,cAAc,QAAQ,UAAU,OAAO,UAAU,MAAM,SAAS,QAAQ,UAAU,MAAM,OAAO;AAC1F,aAAA;AAAA,IACT;AAGA,QAAI,UAAU,MAAM;AAClB,eAAS,UAAU,EAAE,QAAQ,eAAe,aAAa,EAAE;AAAA,IAAA,OACtD;AACY,uBAAA,QAAQ,eAAe,aAAa,EAAE;AAAA,IACzD;AAEA,oBAAgB,cAAc;AAC9B,YAAQ,UAAU,IAAI;AAEf,WAAA;AAAA,EACT;AAAA,EAEA,aAAa,YAA6B;AACxC,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AAGpB,QAAI,cAAc,QAAQ,UAAU,OAAO,UAAU,MAAM,OAAO;AACzD,aAAA;AAAA,IACT;AAEA,WAAO,QAAQ,UAAU;AAAA,EAC3B;AAAA,EAEA,UAAgB;AACd,YAAQ,KAAK,gDAAgD;AAAA,EAC/D;AAAA,EAEA,OAAc;AAGN,UAAA,IAAI,MAAM,6CAA6C;AAAA,EAC/D;AAAA,EAEA,SAAgB;AACR,UAAA,IAAI,MAAM,+CAA+C;AAAA,EACjE;AAAA,EAEA,UAAgB;AAEd,SAAK,SAAS;AAEd,SAAK,iBAAkB;AACvB,SAAK,mBAAmB;AAEjB,WAAA;AAAA,EACT;AACF;;"}