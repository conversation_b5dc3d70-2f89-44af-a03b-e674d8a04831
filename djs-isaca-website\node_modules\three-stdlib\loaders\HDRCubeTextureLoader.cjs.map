{"version": 3, "file": "HDRCubeTextureLoader.cjs", "sources": ["../../src/loaders/HDRCubeTextureLoader.js"], "sourcesContent": ["import { CubeTexture, DataTexture, FileLoader, FloatType, HalfFloatType, LinearFilter, Loader } from 'three'\nimport { RGBELoader } from '../loaders/RGBELoader.js'\n\nclass HDRCubeTextureLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.hdrLoader = new RGBELoader()\n    this.type = HalfFloatType\n  }\n\n  load(urls, onLoad, onProgress, onError) {\n    if (typeof urls === 'string') {\n      urls = [urls]\n    } else if (!Array.isArray(urls)) {\n      console.warn('THREE.HDRCubeTextureLoader signature has changed. Use .setDataType() instead.')\n\n      this.setDataType(urls)\n\n      urls = onLoad\n      onLoad = onProgress\n      onProgress = onError\n      onError = arguments[4]\n    }\n\n    const texture = new CubeTexture()\n\n    texture.type = this.type\n\n    switch (texture.type) {\n      case FloatType:\n      case HalfFloatType:\n        if ('colorSpace' in texture) texture.colorSpace = 'srgb-linear'\n        else texture.encoding = 3000 // LinearEncoding\n        texture.minFilter = LinearFilter\n        texture.magFilter = LinearFilter\n        texture.generateMipmaps = false\n        break\n    }\n\n    const scope = this\n\n    let loaded = 0\n\n    function loadHDRData(i, onLoad, onProgress, onError) {\n      new FileLoader(scope.manager)\n        .setPath(scope.path)\n        .setResponseType('arraybuffer')\n        .setWithCredentials(scope.withCredentials)\n        .load(\n          urls[i],\n          function (buffer) {\n            loaded++\n\n            const texData = scope.hdrLoader.parse(buffer)\n\n            if (!texData) return\n\n            if (texData.data !== undefined) {\n              const dataTexture = new DataTexture(texData.data, texData.width, texData.height)\n\n              dataTexture.type = texture.type\n              if ('colorSpace' in dataTexture) dataTexture.colorSpace = texture.SRGBColorSpace\n              else dataTexture.encoding = texture.encoding\n              dataTexture.format = texture.format\n              dataTexture.minFilter = texture.minFilter\n              dataTexture.magFilter = texture.magFilter\n              dataTexture.generateMipmaps = texture.generateMipmaps\n\n              texture.images[i] = dataTexture\n            }\n\n            if (loaded === 6) {\n              texture.needsUpdate = true\n              if (onLoad) onLoad(texture)\n            }\n          },\n          onProgress,\n          onError,\n        )\n    }\n\n    for (let i = 0; i < urls.length; i++) {\n      loadHDRData(i, onLoad, onProgress, onError)\n    }\n\n    return texture\n  }\n\n  setDataType(value) {\n    this.type = value\n    this.hdrLoader.setDataType(value)\n\n    return this\n  }\n}\n\nexport { HDRCubeTextureLoader }\n"], "names": ["Loader", "RGBELoader", "HalfFloatType", "CubeTexture", "FloatType", "LinearFilter", "onLoad", "onProgress", "onError", "<PERSON><PERSON><PERSON><PERSON>", "DataTexture"], "mappings": ";;;;AAGA,MAAM,6BAA6BA,MAAAA,OAAO;AAAA,EACxC,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,YAAY,IAAIC,sBAAY;AACjC,SAAK,OAAOC,MAAa;AAAA,EAC1B;AAAA,EAED,KAAK,MAAM,QAAQ,YAAY,SAAS;AACtC,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,CAAC,IAAI;AAAA,IACb,WAAU,CAAC,MAAM,QAAQ,IAAI,GAAG;AAC/B,cAAQ,KAAK,+EAA+E;AAE5F,WAAK,YAAY,IAAI;AAErB,aAAO;AACP,eAAS;AACT,mBAAa;AACb,gBAAU,UAAU,CAAC;AAAA,IACtB;AAED,UAAM,UAAU,IAAIC,kBAAa;AAEjC,YAAQ,OAAO,KAAK;AAEpB,YAAQ,QAAQ,MAAI;AAAA,MAClB,KAAKC;MACL,KAAKF,MAAa;AAChB,YAAI,gBAAgB;AAAS,kBAAQ,aAAa;AAAA;AAC7C,kBAAQ,WAAW;AACxB,gBAAQ,YAAYG,MAAY;AAChC,gBAAQ,YAAYA,MAAY;AAChC,gBAAQ,kBAAkB;AAC1B;AAAA,IACH;AAED,UAAM,QAAQ;AAEd,QAAI,SAAS;AAEb,aAAS,YAAY,GAAGC,SAAQC,aAAYC,UAAS;AACnD,UAAIC,MAAU,WAAC,MAAM,OAAO,EACzB,QAAQ,MAAM,IAAI,EAClB,gBAAgB,aAAa,EAC7B,mBAAmB,MAAM,eAAe,EACxC;AAAA,QACC,KAAK,CAAC;AAAA,QACN,SAAU,QAAQ;AAChB;AAEA,gBAAM,UAAU,MAAM,UAAU,MAAM,MAAM;AAE5C,cAAI,CAAC;AAAS;AAEd,cAAI,QAAQ,SAAS,QAAW;AAC9B,kBAAM,cAAc,IAAIC,MAAAA,YAAY,QAAQ,MAAM,QAAQ,OAAO,QAAQ,MAAM;AAE/E,wBAAY,OAAO,QAAQ;AAC3B,gBAAI,gBAAgB;AAAa,0BAAY,aAAa,QAAQ;AAAA;AAC7D,0BAAY,WAAW,QAAQ;AACpC,wBAAY,SAAS,QAAQ;AAC7B,wBAAY,YAAY,QAAQ;AAChC,wBAAY,YAAY,QAAQ;AAChC,wBAAY,kBAAkB,QAAQ;AAEtC,oBAAQ,OAAO,CAAC,IAAI;AAAA,UACrB;AAED,cAAI,WAAW,GAAG;AAChB,oBAAQ,cAAc;AACtB,gBAAIJ;AAAQ,cAAAA,QAAO,OAAO;AAAA,UAC3B;AAAA,QACF;AAAA,QACDC;AAAA,QACAC;AAAA,MACD;AAAA,IACJ;AAED,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAY,GAAG,QAAQ,YAAY,OAAO;AAAA,IAC3C;AAED,WAAO;AAAA,EACR;AAAA,EAED,YAAY,OAAO;AACjB,SAAK,OAAO;AACZ,SAAK,UAAU,YAAY,KAAK;AAEhC,WAAO;AAAA,EACR;AACH;;"}