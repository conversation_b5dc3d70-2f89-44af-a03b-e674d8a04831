{"version": 3, "file": "Timer.cjs", "sources": ["../../src/misc/Timer.ts"], "sourcesContent": ["class Timer {\n  private _previousTime: number\n  private _currentTime: number\n  private _delta: number\n  private _elapsed: number\n  private _timescale: number\n  private _useFixedDelta: boolean\n  private _fixedDelta: number\n  private _usePageVisibilityAPI: boolean\n  private _pageVisibilityHandler: ((...args: any[]) => void) | undefined\n\n  constructor() {\n    this._previousTime = 0\n    this._currentTime = 0\n    this._delta = 0\n    this._elapsed = 0\n    this._timescale = 1\n    this._useFixedDelta = false\n    this._fixedDelta = 16.67 // ms, corresponds to approx. 60 FPS\n    this._usePageVisibilityAPI = typeof document !== 'undefined' && document.hidden !== undefined\n  }\n\n  // https://github.com/mrdoob/three.js/issues/20575\n  // use Page Visibility API to avoid large time delta values\n  connect(): this {\n    if (this._usePageVisibilityAPI) {\n      this._pageVisibilityHandler = handleVisibilityChange.bind(this)\n      document.addEventListener('visibilitychange', this._pageVisibilityHandler, false)\n    }\n    return this\n  }\n\n  dispose(): this {\n    if (this._usePageVisibilityAPI && this._pageVisibilityHandler) {\n      document.removeEventListener('visibilitychange', this._pageVisibilityHandler)\n    }\n    return this\n  }\n\n  disableFixedDelta(): this {\n    this._useFixedDelta = false\n    return this\n  }\n\n  enableFixedDelta(): this {\n    this._useFixedDelta = true\n    return this\n  }\n\n  getDelta(): number {\n    return this._delta / 1000\n  }\n\n  getElapsedTime(): number {\n    return this._elapsed / 1000\n  }\n\n  getFixedDelta(): number {\n    return this._fixedDelta / 1000\n  }\n\n  getTimescale(): number {\n    return this._timescale\n  }\n\n  reset(): this {\n    this._currentTime = this._now()\n    return this\n  }\n\n  setFixedDelta(fixedDelta: number): this {\n    this._fixedDelta = fixedDelta * 1000\n    return this\n  }\n\n  setTimescale(timescale: number): this {\n    this._timescale = timescale\n    return this\n  }\n\n  update(): this {\n    if (this._useFixedDelta === true) {\n      this._delta = this._fixedDelta\n    } else {\n      this._previousTime = this._currentTime\n      this._currentTime = this._now()\n      this._delta = this._currentTime - this._previousTime\n    }\n    this._delta *= this._timescale\n    this._elapsed += this._delta // _elapsed is the accumulation of all previous deltas\n    return this\n  }\n\n  // For THREE.Clock backward compatibility\n  get elapsedTime(): number {\n    return this.getElapsedTime()\n  }\n\n  // private\n\n  private _now(): number {\n    return (typeof performance === 'undefined' ? Date : performance).now()\n  }\n}\n\nfunction handleVisibilityChange(this: Timer): void {\n  if (document.hidden === false) this.reset()\n}\n\nexport { Timer }\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,MAAM;AAAA,EAWV,cAAc;AAVN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGN,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,wBAAwB,OAAO,aAAa,eAAe,SAAS,WAAW;AAAA,EACtF;AAAA;AAAA;AAAA,EAIA,UAAgB;AACd,QAAI,KAAK,uBAAuB;AACzB,WAAA,yBAAyB,uBAAuB,KAAK,IAAI;AAC9D,eAAS,iBAAiB,oBAAoB,KAAK,wBAAwB,KAAK;AAAA,IAClF;AACO,WAAA;AAAA,EACT;AAAA,EAEA,UAAgB;AACV,QAAA,KAAK,yBAAyB,KAAK,wBAAwB;AACpD,eAAA,oBAAoB,oBAAoB,KAAK,sBAAsB;AAAA,IAC9E;AACO,WAAA;AAAA,EACT;AAAA,EAEA,oBAA0B;AACxB,SAAK,iBAAiB;AACf,WAAA;AAAA,EACT;AAAA,EAEA,mBAAyB;AACvB,SAAK,iBAAiB;AACf,WAAA;AAAA,EACT;AAAA,EAEA,WAAmB;AACjB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,iBAAyB;AACvB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EAEA,gBAAwB;AACtB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EAEA,eAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,QAAc;AACP,SAAA,eAAe,KAAK;AAClB,WAAA;AAAA,EACT;AAAA,EAEA,cAAc,YAA0B;AACtC,SAAK,cAAc,aAAa;AACzB,WAAA;AAAA,EACT;AAAA,EAEA,aAAa,WAAyB;AACpC,SAAK,aAAa;AACX,WAAA;AAAA,EACT;AAAA,EAEA,SAAe;AACT,QAAA,KAAK,mBAAmB,MAAM;AAChC,WAAK,SAAS,KAAK;AAAA,IAAA,OACd;AACL,WAAK,gBAAgB,KAAK;AACrB,WAAA,eAAe,KAAK;AACpB,WAAA,SAAS,KAAK,eAAe,KAAK;AAAA,IACzC;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,YAAY,KAAK;AACf,WAAA;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,cAAsB;AACxB,WAAO,KAAK;EACd;AAAA;AAAA,EAIQ,OAAe;AACrB,YAAQ,OAAO,gBAAgB,cAAc,OAAO,aAAa;EACnE;AACF;AAEA,SAAS,yBAA0C;AACjD,MAAI,SAAS,WAAW;AAAO,SAAK,MAAM;AAC5C;;"}