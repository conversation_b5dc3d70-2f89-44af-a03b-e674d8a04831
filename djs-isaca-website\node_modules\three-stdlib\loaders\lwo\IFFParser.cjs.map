{"version": 3, "file": "IFFParser.cjs", "sources": ["../../../src/loaders/lwo/IFFParser.js"], "sourcesContent": ["/**\n * === IFFParser ===\n * - Parses data from the IFF buffer.\n * - LWO3 files are in IFF format and can contain the following data types, referred to by shorthand codes\n *\n * ATOMIC DATA TYPES\n *  ID Tag - 4x 7 bit uppercase ASCII chars: ID4\n *  signed integer, 1, 2, or 4 byte length: I1, I2, I4\n *  unsigned integer, 1, 2, or 4 byte length: U1, U2, U4\n *  float, 4 byte length: F4\n *  string, series of ASCII chars followed by null byte (If the length of the string including the null terminating byte is odd, an extra null is added so that the data that follows will begin on an even byte boundary): S0\n *\n * COMPOUND DATA TYPES\n *  Variable-length Index (index into an array or collection): U2 or U4 : VX\n *  Color (RGB): F4 + F4 + F4: COL12\n *  Coordinate (x, y, z): F4 + F4 + F4: VEC12\n *  Percentage F4 data type from 0->1 with 1 = 100%: FP4\n *  Angle in radian F4: ANG4\n *  Filename (string) S0: FNAM0\n *  XValue F4 + index (VX) + optional envelope( ENVL ): XVAL\n *  XValue vector VEC12 + index (VX) + optional envelope( ENVL ): XVAL3\n *\n *  The IFF file is arranged in chunks:\n *  CHUNK = ID4 + length (U4) + length X bytes of data + optional 0 pad byte\n *  optional 0 pad byte is there to ensure chunk ends on even boundary, not counted in size\n *\n * COMPOUND DATA TYPES\n * - Chunks are combined in Forms (collections of chunks)\n * - FORM = string 'FORM' (ID4) + length (U4) + type (ID4) + optional ( CHUNK | FORM )\n * - CHUNKS and FORMS are collectively referred to as blocks\n * - The entire file is contained in one top level FORM\n *\n **/\n\nimport { LWO2Parser } from './LWO2Parser'\nimport { LWO3Parser } from './LWO3Parser'\n\nclass IFFParser {\n  constructor() {\n    this.debugger = new Debugger()\n    // this.debugger.enable(); // un-comment to log IFF hierarchy.\n  }\n\n  parse(buffer) {\n    this.reader = new DataViewReader(buffer)\n\n    this.tree = {\n      materials: {},\n      layers: [],\n      tags: [],\n      textures: [],\n    }\n\n    // start out at the top level to add any data before first layer is encountered\n    this.currentLayer = this.tree\n    this.currentForm = this.tree\n\n    this.parseTopForm()\n\n    if (this.tree.format === undefined) return\n\n    if (this.tree.format === 'LWO2') {\n      this.parser = new LWO2Parser(this)\n      while (!this.reader.endOfFile()) this.parser.parseBlock()\n    } else if (this.tree.format === 'LWO3') {\n      this.parser = new LWO3Parser(this)\n      while (!this.reader.endOfFile()) this.parser.parseBlock()\n    }\n\n    this.debugger.offset = this.reader.offset\n    this.debugger.closeForms()\n\n    return this.tree\n  }\n\n  parseTopForm() {\n    this.debugger.offset = this.reader.offset\n\n    var topForm = this.reader.getIDTag()\n\n    if (topForm !== 'FORM') {\n      console.warn('LWOLoader: Top-level FORM missing.')\n      return\n    }\n\n    var length = this.reader.getUint32()\n\n    this.debugger.dataOffset = this.reader.offset\n    this.debugger.length = length\n\n    var type = this.reader.getIDTag()\n\n    if (type === 'LWO2') {\n      this.tree.format = type\n    } else if (type === 'LWO3') {\n      this.tree.format = type\n    }\n\n    this.debugger.node = 0\n    this.debugger.nodeID = type\n    this.debugger.log()\n\n    return\n  }\n\n  ///\n  // FORM PARSING METHODS\n  ///\n\n  // Forms are organisational and can contain any number of sub chunks and sub forms\n  // FORM ::= 'FORM'[ID4], length[U4], type[ID4], ( chunk[CHUNK] | form[FORM] ) * }\n  parseForm(length) {\n    var type = this.reader.getIDTag()\n\n    switch (type) {\n      // SKIPPED FORMS\n      // if skipForm( length ) is called, the entire form and any sub forms and chunks are skipped\n\n      case 'ISEQ': // Image sequence\n      case 'ANIM': // plug in animation\n      case 'STCC': // Color-cycling Still\n      case 'VPVL':\n      case 'VPRM':\n      case 'NROT':\n      case 'WRPW': // image wrap w ( for cylindrical and spherical projections)\n      case 'WRPH': // image wrap h\n      case 'FUNC':\n      case 'FALL':\n      case 'OPAC':\n      case 'GRAD': // gradient texture\n      case 'ENVS':\n      case 'VMOP':\n      case 'VMBG':\n\n      // Car Material FORMS\n      case 'OMAX':\n      case 'STEX':\n      case 'CKBG':\n      case 'CKEY':\n      case 'VMLA':\n      case 'VMLB':\n        this.debugger.skipped = true\n        this.skipForm(length) // not currently supported\n        break\n\n      // if break; is called directly, the position in the lwoTree is not created\n      // any sub chunks and forms are added to the parent form instead\n      case 'META':\n      case 'NNDS':\n      case 'NODS':\n      case 'NDTA':\n      case 'ADAT':\n      case 'AOVS':\n      case 'BLOK':\n\n      // used by texture nodes\n      case 'IBGC': // imageBackgroundColor\n      case 'IOPC': // imageOpacity\n      case 'IIMG': // hold reference to image path\n      case 'TXTR':\n        // this.setupForm( type, length );\n        this.debugger.length = 4\n        this.debugger.skipped = true\n        break\n\n      case 'IFAL': // imageFallof\n      case 'ISCL': // imageScale\n      case 'IPOS': // imagePosition\n      case 'IROT': // imageRotation\n      case 'IBMP':\n      case 'IUTD':\n      case 'IVTD':\n        this.parseTextureNodeAttribute(type)\n        break\n\n      case 'ENVL':\n        this.parseEnvelope(length)\n        break\n\n      // CLIP FORM AND SUB FORMS\n\n      case 'CLIP':\n        if (this.tree.format === 'LWO2') {\n          this.parseForm(length)\n        } else {\n          this.parseClip(length)\n        }\n\n        break\n\n      case 'STIL':\n        this.parseImage()\n        break\n\n      case 'XREF': // clone of another STIL\n        this.reader.skip(8) // unknown\n        this.currentForm.referenceTexture = {\n          index: this.reader.getUint32(),\n          refName: this.reader.getString(), // internal unique ref\n        }\n        break\n\n      // Not in spec, used by texture nodes\n\n      case 'IMST':\n        this.parseImageStateForm(length)\n        break\n\n      // SURF FORM AND SUB FORMS\n\n      case 'SURF':\n        this.parseSurfaceForm(length)\n        break\n\n      case 'VALU': // Not in spec\n        this.parseValueForm(length)\n        break\n\n      case 'NTAG':\n        this.parseSubNode(length)\n        break\n\n      case 'ATTR': // BSDF Node Attributes\n      case 'SATR': // Standard Node Attributes\n        this.setupForm('attributes', length)\n        break\n\n      case 'NCON':\n        this.parseConnections(length)\n        break\n\n      case 'SSHA':\n        this.parentForm = this.currentForm\n        this.currentForm = this.currentSurface\n        this.setupForm('surfaceShader', length)\n        break\n\n      case 'SSHD':\n        this.setupForm('surfaceShaderData', length)\n        break\n\n      case 'ENTR': // Not in spec\n        this.parseEntryForm(length)\n        break\n\n      // Image Map Layer\n\n      case 'IMAP':\n        this.parseImageMap(length)\n        break\n\n      case 'TAMP':\n        this.parseXVAL('amplitude', length)\n        break\n\n      //Texture Mapping Form\n\n      case 'TMAP':\n        this.setupForm('textureMap', length)\n        break\n\n      case 'CNTR':\n        this.parseXVAL3('center', length)\n        break\n\n      case 'SIZE':\n        this.parseXVAL3('scale', length)\n        break\n\n      case 'ROTA':\n        this.parseXVAL3('rotation', length)\n        break\n\n      default:\n        this.parseUnknownForm(type, length)\n    }\n\n    this.debugger.node = 0\n    this.debugger.nodeID = type\n    this.debugger.log()\n  }\n\n  setupForm(type, length) {\n    if (!this.currentForm) this.currentForm = this.currentNode\n\n    this.currentFormEnd = this.reader.offset + length\n    this.parentForm = this.currentForm\n\n    if (!this.currentForm[type]) {\n      this.currentForm[type] = {}\n      this.currentForm = this.currentForm[type]\n    } else {\n      // should never see this unless there's a bug in the reader\n      console.warn('LWOLoader: form already exists on parent: ', type, this.currentForm)\n\n      this.currentForm = this.currentForm[type]\n    }\n  }\n\n  skipForm(length) {\n    this.reader.skip(length - 4)\n  }\n\n  parseUnknownForm(type, length) {\n    console.warn('LWOLoader: unknown FORM encountered: ' + type, length)\n\n    printBuffer(this.reader.dv.buffer, this.reader.offset, length - 4)\n    this.reader.skip(length - 4)\n  }\n\n  parseSurfaceForm(length) {\n    this.reader.skip(8) // unknown Uint32 x2\n\n    var name = this.reader.getString()\n\n    var surface = {\n      attributes: {}, // LWO2 style non-node attributes will go here\n      connections: {},\n      name: name,\n      inputName: name,\n      nodes: {},\n      source: this.reader.getString(),\n    }\n\n    this.tree.materials[name] = surface\n    this.currentSurface = surface\n\n    this.parentForm = this.tree.materials\n    this.currentForm = surface\n    this.currentFormEnd = this.reader.offset + length\n  }\n\n  parseSurfaceLwo2(length) {\n    var name = this.reader.getString()\n\n    var surface = {\n      attributes: {}, // LWO2 style non-node attributes will go here\n      connections: {},\n      name: name,\n      nodes: {},\n      source: this.reader.getString(),\n    }\n\n    this.tree.materials[name] = surface\n    this.currentSurface = surface\n\n    this.parentForm = this.tree.materials\n    this.currentForm = surface\n    this.currentFormEnd = this.reader.offset + length\n  }\n\n  parseSubNode(length) {\n    // parse the NRNM CHUNK of the subnode FORM to get\n    // a meaningful name for the subNode\n    // some subnodes can be renamed, but Input and Surface cannot\n\n    this.reader.skip(8) // NRNM + length\n    var name = this.reader.getString()\n\n    var node = {\n      name: name,\n    }\n    this.currentForm = node\n    this.currentNode = node\n\n    this.currentFormEnd = this.reader.offset + length\n  }\n\n  // collect attributes from all nodes at the top level of a surface\n  parseConnections(length) {\n    this.currentFormEnd = this.reader.offset + length\n    this.parentForm = this.currentForm\n\n    this.currentForm = this.currentSurface.connections\n  }\n\n  // surface node attribute data, e.g. specular, roughness etc\n  parseEntryForm(length) {\n    this.reader.skip(8) // NAME + length\n    var name = this.reader.getString()\n    this.currentForm = this.currentNode.attributes\n\n    this.setupForm(name, length)\n  }\n\n  // parse values from material - doesn't match up to other LWO3 data types\n  // sub form of entry form\n  parseValueForm() {\n    this.reader.skip(8) // unknown + length\n\n    var valueType = this.reader.getString()\n\n    if (valueType === 'double') {\n      this.currentForm.value = this.reader.getUint64()\n    } else if (valueType === 'int') {\n      this.currentForm.value = this.reader.getUint32()\n    } else if (valueType === 'vparam') {\n      this.reader.skip(24)\n      this.currentForm.value = this.reader.getFloat64()\n    } else if (valueType === 'vparam3') {\n      this.reader.skip(24)\n      this.currentForm.value = this.reader.getFloat64Array(3)\n    }\n  }\n\n  // holds various data about texture node image state\n  // Data other thanmipMapLevel unknown\n  parseImageStateForm() {\n    this.reader.skip(8) // unknown\n\n    this.currentForm.mipMapLevel = this.reader.getFloat32()\n  }\n\n  // LWO2 style image data node OR LWO3 textures defined at top level in editor (not as SURF node)\n  parseImageMap(length) {\n    this.currentFormEnd = this.reader.offset + length\n    this.parentForm = this.currentForm\n\n    if (!this.currentForm.maps) this.currentForm.maps = []\n\n    var map = {}\n    this.currentForm.maps.push(map)\n    this.currentForm = map\n\n    this.reader.skip(10) // unknown, could be an issue if it contains a VX\n  }\n\n  parseTextureNodeAttribute(type) {\n    this.reader.skip(28) // FORM + length + VPRM + unknown + Uint32 x2 + float32\n\n    this.reader.skip(20) // FORM + length + VPVL + float32 + Uint32\n\n    switch (type) {\n      case 'ISCL':\n        this.currentNode.scale = this.reader.getFloat32Array(3)\n        break\n      case 'IPOS':\n        this.currentNode.position = this.reader.getFloat32Array(3)\n        break\n      case 'IROT':\n        this.currentNode.rotation = this.reader.getFloat32Array(3)\n        break\n      case 'IFAL':\n        this.currentNode.falloff = this.reader.getFloat32Array(3)\n        break\n\n      case 'IBMP':\n        this.currentNode.amplitude = this.reader.getFloat32()\n        break\n      case 'IUTD':\n        this.currentNode.uTiles = this.reader.getFloat32()\n        break\n      case 'IVTD':\n        this.currentNode.vTiles = this.reader.getFloat32()\n        break\n    }\n\n    this.reader.skip(2) // unknown\n  }\n\n  // ENVL forms are currently ignored\n  parseEnvelope(length) {\n    this.reader.skip(length - 4) // skipping  entirely for now\n  }\n\n  ///\n  // CHUNK PARSING METHODS\n  ///\n\n  // clips can either be defined inside a surface node, or at the top\n  // level and they have a different format in each case\n  parseClip(length) {\n    var tag = this.reader.getIDTag()\n\n    // inside surface node\n    if (tag === 'FORM') {\n      this.reader.skip(16)\n\n      this.currentNode.fileName = this.reader.getString()\n\n      return\n    }\n\n    // otherwise top level\n    this.reader.setOffset(this.reader.offset - 4)\n\n    this.currentFormEnd = this.reader.offset + length\n    this.parentForm = this.currentForm\n\n    this.reader.skip(8) // unknown\n\n    var texture = {\n      index: this.reader.getUint32(),\n    }\n    this.tree.textures.push(texture)\n    this.currentForm = texture\n  }\n\n  parseClipLwo2(length) {\n    var texture = {\n      index: this.reader.getUint32(),\n      fileName: '',\n    }\n\n    // seach STIL block\n    while (true) {\n      var tag = this.reader.getIDTag()\n      var n_length = this.reader.getUint16()\n      if (tag === 'STIL') {\n        texture.fileName = this.reader.getString()\n        break\n      }\n\n      if (n_length >= length) {\n        break\n      }\n    }\n\n    this.tree.textures.push(texture)\n    this.currentForm = texture\n  }\n\n  parseImage() {\n    this.reader.skip(8) // unknown\n    this.currentForm.fileName = this.reader.getString()\n  }\n\n  parseXVAL(type, length) {\n    var endOffset = this.reader.offset + length - 4\n    this.reader.skip(8)\n\n    this.currentForm[type] = this.reader.getFloat32()\n\n    this.reader.setOffset(endOffset) // set end offset directly to skip optional envelope\n  }\n\n  parseXVAL3(type, length) {\n    var endOffset = this.reader.offset + length - 4\n    this.reader.skip(8)\n\n    this.currentForm[type] = {\n      x: this.reader.getFloat32(),\n      y: this.reader.getFloat32(),\n      z: this.reader.getFloat32(),\n    }\n\n    this.reader.setOffset(endOffset)\n  }\n\n  // Tags associated with an object\n  // OTAG { type[ID4], tag-string[S0] }\n  parseObjectTag() {\n    if (!this.tree.objectTags) this.tree.objectTags = {}\n\n    this.tree.objectTags[this.reader.getIDTag()] = {\n      tagString: this.reader.getString(),\n    }\n  }\n\n  // Signals the start of a new layer. All the data chunks which follow will be included in this layer until another layer chunk is encountered.\n  // LAYR: number[U2], flags[U2], pivot[VEC12], name[S0], parent[U2]\n  parseLayer(length) {\n    var layer = {\n      number: this.reader.getUint16(),\n      flags: this.reader.getUint16(), // If the least significant bit of flags is set, the layer is hidden.\n      pivot: this.reader.getFloat32Array(3), // Note: this seems to be superflous, as the geometry is translated when pivot is present\n      name: this.reader.getString(),\n    }\n\n    this.tree.layers.push(layer)\n    this.currentLayer = layer\n\n    var parsedLength = 16 + stringOffset(this.currentLayer.name) // index ( 2 ) + flags( 2 ) + pivot( 12 ) + stringlength\n\n    // if we have not reached then end of the layer block, there must be a parent defined\n    this.currentLayer.parent = parsedLength < length ? this.reader.getUint16() : -1 // omitted or -1 for no parent\n  }\n\n  // VEC12 * ( F4 + F4 + F4 ) array of x,y,z vectors\n  // Converting from left to right handed coordinate system:\n  // x -> -x and switch material FrontSide -> BackSide\n  parsePoints(length) {\n    this.currentPoints = []\n    for (var i = 0; i < length / 4; i += 3) {\n      // z -> -z to match three.js right handed coords\n      this.currentPoints.push(this.reader.getFloat32(), this.reader.getFloat32(), -this.reader.getFloat32())\n    }\n  }\n\n  // parse VMAP or VMAD\n  // Associates a set of floating-point vectors with a set of points.\n  // VMAP: { type[ID4], dimension[U2], name[S0], ( vert[VX], value[F4] # dimension ) * }\n\n  // VMAD Associates a set of floating-point vectors with the vertices of specific polygons.\n  // Similar to VMAP UVs, but associates with polygon vertices rather than points\n  // to solve to problem of UV seams:  VMAD chunks are paired with VMAPs of the same name,\n  // if they exist. The vector values in the VMAD will then replace those in the\n  // corresponding VMAP, but only for calculations involving the specified polygons.\n  // VMAD { type[ID4], dimension[U2], name[S0], ( vert[VX], poly[VX], value[F4] # dimension ) * }\n  parseVertexMapping(length, discontinuous) {\n    var finalOffset = this.reader.offset + length\n\n    var channelName = this.reader.getString()\n\n    if (this.reader.offset === finalOffset) {\n      // then we are in a texture node and the VMAP chunk is just a reference to a UV channel name\n      this.currentForm.UVChannel = channelName\n      return\n    }\n\n    // otherwise reset to initial length and parse normal VMAP CHUNK\n    this.reader.setOffset(this.reader.offset - stringOffset(channelName))\n\n    var type = this.reader.getIDTag()\n\n    this.reader.getUint16() // dimension\n    var name = this.reader.getString()\n\n    var remainingLength = length - 6 - stringOffset(name)\n\n    switch (type) {\n      case 'TXUV':\n        this.parseUVMapping(name, finalOffset, discontinuous)\n        break\n      case 'MORF':\n      case 'SPOT':\n        this.parseMorphTargets(name, finalOffset, type) // can't be discontinuous\n        break\n      // unsupported VMAPs\n      case 'APSL':\n      case 'NORM':\n      case 'WGHT':\n      case 'MNVW':\n      case 'PICK':\n      case 'RGB ':\n      case 'RGBA':\n        this.reader.skip(remainingLength)\n        break\n      default:\n        console.warn('LWOLoader: unknown vertex map type: ' + type)\n        this.reader.skip(remainingLength)\n    }\n  }\n\n  parseUVMapping(name, finalOffset, discontinuous) {\n    var uvIndices = []\n    var polyIndices = []\n    var uvs = []\n\n    while (this.reader.offset < finalOffset) {\n      uvIndices.push(this.reader.getVariableLengthIndex())\n\n      if (discontinuous) polyIndices.push(this.reader.getVariableLengthIndex())\n\n      uvs.push(this.reader.getFloat32(), this.reader.getFloat32())\n    }\n\n    if (discontinuous) {\n      if (!this.currentLayer.discontinuousUVs) this.currentLayer.discontinuousUVs = {}\n\n      this.currentLayer.discontinuousUVs[name] = {\n        uvIndices: uvIndices,\n        polyIndices: polyIndices,\n        uvs: uvs,\n      }\n    } else {\n      if (!this.currentLayer.uvs) this.currentLayer.uvs = {}\n\n      this.currentLayer.uvs[name] = {\n        uvIndices: uvIndices,\n        uvs: uvs,\n      }\n    }\n  }\n\n  parseMorphTargets(name, finalOffset, type) {\n    var indices = []\n    var points = []\n\n    type = type === 'MORF' ? 'relative' : 'absolute'\n\n    while (this.reader.offset < finalOffset) {\n      indices.push(this.reader.getVariableLengthIndex())\n      // z -> -z to match three.js right handed coords\n      points.push(this.reader.getFloat32(), this.reader.getFloat32(), -this.reader.getFloat32())\n    }\n\n    if (!this.currentLayer.morphTargets) this.currentLayer.morphTargets = {}\n\n    this.currentLayer.morphTargets[name] = {\n      indices: indices,\n      points: points,\n      type: type,\n    }\n  }\n\n  // A list of polygons for the current layer.\n  // POLS { type[ID4], ( numvert+flags[U2], vert[VX] # numvert ) * }\n  parsePolygonList(length) {\n    var finalOffset = this.reader.offset + length\n    var type = this.reader.getIDTag()\n\n    var indices = []\n\n    // hold a list of polygon sizes, to be split up later\n    var polygonDimensions = []\n\n    while (this.reader.offset < finalOffset) {\n      var numverts = this.reader.getUint16()\n\n      //var flags = numverts & 64512; // 6 high order bits are flags - ignoring for now\n      numverts = numverts & 1023 // remaining ten low order bits are vertex num\n      polygonDimensions.push(numverts)\n\n      for (var j = 0; j < numverts; j++) indices.push(this.reader.getVariableLengthIndex())\n    }\n\n    var geometryData = {\n      type: type,\n      vertexIndices: indices,\n      polygonDimensions: polygonDimensions,\n      points: this.currentPoints,\n    }\n\n    // Note: assuming that all polys will be lines or points if the first is\n    if (polygonDimensions[0] === 1) geometryData.type = 'points'\n    else if (polygonDimensions[0] === 2) geometryData.type = 'lines'\n\n    this.currentLayer.geometry = geometryData\n  }\n\n  // Lists the tag strings that can be associated with polygons by the PTAG chunk.\n  // TAGS { tag-string[S0] * }\n  parseTagStrings(length) {\n    this.tree.tags = this.reader.getStringArray(length)\n  }\n\n  // Associates tags of a given type with polygons in the most recent POLS chunk.\n  // PTAG { type[ID4], ( poly[VX], tag[U2] ) * }\n  parsePolygonTagMapping(length) {\n    var finalOffset = this.reader.offset + length\n    var type = this.reader.getIDTag()\n    if (type === 'SURF') this.parseMaterialIndices(finalOffset)\n    else {\n      //PART, SMGP, COLR not supported\n\n      this.reader.skip(length - 4)\n    }\n  }\n\n  parseMaterialIndices(finalOffset) {\n    // array holds polygon index followed by material index\n    this.currentLayer.geometry.materialIndices = []\n\n    while (this.reader.offset < finalOffset) {\n      var polygonIndex = this.reader.getVariableLengthIndex()\n      var materialIndex = this.reader.getUint16()\n\n      this.currentLayer.geometry.materialIndices.push(polygonIndex, materialIndex)\n    }\n  }\n\n  parseUnknownCHUNK(blockID, length) {\n    console.warn('LWOLoader: unknown chunk type: ' + blockID + ' length: ' + length)\n\n    // print the chunk plus some bytes padding either side\n    // printBuffer( this.reader.dv.buffer, this.reader.offset - 20, length + 40 );\n\n    var data = this.reader.getString(length)\n\n    this.currentForm[blockID] = data\n  }\n}\n\nclass DataViewReader {\n  constructor(buffer) {\n    this.dv = new DataView(buffer)\n    this.offset = 0\n    this._textDecoder = new TextDecoder()\n    this._bytes = new Uint8Array(buffer)\n  }\n\n  size() {\n    return this.dv.buffer.byteLength\n  }\n\n  setOffset(offset) {\n    if (offset > 0 && offset < this.dv.buffer.byteLength) {\n      this.offset = offset\n    } else {\n      console.error('LWOLoader: invalid buffer offset')\n    }\n  }\n\n  endOfFile() {\n    if (this.offset >= this.size()) return true\n    return false\n  }\n\n  skip(length) {\n    this.offset += length\n  }\n\n  getUint8() {\n    var value = this.dv.getUint8(this.offset)\n    this.offset += 1\n    return value\n  }\n\n  getUint16() {\n    var value = this.dv.getUint16(this.offset)\n    this.offset += 2\n    return value\n  }\n\n  getInt32() {\n    var value = this.dv.getInt32(this.offset, false)\n    this.offset += 4\n    return value\n  }\n\n  getUint32() {\n    var value = this.dv.getUint32(this.offset, false)\n    this.offset += 4\n    return value\n  }\n\n  getUint64() {\n    var low, high\n\n    high = this.getUint32()\n    low = this.getUint32()\n    return high * 0x100000000 + low\n  }\n\n  getFloat32() {\n    var value = this.dv.getFloat32(this.offset, false)\n    this.offset += 4\n    return value\n  }\n\n  getFloat32Array(size) {\n    var a = []\n\n    for (var i = 0; i < size; i++) {\n      a.push(this.getFloat32())\n    }\n\n    return a\n  }\n\n  getFloat64() {\n    var value = this.dv.getFloat64(this.offset, this.littleEndian)\n    this.offset += 8\n    return value\n  }\n\n  getFloat64Array(size) {\n    var a = []\n\n    for (var i = 0; i < size; i++) {\n      a.push(this.getFloat64())\n    }\n\n    return a\n  }\n\n  // get variable-length index data type\n  // VX ::= index[U2] | (index + 0xFF000000)[U4]\n  // If the index value is less than 65,280 (0xFF00),then VX === U2\n  // otherwise VX === U4 with bits 24-31 set\n  // When reading an index, if the first byte encountered is 255 (0xFF), then\n  // the four-byte form is being used and the first byte should be discarded or masked out.\n  getVariableLengthIndex() {\n    var firstByte = this.getUint8()\n\n    if (firstByte === 255) {\n      return this.getUint8() * 65536 + this.getUint8() * 256 + this.getUint8()\n    }\n\n    return firstByte * 256 + this.getUint8()\n  }\n\n  // An ID tag is a sequence of 4 bytes containing 7-bit ASCII values\n  getIDTag() {\n    return this.getString(4)\n  }\n\n  getString(size) {\n    if (size === 0) return\n\n    const start = this.offset\n\n    let result\n    let length\n\n    if (size) {\n      length = size\n      result = this._textDecoder.decode(new Uint8Array(this.dv.buffer, start, size))\n    } else {\n      // use 1:1 mapping of buffer to avoid redundant new array creation.\n      length = this._bytes.indexOf(0, start) - start\n\n      result = this._textDecoder.decode(new Uint8Array(this.dv.buffer, start, length))\n\n      // account for null byte in length\n      length++\n\n      // if string with terminating nullbyte is uneven, extra nullbyte is added, skip that too\n      length += length % 2\n    }\n\n    this.skip(length)\n\n    return result\n  }\n\n  getStringArray(size) {\n    var a = this.getString(size)\n    a = a.split('\\0')\n\n    return a.filter(Boolean) // return array with any empty strings removed\n  }\n}\n\n// ************** DEBUGGER  **************\n\nclass Debugger {\n  constructor() {\n    this.active = false\n    this.depth = 0\n    this.formList = []\n  }\n\n  enable() {\n    this.active = true\n  }\n\n  log() {\n    if (!this.active) return\n\n    var nodeType\n\n    switch (this.node) {\n      case 0:\n        nodeType = 'FORM'\n        break\n\n      case 1:\n        nodeType = 'CHK'\n        break\n\n      case 2:\n        nodeType = 'S-CHK'\n        break\n    }\n\n    console.log(\n      '| '.repeat(this.depth) + nodeType,\n      this.nodeID,\n      `( ${this.offset} ) -> ( ${this.dataOffset + this.length} )`,\n      this.node == 0 ? ' {' : '',\n      this.skipped ? 'SKIPPED' : '',\n      this.node == 0 && this.skipped ? '}' : '',\n    )\n\n    if (this.node == 0 && !this.skipped) {\n      this.depth += 1\n      this.formList.push(this.dataOffset + this.length)\n    }\n\n    this.skipped = false\n  }\n\n  closeForms() {\n    if (!this.active) return\n\n    for (var i = this.formList.length - 1; i >= 0; i--) {\n      if (this.offset >= this.formList[i]) {\n        this.depth -= 1\n        console.log('| '.repeat(this.depth) + '}')\n        this.formList.splice(-1, 1)\n      }\n    }\n  }\n}\n\n// ************** UTILITY FUNCTIONS **************\n\nfunction isEven(num) {\n  return num % 2\n}\n\n// calculate the length of the string in the buffer\n// this will be string.length + nullbyte + optional padbyte to make the length even\nfunction stringOffset(string) {\n  return string.length + 1 + (isEven(string.length + 1) ? 1 : 0)\n}\n\n// for testing purposes, dump buffer to console\n// printBuffer( this.reader.dv.buffer, this.reader.offset, length );\nfunction printBuffer(buffer, from, to) {\n  console.log(new TextDecoder().decode(new Uint8Array(buffer, from, to)))\n}\n\nexport { IFFParser }\n"], "names": ["LWO2Parser", "LWO3Parser"], "mappings": ";;;;AAqCA,MAAM,UAAU;AAAA,EACd,cAAc;AACZ,SAAK,WAAW,IAAI,SAAU;AAAA,EAE/B;AAAA,EAED,MAAM,QAAQ;AACZ,SAAK,SAAS,IAAI,eAAe,MAAM;AAEvC,SAAK,OAAO;AAAA,MACV,WAAW,CAAE;AAAA,MACb,QAAQ,CAAE;AAAA,MACV,MAAM,CAAE;AAAA,MACR,UAAU,CAAE;AAAA,IACb;AAGD,SAAK,eAAe,KAAK;AACzB,SAAK,cAAc,KAAK;AAExB,SAAK,aAAc;AAEnB,QAAI,KAAK,KAAK,WAAW;AAAW;AAEpC,QAAI,KAAK,KAAK,WAAW,QAAQ;AAC/B,WAAK,SAAS,IAAIA,WAAU,WAAC,IAAI;AACjC,aAAO,CAAC,KAAK,OAAO,UAAW;AAAE,aAAK,OAAO,WAAY;AAAA,IAC1D,WAAU,KAAK,KAAK,WAAW,QAAQ;AACtC,WAAK,SAAS,IAAIC,WAAU,WAAC,IAAI;AACjC,aAAO,CAAC,KAAK,OAAO,UAAW;AAAE,aAAK,OAAO,WAAY;AAAA,IAC1D;AAED,SAAK,SAAS,SAAS,KAAK,OAAO;AACnC,SAAK,SAAS,WAAY;AAE1B,WAAO,KAAK;AAAA,EACb;AAAA,EAED,eAAe;AACb,SAAK,SAAS,SAAS,KAAK,OAAO;AAEnC,QAAI,UAAU,KAAK,OAAO,SAAU;AAEpC,QAAI,YAAY,QAAQ;AACtB,cAAQ,KAAK,oCAAoC;AACjD;AAAA,IACD;AAED,QAAI,SAAS,KAAK,OAAO,UAAW;AAEpC,SAAK,SAAS,aAAa,KAAK,OAAO;AACvC,SAAK,SAAS,SAAS;AAEvB,QAAI,OAAO,KAAK,OAAO,SAAU;AAEjC,QAAI,SAAS,QAAQ;AACnB,WAAK,KAAK,SAAS;AAAA,IACzB,WAAe,SAAS,QAAQ;AAC1B,WAAK,KAAK,SAAS;AAAA,IACpB;AAED,SAAK,SAAS,OAAO;AACrB,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,IAAK;AAEnB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,UAAU,QAAQ;AAChB,QAAI,OAAO,KAAK,OAAO,SAAU;AAEjC,YAAQ,MAAI;AAAA,MAIV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAGL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK,SAAS,UAAU;AACxB,aAAK,SAAS,MAAM;AACpB;AAAA,MAIF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAGL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH,aAAK,SAAS,SAAS;AACvB,aAAK,SAAS,UAAU;AACxB;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK,0BAA0B,IAAI;AACnC;AAAA,MAEF,KAAK;AACH,aAAK,cAAc,MAAM;AACzB;AAAA,MAIF,KAAK;AACH,YAAI,KAAK,KAAK,WAAW,QAAQ;AAC/B,eAAK,UAAU,MAAM;AAAA,QAC/B,OAAe;AACL,eAAK,UAAU,MAAM;AAAA,QACtB;AAED;AAAA,MAEF,KAAK;AACH,aAAK,WAAY;AACjB;AAAA,MAEF,KAAK;AACH,aAAK,OAAO,KAAK,CAAC;AAClB,aAAK,YAAY,mBAAmB;AAAA,UAClC,OAAO,KAAK,OAAO,UAAW;AAAA,UAC9B,SAAS,KAAK,OAAO,UAAW;AAAA;AAAA,QACjC;AACD;AAAA,MAIF,KAAK;AACH,aAAK,oBAAoB,MAAM;AAC/B;AAAA,MAIF,KAAK;AACH,aAAK,iBAAiB,MAAM;AAC5B;AAAA,MAEF,KAAK;AACH,aAAK,eAAe,MAAM;AAC1B;AAAA,MAEF,KAAK;AACH,aAAK,aAAa,MAAM;AACxB;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AACH,aAAK,UAAU,cAAc,MAAM;AACnC;AAAA,MAEF,KAAK;AACH,aAAK,iBAAiB,MAAM;AAC5B;AAAA,MAEF,KAAK;AACH,aAAK,aAAa,KAAK;AACvB,aAAK,cAAc,KAAK;AACxB,aAAK,UAAU,iBAAiB,MAAM;AACtC;AAAA,MAEF,KAAK;AACH,aAAK,UAAU,qBAAqB,MAAM;AAC1C;AAAA,MAEF,KAAK;AACH,aAAK,eAAe,MAAM;AAC1B;AAAA,MAIF,KAAK;AACH,aAAK,cAAc,MAAM;AACzB;AAAA,MAEF,KAAK;AACH,aAAK,UAAU,aAAa,MAAM;AAClC;AAAA,MAIF,KAAK;AACH,aAAK,UAAU,cAAc,MAAM;AACnC;AAAA,MAEF,KAAK;AACH,aAAK,WAAW,UAAU,MAAM;AAChC;AAAA,MAEF,KAAK;AACH,aAAK,WAAW,SAAS,MAAM;AAC/B;AAAA,MAEF,KAAK;AACH,aAAK,WAAW,YAAY,MAAM;AAClC;AAAA,MAEF;AACE,aAAK,iBAAiB,MAAM,MAAM;AAAA,IACrC;AAED,SAAK,SAAS,OAAO;AACrB,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,IAAK;AAAA,EACpB;AAAA,EAED,UAAU,MAAM,QAAQ;AACtB,QAAI,CAAC,KAAK;AAAa,WAAK,cAAc,KAAK;AAE/C,SAAK,iBAAiB,KAAK,OAAO,SAAS;AAC3C,SAAK,aAAa,KAAK;AAEvB,QAAI,CAAC,KAAK,YAAY,IAAI,GAAG;AAC3B,WAAK,YAAY,IAAI,IAAI,CAAE;AAC3B,WAAK,cAAc,KAAK,YAAY,IAAI;AAAA,IAC9C,OAAW;AAEL,cAAQ,KAAK,8CAA8C,MAAM,KAAK,WAAW;AAEjF,WAAK,cAAc,KAAK,YAAY,IAAI;AAAA,IACzC;AAAA,EACF;AAAA,EAED,SAAS,QAAQ;AACf,SAAK,OAAO,KAAK,SAAS,CAAC;AAAA,EAC5B;AAAA,EAED,iBAAiB,MAAM,QAAQ;AAC7B,YAAQ,KAAK,0CAA0C,MAAM,MAAM;AAEnE,gBAAY,KAAK,OAAO,GAAG,QAAQ,KAAK,OAAO,QAAQ,SAAS,CAAC;AACjE,SAAK,OAAO,KAAK,SAAS,CAAC;AAAA,EAC5B;AAAA,EAED,iBAAiB,QAAQ;AACvB,SAAK,OAAO,KAAK,CAAC;AAElB,QAAI,OAAO,KAAK,OAAO,UAAW;AAElC,QAAI,UAAU;AAAA,MACZ,YAAY,CAAE;AAAA;AAAA,MACd,aAAa,CAAE;AAAA,MACf;AAAA,MACA,WAAW;AAAA,MACX,OAAO,CAAE;AAAA,MACT,QAAQ,KAAK,OAAO,UAAW;AAAA,IAChC;AAED,SAAK,KAAK,UAAU,IAAI,IAAI;AAC5B,SAAK,iBAAiB;AAEtB,SAAK,aAAa,KAAK,KAAK;AAC5B,SAAK,cAAc;AACnB,SAAK,iBAAiB,KAAK,OAAO,SAAS;AAAA,EAC5C;AAAA,EAED,iBAAiB,QAAQ;AACvB,QAAI,OAAO,KAAK,OAAO,UAAW;AAElC,QAAI,UAAU;AAAA,MACZ,YAAY,CAAE;AAAA;AAAA,MACd,aAAa,CAAE;AAAA,MACf;AAAA,MACA,OAAO,CAAE;AAAA,MACT,QAAQ,KAAK,OAAO,UAAW;AAAA,IAChC;AAED,SAAK,KAAK,UAAU,IAAI,IAAI;AAC5B,SAAK,iBAAiB;AAEtB,SAAK,aAAa,KAAK,KAAK;AAC5B,SAAK,cAAc;AACnB,SAAK,iBAAiB,KAAK,OAAO,SAAS;AAAA,EAC5C;AAAA,EAED,aAAa,QAAQ;AAKnB,SAAK,OAAO,KAAK,CAAC;AAClB,QAAI,OAAO,KAAK,OAAO,UAAW;AAElC,QAAI,OAAO;AAAA,MACT;AAAA,IACD;AACD,SAAK,cAAc;AACnB,SAAK,cAAc;AAEnB,SAAK,iBAAiB,KAAK,OAAO,SAAS;AAAA,EAC5C;AAAA;AAAA,EAGD,iBAAiB,QAAQ;AACvB,SAAK,iBAAiB,KAAK,OAAO,SAAS;AAC3C,SAAK,aAAa,KAAK;AAEvB,SAAK,cAAc,KAAK,eAAe;AAAA,EACxC;AAAA;AAAA,EAGD,eAAe,QAAQ;AACrB,SAAK,OAAO,KAAK,CAAC;AAClB,QAAI,OAAO,KAAK,OAAO,UAAW;AAClC,SAAK,cAAc,KAAK,YAAY;AAEpC,SAAK,UAAU,MAAM,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA,EAID,iBAAiB;AACf,SAAK,OAAO,KAAK,CAAC;AAElB,QAAI,YAAY,KAAK,OAAO,UAAW;AAEvC,QAAI,cAAc,UAAU;AAC1B,WAAK,YAAY,QAAQ,KAAK,OAAO,UAAW;AAAA,IACtD,WAAe,cAAc,OAAO;AAC9B,WAAK,YAAY,QAAQ,KAAK,OAAO,UAAW;AAAA,IACtD,WAAe,cAAc,UAAU;AACjC,WAAK,OAAO,KAAK,EAAE;AACnB,WAAK,YAAY,QAAQ,KAAK,OAAO,WAAY;AAAA,IACvD,WAAe,cAAc,WAAW;AAClC,WAAK,OAAO,KAAK,EAAE;AACnB,WAAK,YAAY,QAAQ,KAAK,OAAO,gBAAgB,CAAC;AAAA,IACvD;AAAA,EACF;AAAA;AAAA;AAAA,EAID,sBAAsB;AACpB,SAAK,OAAO,KAAK,CAAC;AAElB,SAAK,YAAY,cAAc,KAAK,OAAO,WAAY;AAAA,EACxD;AAAA;AAAA,EAGD,cAAc,QAAQ;AACpB,SAAK,iBAAiB,KAAK,OAAO,SAAS;AAC3C,SAAK,aAAa,KAAK;AAEvB,QAAI,CAAC,KAAK,YAAY;AAAM,WAAK,YAAY,OAAO,CAAE;AAEtD,QAAI,MAAM,CAAE;AACZ,SAAK,YAAY,KAAK,KAAK,GAAG;AAC9B,SAAK,cAAc;AAEnB,SAAK,OAAO,KAAK,EAAE;AAAA,EACpB;AAAA,EAED,0BAA0B,MAAM;AAC9B,SAAK,OAAO,KAAK,EAAE;AAEnB,SAAK,OAAO,KAAK,EAAE;AAEnB,YAAQ,MAAI;AAAA,MACV,KAAK;AACH,aAAK,YAAY,QAAQ,KAAK,OAAO,gBAAgB,CAAC;AACtD;AAAA,MACF,KAAK;AACH,aAAK,YAAY,WAAW,KAAK,OAAO,gBAAgB,CAAC;AACzD;AAAA,MACF,KAAK;AACH,aAAK,YAAY,WAAW,KAAK,OAAO,gBAAgB,CAAC;AACzD;AAAA,MACF,KAAK;AACH,aAAK,YAAY,UAAU,KAAK,OAAO,gBAAgB,CAAC;AACxD;AAAA,MAEF,KAAK;AACH,aAAK,YAAY,YAAY,KAAK,OAAO,WAAY;AACrD;AAAA,MACF,KAAK;AACH,aAAK,YAAY,SAAS,KAAK,OAAO,WAAY;AAClD;AAAA,MACF,KAAK;AACH,aAAK,YAAY,SAAS,KAAK,OAAO,WAAY;AAClD;AAAA,IACH;AAED,SAAK,OAAO,KAAK,CAAC;AAAA,EACnB;AAAA;AAAA,EAGD,cAAc,QAAQ;AACpB,SAAK,OAAO,KAAK,SAAS,CAAC;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,UAAU,QAAQ;AAChB,QAAI,MAAM,KAAK,OAAO,SAAU;AAGhC,QAAI,QAAQ,QAAQ;AAClB,WAAK,OAAO,KAAK,EAAE;AAEnB,WAAK,YAAY,WAAW,KAAK,OAAO,UAAW;AAEnD;AAAA,IACD;AAGD,SAAK,OAAO,UAAU,KAAK,OAAO,SAAS,CAAC;AAE5C,SAAK,iBAAiB,KAAK,OAAO,SAAS;AAC3C,SAAK,aAAa,KAAK;AAEvB,SAAK,OAAO,KAAK,CAAC;AAElB,QAAI,UAAU;AAAA,MACZ,OAAO,KAAK,OAAO,UAAW;AAAA,IAC/B;AACD,SAAK,KAAK,SAAS,KAAK,OAAO;AAC/B,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,cAAc,QAAQ;AACpB,QAAI,UAAU;AAAA,MACZ,OAAO,KAAK,OAAO,UAAW;AAAA,MAC9B,UAAU;AAAA,IACX;AAGD,WAAO,MAAM;AACX,UAAI,MAAM,KAAK,OAAO,SAAU;AAChC,UAAI,WAAW,KAAK,OAAO,UAAW;AACtC,UAAI,QAAQ,QAAQ;AAClB,gBAAQ,WAAW,KAAK,OAAO,UAAW;AAC1C;AAAA,MACD;AAED,UAAI,YAAY,QAAQ;AACtB;AAAA,MACD;AAAA,IACF;AAED,SAAK,KAAK,SAAS,KAAK,OAAO;AAC/B,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,aAAa;AACX,SAAK,OAAO,KAAK,CAAC;AAClB,SAAK,YAAY,WAAW,KAAK,OAAO,UAAW;AAAA,EACpD;AAAA,EAED,UAAU,MAAM,QAAQ;AACtB,QAAI,YAAY,KAAK,OAAO,SAAS,SAAS;AAC9C,SAAK,OAAO,KAAK,CAAC;AAElB,SAAK,YAAY,IAAI,IAAI,KAAK,OAAO,WAAY;AAEjD,SAAK,OAAO,UAAU,SAAS;AAAA,EAChC;AAAA,EAED,WAAW,MAAM,QAAQ;AACvB,QAAI,YAAY,KAAK,OAAO,SAAS,SAAS;AAC9C,SAAK,OAAO,KAAK,CAAC;AAElB,SAAK,YAAY,IAAI,IAAI;AAAA,MACvB,GAAG,KAAK,OAAO,WAAY;AAAA,MAC3B,GAAG,KAAK,OAAO,WAAY;AAAA,MAC3B,GAAG,KAAK,OAAO,WAAY;AAAA,IAC5B;AAED,SAAK,OAAO,UAAU,SAAS;AAAA,EAChC;AAAA;AAAA;AAAA,EAID,iBAAiB;AACf,QAAI,CAAC,KAAK,KAAK;AAAY,WAAK,KAAK,aAAa,CAAE;AAEpD,SAAK,KAAK,WAAW,KAAK,OAAO,SAAQ,CAAE,IAAI;AAAA,MAC7C,WAAW,KAAK,OAAO,UAAW;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA,EAID,WAAW,QAAQ;AACjB,QAAI,QAAQ;AAAA,MACV,QAAQ,KAAK,OAAO,UAAW;AAAA,MAC/B,OAAO,KAAK,OAAO,UAAW;AAAA;AAAA,MAC9B,OAAO,KAAK,OAAO,gBAAgB,CAAC;AAAA;AAAA,MACpC,MAAM,KAAK,OAAO,UAAW;AAAA,IAC9B;AAED,SAAK,KAAK,OAAO,KAAK,KAAK;AAC3B,SAAK,eAAe;AAEpB,QAAI,eAAe,KAAK,aAAa,KAAK,aAAa,IAAI;AAG3D,SAAK,aAAa,SAAS,eAAe,SAAS,KAAK,OAAO,UAAW,IAAG;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY,QAAQ;AAClB,SAAK,gBAAgB,CAAE;AACvB,aAAS,IAAI,GAAG,IAAI,SAAS,GAAG,KAAK,GAAG;AAEtC,WAAK,cAAc,KAAK,KAAK,OAAO,WAAY,GAAE,KAAK,OAAO,WAAU,GAAI,CAAC,KAAK,OAAO,WAAU,CAAE;AAAA,IACtG;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYD,mBAAmB,QAAQ,eAAe;AACxC,QAAI,cAAc,KAAK,OAAO,SAAS;AAEvC,QAAI,cAAc,KAAK,OAAO,UAAW;AAEzC,QAAI,KAAK,OAAO,WAAW,aAAa;AAEtC,WAAK,YAAY,YAAY;AAC7B;AAAA,IACD;AAGD,SAAK,OAAO,UAAU,KAAK,OAAO,SAAS,aAAa,WAAW,CAAC;AAEpE,QAAI,OAAO,KAAK,OAAO,SAAU;AAEjC,SAAK,OAAO,UAAW;AACvB,QAAI,OAAO,KAAK,OAAO,UAAW;AAElC,QAAI,kBAAkB,SAAS,IAAI,aAAa,IAAI;AAEpD,YAAQ,MAAI;AAAA,MACV,KAAK;AACH,aAAK,eAAe,MAAM,aAAa,aAAa;AACpD;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,aAAK,kBAAkB,MAAM,aAAa,IAAI;AAC9C;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK,OAAO,KAAK,eAAe;AAChC;AAAA,MACF;AACE,gBAAQ,KAAK,yCAAyC,IAAI;AAC1D,aAAK,OAAO,KAAK,eAAe;AAAA,IACnC;AAAA,EACF;AAAA,EAED,eAAe,MAAM,aAAa,eAAe;AAC/C,QAAI,YAAY,CAAE;AAClB,QAAI,cAAc,CAAE;AACpB,QAAI,MAAM,CAAE;AAEZ,WAAO,KAAK,OAAO,SAAS,aAAa;AACvC,gBAAU,KAAK,KAAK,OAAO,uBAAsB,CAAE;AAEnD,UAAI;AAAe,oBAAY,KAAK,KAAK,OAAO,wBAAwB;AAExE,UAAI,KAAK,KAAK,OAAO,WAAU,GAAI,KAAK,OAAO,YAAY;AAAA,IAC5D;AAED,QAAI,eAAe;AACjB,UAAI,CAAC,KAAK,aAAa;AAAkB,aAAK,aAAa,mBAAmB,CAAE;AAEhF,WAAK,aAAa,iBAAiB,IAAI,IAAI;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACP,OAAW;AACL,UAAI,CAAC,KAAK,aAAa;AAAK,aAAK,aAAa,MAAM,CAAE;AAEtD,WAAK,aAAa,IAAI,IAAI,IAAI;AAAA,QAC5B;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAAA,EACF;AAAA,EAED,kBAAkB,MAAM,aAAa,MAAM;AACzC,QAAI,UAAU,CAAE;AAChB,QAAI,SAAS,CAAE;AAEf,WAAO,SAAS,SAAS,aAAa;AAEtC,WAAO,KAAK,OAAO,SAAS,aAAa;AACvC,cAAQ,KAAK,KAAK,OAAO,uBAAsB,CAAE;AAEjD,aAAO,KAAK,KAAK,OAAO,WAAU,GAAI,KAAK,OAAO,WAAU,GAAI,CAAC,KAAK,OAAO,WAAU,CAAE;AAAA,IAC1F;AAED,QAAI,CAAC,KAAK,aAAa;AAAc,WAAK,aAAa,eAAe,CAAE;AAExE,SAAK,aAAa,aAAa,IAAI,IAAI;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA;AAAA;AAAA,EAID,iBAAiB,QAAQ;AACvB,QAAI,cAAc,KAAK,OAAO,SAAS;AACvC,QAAI,OAAO,KAAK,OAAO,SAAU;AAEjC,QAAI,UAAU,CAAE;AAGhB,QAAI,oBAAoB,CAAE;AAE1B,WAAO,KAAK,OAAO,SAAS,aAAa;AACvC,UAAI,WAAW,KAAK,OAAO,UAAW;AAGtC,iBAAW,WAAW;AACtB,wBAAkB,KAAK,QAAQ;AAE/B,eAAS,IAAI,GAAG,IAAI,UAAU;AAAK,gBAAQ,KAAK,KAAK,OAAO,uBAAsB,CAAE;AAAA,IACrF;AAED,QAAI,eAAe;AAAA,MACjB;AAAA,MACA,eAAe;AAAA,MACf;AAAA,MACA,QAAQ,KAAK;AAAA,IACd;AAGD,QAAI,kBAAkB,CAAC,MAAM;AAAG,mBAAa,OAAO;AAAA,aAC3C,kBAAkB,CAAC,MAAM;AAAG,mBAAa,OAAO;AAEzD,SAAK,aAAa,WAAW;AAAA,EAC9B;AAAA;AAAA;AAAA,EAID,gBAAgB,QAAQ;AACtB,SAAK,KAAK,OAAO,KAAK,OAAO,eAAe,MAAM;AAAA,EACnD;AAAA;AAAA;AAAA,EAID,uBAAuB,QAAQ;AAC7B,QAAI,cAAc,KAAK,OAAO,SAAS;AACvC,QAAI,OAAO,KAAK,OAAO,SAAU;AACjC,QAAI,SAAS;AAAQ,WAAK,qBAAqB,WAAW;AAAA,SACrD;AAGH,WAAK,OAAO,KAAK,SAAS,CAAC;AAAA,IAC5B;AAAA,EACF;AAAA,EAED,qBAAqB,aAAa;AAEhC,SAAK,aAAa,SAAS,kBAAkB,CAAE;AAE/C,WAAO,KAAK,OAAO,SAAS,aAAa;AACvC,UAAI,eAAe,KAAK,OAAO,uBAAwB;AACvD,UAAI,gBAAgB,KAAK,OAAO,UAAW;AAE3C,WAAK,aAAa,SAAS,gBAAgB,KAAK,cAAc,aAAa;AAAA,IAC5E;AAAA,EACF;AAAA,EAED,kBAAkB,SAAS,QAAQ;AACjC,YAAQ,KAAK,oCAAoC,UAAU,cAAc,MAAM;AAK/E,QAAI,OAAO,KAAK,OAAO,UAAU,MAAM;AAEvC,SAAK,YAAY,OAAO,IAAI;AAAA,EAC7B;AACH;AAEA,MAAM,eAAe;AAAA,EACnB,YAAY,QAAQ;AAClB,SAAK,KAAK,IAAI,SAAS,MAAM;AAC7B,SAAK,SAAS;AACd,SAAK,eAAe,IAAI,YAAa;AACrC,SAAK,SAAS,IAAI,WAAW,MAAM;AAAA,EACpC;AAAA,EAED,OAAO;AACL,WAAO,KAAK,GAAG,OAAO;AAAA,EACvB;AAAA,EAED,UAAU,QAAQ;AAChB,QAAI,SAAS,KAAK,SAAS,KAAK,GAAG,OAAO,YAAY;AACpD,WAAK,SAAS;AAAA,IACpB,OAAW;AACL,cAAQ,MAAM,kCAAkC;AAAA,IACjD;AAAA,EACF;AAAA,EAED,YAAY;AACV,QAAI,KAAK,UAAU,KAAK,KAAI;AAAI,aAAO;AACvC,WAAO;AAAA,EACR;AAAA,EAED,KAAK,QAAQ;AACX,SAAK,UAAU;AAAA,EAChB;AAAA,EAED,WAAW;AACT,QAAI,QAAQ,KAAK,GAAG,SAAS,KAAK,MAAM;AACxC,SAAK,UAAU;AACf,WAAO;AAAA,EACR;AAAA,EAED,YAAY;AACV,QAAI,QAAQ,KAAK,GAAG,UAAU,KAAK,MAAM;AACzC,SAAK,UAAU;AACf,WAAO;AAAA,EACR;AAAA,EAED,WAAW;AACT,QAAI,QAAQ,KAAK,GAAG,SAAS,KAAK,QAAQ,KAAK;AAC/C,SAAK,UAAU;AACf,WAAO;AAAA,EACR;AAAA,EAED,YAAY;AACV,QAAI,QAAQ,KAAK,GAAG,UAAU,KAAK,QAAQ,KAAK;AAChD,SAAK,UAAU;AACf,WAAO;AAAA,EACR;AAAA,EAED,YAAY;AACV,QAAI,KAAK;AAET,WAAO,KAAK,UAAW;AACvB,UAAM,KAAK,UAAW;AACtB,WAAO,OAAO,aAAc;AAAA,EAC7B;AAAA,EAED,aAAa;AACX,QAAI,QAAQ,KAAK,GAAG,WAAW,KAAK,QAAQ,KAAK;AACjD,SAAK,UAAU;AACf,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,MAAM;AACpB,QAAI,IAAI,CAAE;AAEV,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,QAAE,KAAK,KAAK,YAAY;AAAA,IACzB;AAED,WAAO;AAAA,EACR;AAAA,EAED,aAAa;AACX,QAAI,QAAQ,KAAK,GAAG,WAAW,KAAK,QAAQ,KAAK,YAAY;AAC7D,SAAK,UAAU;AACf,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,MAAM;AACpB,QAAI,IAAI,CAAE;AAEV,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,QAAE,KAAK,KAAK,YAAY;AAAA,IACzB;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,yBAAyB;AACvB,QAAI,YAAY,KAAK,SAAU;AAE/B,QAAI,cAAc,KAAK;AACrB,aAAO,KAAK,SAAU,IAAG,QAAQ,KAAK,aAAa,MAAM,KAAK,SAAU;AAAA,IACzE;AAED,WAAO,YAAY,MAAM,KAAK,SAAU;AAAA,EACzC;AAAA;AAAA,EAGD,WAAW;AACT,WAAO,KAAK,UAAU,CAAC;AAAA,EACxB;AAAA,EAED,UAAU,MAAM;AACd,QAAI,SAAS;AAAG;AAEhB,UAAM,QAAQ,KAAK;AAEnB,QAAI;AACJ,QAAI;AAEJ,QAAI,MAAM;AACR,eAAS;AACT,eAAS,KAAK,aAAa,OAAO,IAAI,WAAW,KAAK,GAAG,QAAQ,OAAO,IAAI,CAAC;AAAA,IACnF,OAAW;AAEL,eAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,IAAI;AAEzC,eAAS,KAAK,aAAa,OAAO,IAAI,WAAW,KAAK,GAAG,QAAQ,OAAO,MAAM,CAAC;AAG/E;AAGA,gBAAU,SAAS;AAAA,IACpB;AAED,SAAK,KAAK,MAAM;AAEhB,WAAO;AAAA,EACR;AAAA,EAED,eAAe,MAAM;AACnB,QAAI,IAAI,KAAK,UAAU,IAAI;AAC3B,QAAI,EAAE,MAAM,IAAI;AAEhB,WAAO,EAAE,OAAO,OAAO;AAAA,EACxB;AACH;AAIA,MAAM,SAAS;AAAA,EACb,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,WAAW,CAAE;AAAA,EACnB;AAAA,EAED,SAAS;AACP,SAAK,SAAS;AAAA,EACf;AAAA,EAED,MAAM;AACJ,QAAI,CAAC,KAAK;AAAQ;AAElB,QAAI;AAEJ,YAAQ,KAAK,MAAI;AAAA,MACf,KAAK;AACH,mBAAW;AACX;AAAA,MAEF,KAAK;AACH,mBAAW;AACX;AAAA,MAEF,KAAK;AACH,mBAAW;AACX;AAAA,IACH;AAED,YAAQ;AAAA,MACN,KAAK,OAAO,KAAK,KAAK,IAAI;AAAA,MAC1B,KAAK;AAAA,MACL,KAAK,KAAK,iBAAiB,KAAK,aAAa,KAAK;AAAA,MAClD,KAAK,QAAQ,IAAI,OAAO;AAAA,MACxB,KAAK,UAAU,YAAY;AAAA,MAC3B,KAAK,QAAQ,KAAK,KAAK,UAAU,MAAM;AAAA,IACxC;AAED,QAAI,KAAK,QAAQ,KAAK,CAAC,KAAK,SAAS;AACnC,WAAK,SAAS;AACd,WAAK,SAAS,KAAK,KAAK,aAAa,KAAK,MAAM;AAAA,IACjD;AAED,SAAK,UAAU;AAAA,EAChB;AAAA,EAED,aAAa;AACX,QAAI,CAAC,KAAK;AAAQ;AAElB,aAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAClD,UAAI,KAAK,UAAU,KAAK,SAAS,CAAC,GAAG;AACnC,aAAK,SAAS;AACd,gBAAQ,IAAI,KAAK,OAAO,KAAK,KAAK,IAAI,GAAG;AACzC,aAAK,SAAS,OAAO,IAAI,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACH;AAIA,SAAS,OAAO,KAAK;AACnB,SAAO,MAAM;AACf;AAIA,SAAS,aAAa,QAAQ;AAC5B,SAAO,OAAO,SAAS,KAAK,OAAO,OAAO,SAAS,CAAC,IAAI,IAAI;AAC9D;AAIA,SAAS,YAAY,QAAQ,MAAM,IAAI;AACrC,UAAQ,IAAI,IAAI,YAAW,EAAG,OAAO,IAAI,WAAW,QAAQ,MAAM,EAAE,CAAC,CAAC;AACxE;;"}