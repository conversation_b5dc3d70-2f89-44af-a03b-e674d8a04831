{"version": 3, "file": "OculusHandPointerModel.js", "sources": ["../../src/webxr/OculusHandPointerModel.js"], "sourcesContent": ["import * as THREE from 'three'\n\nconst PINCH_MAX = 0.05\nconst <PERSON>INCH_THRESHOLD = 0.02\nconst PINCH_MIN = 0.01\nconst POINTER_ADVANCE_MAX = 0.02\nconst POINTER_OPACITY_MAX = 1\nconst POINTER_OPACITY_MIN = 0.4\nconst POINTER_FRONT_RADIUS = 0.002\nconst POINTER_REAR_RADIUS = 0.01\nconst POINTER_REAR_RADIUS_MIN = 0.003\nconst POINTER_LENGTH = 0.035\nconst POINTER_SEGMENTS = 16\nconst POINTER_RINGS = 12\nconst POINTER_HEMISPHERE_ANGLE = 110\nconst YAXIS = /* @__PURE__ */ new THREE.Vector3(0, 1, 0)\nconst ZAXIS = /* @__PURE__ */ new THREE.Vector3(0, 0, 1)\n\nconst CURSOR_RADIUS = 0.02\nconst C<PERSON><PERSON><PERSON>_MAX_DISTANCE = 1.5\n\nclass OculusHandPointerModel extends THREE.Object3D {\n  constructor(hand, controller) {\n    super()\n\n    this.hand = hand\n    this.controller = controller\n\n    // Unused\n    this.motionController = null\n    this.envMap = null\n    this.mesh = null\n\n    this.pointerGeometry = null\n    this.pointerMesh = null\n    this.pointerObject = null\n\n    this.pinched = false\n    this.attached = false\n\n    this.cursorObject = null\n\n    this.raycaster = null\n\n    this._onConnected = this._onConnected.bind(this)\n    this._onDisconnected = this._onDisconnected.bind(this)\n    this.hand.addEventListener('connected', this._onConnected)\n    this.hand.addEventListener('disconnected', this._onDisconnected)\n  }\n\n  _onConnected(event) {\n    const xrInputSource = event.data\n    if (xrInputSource.hand) {\n      this.visible = true\n      this.xrInputSource = xrInputSource\n\n      this.createPointer()\n    }\n  }\n\n  _onDisconnected() {\n    this.visible = false\n    this.xrInputSource = null\n\n    this.pointerGeometry?.dispose()\n    this.pointerMesh?.material.dispose()\n\n    this.clear()\n  }\n\n  _drawVerticesRing(vertices, baseVector, ringIndex) {\n    const segmentVector = baseVector.clone()\n    for (var i = 0; i < POINTER_SEGMENTS; i++) {\n      segmentVector.applyAxisAngle(ZAXIS, (Math.PI * 2) / POINTER_SEGMENTS)\n      const vid = ringIndex * POINTER_SEGMENTS + i\n      vertices[3 * vid] = segmentVector.x\n      vertices[3 * vid + 1] = segmentVector.y\n      vertices[3 * vid + 2] = segmentVector.z\n    }\n  }\n\n  _updatePointerVertices(rearRadius) {\n    const vertices = this.pointerGeometry.attributes.position.array\n    // first ring for front face\n    const frontFaceBase = new THREE.Vector3(POINTER_FRONT_RADIUS, 0, -1 * (POINTER_LENGTH - rearRadius))\n    this._drawVerticesRing(vertices, frontFaceBase, 0)\n\n    // rings for rear hemisphere\n    const rearBase = new THREE.Vector3(\n      Math.sin((Math.PI * POINTER_HEMISPHERE_ANGLE) / 180) * rearRadius,\n      Math.cos((Math.PI * POINTER_HEMISPHERE_ANGLE) / 180) * rearRadius,\n      0,\n    )\n    for (var i = 0; i < POINTER_RINGS; i++) {\n      this._drawVerticesRing(vertices, rearBase, i + 1)\n      rearBase.applyAxisAngle(YAXIS, (Math.PI * POINTER_HEMISPHERE_ANGLE) / 180 / (POINTER_RINGS * -2))\n    }\n\n    // front and rear face center vertices\n    const frontCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS)\n    const rearCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS) + 1\n    const frontCenter = new THREE.Vector3(0, 0, -1 * (POINTER_LENGTH - rearRadius))\n    vertices[frontCenterIndex * 3] = frontCenter.x\n    vertices[frontCenterIndex * 3 + 1] = frontCenter.y\n    vertices[frontCenterIndex * 3 + 2] = frontCenter.z\n    const rearCenter = new THREE.Vector3(0, 0, rearRadius)\n    vertices[rearCenterIndex * 3] = rearCenter.x\n    vertices[rearCenterIndex * 3 + 1] = rearCenter.y\n    vertices[rearCenterIndex * 3 + 2] = rearCenter.z\n\n    this.pointerGeometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3))\n    // verticesNeedUpdate = true;\n  }\n\n  createPointer() {\n    var i, j\n    const vertices = new Array(((POINTER_RINGS + 1) * POINTER_SEGMENTS + 2) * 3).fill(0)\n    // const vertices = [];\n    const indices = []\n    this.pointerGeometry = new THREE.BufferGeometry()\n\n    this.pointerGeometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3))\n\n    this._updatePointerVertices(POINTER_REAR_RADIUS)\n\n    // construct faces to connect rings\n    for (i = 0; i < POINTER_RINGS; i++) {\n      for (j = 0; j < POINTER_SEGMENTS - 1; j++) {\n        indices.push(i * POINTER_SEGMENTS + j, i * POINTER_SEGMENTS + j + 1, (i + 1) * POINTER_SEGMENTS + j)\n        indices.push(i * POINTER_SEGMENTS + j + 1, (i + 1) * POINTER_SEGMENTS + j + 1, (i + 1) * POINTER_SEGMENTS + j)\n      }\n\n      indices.push((i + 1) * POINTER_SEGMENTS - 1, i * POINTER_SEGMENTS, (i + 2) * POINTER_SEGMENTS - 1)\n      indices.push(i * POINTER_SEGMENTS, (i + 1) * POINTER_SEGMENTS, (i + 2) * POINTER_SEGMENTS - 1)\n    }\n\n    // construct front and rear face\n    const frontCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS)\n    const rearCenterIndex = POINTER_SEGMENTS * (1 + POINTER_RINGS) + 1\n\n    for (i = 0; i < POINTER_SEGMENTS - 1; i++) {\n      indices.push(frontCenterIndex, i + 1, i)\n      indices.push(rearCenterIndex, i + POINTER_SEGMENTS * POINTER_RINGS, i + POINTER_SEGMENTS * POINTER_RINGS + 1)\n    }\n\n    indices.push(frontCenterIndex, 0, POINTER_SEGMENTS - 1)\n    indices.push(rearCenterIndex, POINTER_SEGMENTS * (POINTER_RINGS + 1) - 1, POINTER_SEGMENTS * POINTER_RINGS)\n\n    const material = new THREE.MeshBasicMaterial()\n    material.transparent = true\n    material.opacity = POINTER_OPACITY_MIN\n\n    this.pointerGeometry.setIndex(indices)\n\n    this.pointerMesh = new THREE.Mesh(this.pointerGeometry, material)\n\n    this.pointerMesh.position.set(0, 0, -1 * POINTER_REAR_RADIUS)\n    this.pointerObject = new THREE.Object3D()\n    this.pointerObject.add(this.pointerMesh)\n\n    this.raycaster = new THREE.Raycaster()\n\n    // create cursor\n    const cursorGeometry = new THREE.SphereGeometry(CURSOR_RADIUS, 10, 10)\n    const cursorMaterial = new THREE.MeshBasicMaterial()\n    cursorMaterial.transparent = true\n    cursorMaterial.opacity = POINTER_OPACITY_MIN\n\n    this.cursorObject = new THREE.Mesh(cursorGeometry, cursorMaterial)\n    this.pointerObject.add(this.cursorObject)\n\n    this.add(this.pointerObject)\n  }\n\n  _updateRaycaster() {\n    if (this.raycaster) {\n      const pointerMatrix = this.pointerObject.matrixWorld\n      const tempMatrix = new THREE.Matrix4()\n      tempMatrix.identity().extractRotation(pointerMatrix)\n      this.raycaster.ray.origin.setFromMatrixPosition(pointerMatrix)\n      this.raycaster.ray.direction.set(0, 0, -1).applyMatrix4(tempMatrix)\n    }\n  }\n\n  _updatePointer() {\n    this.pointerObject.visible = this.controller.visible\n    const indexTip = this.hand.joints['index-finger-tip']\n    const thumbTip = this.hand.joints['thumb-tip']\n    const distance = indexTip.position.distanceTo(thumbTip.position)\n    const position = indexTip.position.clone().add(thumbTip.position).multiplyScalar(0.5)\n    this.pointerObject.position.copy(position)\n    this.pointerObject.quaternion.copy(this.controller.quaternion)\n\n    this.pinched = distance <= PINCH_THRESHOLD\n\n    const pinchScale = (distance - PINCH_MIN) / (PINCH_MAX - PINCH_MIN)\n    const focusScale = (distance - PINCH_MIN) / (PINCH_THRESHOLD - PINCH_MIN)\n    if (pinchScale > 1) {\n      this._updatePointerVertices(POINTER_REAR_RADIUS)\n      this.pointerMesh.position.set(0, 0, -1 * POINTER_REAR_RADIUS)\n      this.pointerMesh.material.opacity = POINTER_OPACITY_MIN\n    } else if (pinchScale > 0) {\n      const rearRadius = (POINTER_REAR_RADIUS - POINTER_REAR_RADIUS_MIN) * pinchScale + POINTER_REAR_RADIUS_MIN\n      this._updatePointerVertices(rearRadius)\n      if (focusScale < 1) {\n        this.pointerMesh.position.set(0, 0, -1 * rearRadius - (1 - focusScale) * POINTER_ADVANCE_MAX)\n        this.pointerMesh.material.opacity =\n          POINTER_OPACITY_MIN + (1 - focusScale) * (POINTER_OPACITY_MAX - POINTER_OPACITY_MIN)\n      } else {\n        this.pointerMesh.position.set(0, 0, -1 * rearRadius)\n        this.pointerMesh.material.opacity = POINTER_OPACITY_MIN\n      }\n    } else {\n      this._updatePointerVertices(POINTER_REAR_RADIUS_MIN)\n      this.pointerMesh.position.set(0, 0, -1 * POINTER_REAR_RADIUS_MIN - POINTER_ADVANCE_MAX)\n      this.pointerMesh.material.opacity = POINTER_OPACITY_MAX\n    }\n\n    this.cursorObject.material.opacity = this.pointerMesh.material.opacity\n  }\n\n  updateMatrixWorld(force) {\n    super.updateMatrixWorld(force)\n    if (this.pointerGeometry) {\n      this._updatePointer()\n      this._updateRaycaster()\n    }\n  }\n\n  isPinched() {\n    return this.pinched\n  }\n\n  setAttached(attached) {\n    this.attached = attached\n  }\n\n  isAttached() {\n    return this.attached\n  }\n\n  intersectObject(object, recursive = true) {\n    if (this.raycaster) {\n      return this.raycaster.intersectObject(object, recursive)\n    }\n  }\n\n  intersectObjects(objects, recursive = true) {\n    if (this.raycaster) {\n      return this.raycaster.intersectObjects(objects, recursive)\n    }\n  }\n\n  checkIntersections(objects, recursive = false) {\n    if (this.raycaster && !this.attached) {\n      const intersections = this.raycaster.intersectObjects(objects, recursive)\n      const direction = new THREE.Vector3(0, 0, -1)\n      if (intersections.length > 0) {\n        const intersection = intersections[0]\n        const distance = intersection.distance\n        this.cursorObject.position.copy(direction.multiplyScalar(distance))\n      } else {\n        this.cursorObject.position.copy(direction.multiplyScalar(CURSOR_MAX_DISTANCE))\n      }\n    }\n  }\n\n  setCursor(distance) {\n    const direction = new THREE.Vector3(0, 0, -1)\n    if (this.raycaster && !this.attached) {\n      this.cursorObject.position.copy(direction.multiplyScalar(distance))\n    }\n  }\n\n  dispose() {\n    this._onDisconnected()\n    this.hand.removeEventListener('connected', this._onConnected)\n    this.hand.removeEventListener('disconnected', this._onDisconnected)\n  }\n}\n\nexport { OculusHandPointerModel }\n"], "names": [], "mappings": ";AAEA,MAAM,YAAY;AAClB,MAAM,kBAAkB;AACxB,MAAM,YAAY;AAClB,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,uBAAuB;AAC7B,MAAM,sBAAsB;AAC5B,MAAM,0BAA0B;AAChC,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AACzB,MAAM,gBAAgB;AACtB,MAAM,2BAA2B;AACjC,MAAM,QAAwB,oBAAI,MAAM,QAAQ,GAAG,GAAG,CAAC;AACvD,MAAM,QAAwB,oBAAI,MAAM,QAAQ,GAAG,GAAG,CAAC;AAEvD,MAAM,gBAAgB;AACtB,MAAM,sBAAsB;AAE5B,MAAM,+BAA+B,MAAM,SAAS;AAAA,EAClD,YAAY,MAAM,YAAY;AAC5B,UAAO;AAEP,SAAK,OAAO;AACZ,SAAK,aAAa;AAGlB,SAAK,mBAAmB;AACxB,SAAK,SAAS;AACd,SAAK,OAAO;AAEZ,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AAErB,SAAK,UAAU;AACf,SAAK,WAAW;AAEhB,SAAK,eAAe;AAEpB,SAAK,YAAY;AAEjB,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,KAAK,iBAAiB,aAAa,KAAK,YAAY;AACzD,SAAK,KAAK,iBAAiB,gBAAgB,KAAK,eAAe;AAAA,EAChE;AAAA,EAED,aAAa,OAAO;AAClB,UAAM,gBAAgB,MAAM;AAC5B,QAAI,cAAc,MAAM;AACtB,WAAK,UAAU;AACf,WAAK,gBAAgB;AAErB,WAAK,cAAe;AAAA,IACrB;AAAA,EACF;AAAA,EAED,kBAAkB;;AAChB,SAAK,UAAU;AACf,SAAK,gBAAgB;AAErB,eAAK,oBAAL,mBAAsB;AACtB,eAAK,gBAAL,mBAAkB,SAAS;AAE3B,SAAK,MAAO;AAAA,EACb;AAAA,EAED,kBAAkB,UAAU,YAAY,WAAW;AACjD,UAAM,gBAAgB,WAAW,MAAO;AACxC,aAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,oBAAc,eAAe,OAAQ,KAAK,KAAK,IAAK,gBAAgB;AACpE,YAAM,MAAM,YAAY,mBAAmB;AAC3C,eAAS,IAAI,GAAG,IAAI,cAAc;AAClC,eAAS,IAAI,MAAM,CAAC,IAAI,cAAc;AACtC,eAAS,IAAI,MAAM,CAAC,IAAI,cAAc;AAAA,IACvC;AAAA,EACF;AAAA,EAED,uBAAuB,YAAY;AACjC,UAAM,WAAW,KAAK,gBAAgB,WAAW,SAAS;AAE1D,UAAM,gBAAgB,IAAI,MAAM,QAAQ,sBAAsB,GAAG,MAAM,iBAAiB,WAAW;AACnG,SAAK,kBAAkB,UAAU,eAAe,CAAC;AAGjD,UAAM,WAAW,IAAI,MAAM;AAAA,MACzB,KAAK,IAAK,KAAK,KAAK,2BAA4B,GAAG,IAAI;AAAA,MACvD,KAAK,IAAK,KAAK,KAAK,2BAA4B,GAAG,IAAI;AAAA,MACvD;AAAA,IACD;AACD,aAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,WAAK,kBAAkB,UAAU,UAAU,IAAI,CAAC;AAChD,eAAS,eAAe,OAAQ,KAAK,KAAK,2BAA4B,OAAO,gBAAgB,GAAG;AAAA,IACjG;AAGD,UAAM,mBAAmB,oBAAoB,IAAI;AACjD,UAAM,kBAAkB,oBAAoB,IAAI,iBAAiB;AACjE,UAAM,cAAc,IAAI,MAAM,QAAQ,GAAG,GAAG,MAAM,iBAAiB,WAAW;AAC9E,aAAS,mBAAmB,CAAC,IAAI,YAAY;AAC7C,aAAS,mBAAmB,IAAI,CAAC,IAAI,YAAY;AACjD,aAAS,mBAAmB,IAAI,CAAC,IAAI,YAAY;AACjD,UAAM,aAAa,IAAI,MAAM,QAAQ,GAAG,GAAG,UAAU;AACrD,aAAS,kBAAkB,CAAC,IAAI,WAAW;AAC3C,aAAS,kBAAkB,IAAI,CAAC,IAAI,WAAW;AAC/C,aAAS,kBAAkB,IAAI,CAAC,IAAI,WAAW;AAE/C,SAAK,gBAAgB,aAAa,YAAY,IAAI,MAAM,uBAAuB,UAAU,CAAC,CAAC;AAAA,EAE5F;AAAA,EAED,gBAAgB;AACd,QAAI,GAAG;AACP,UAAM,WAAW,IAAI,QAAQ,gBAAgB,KAAK,mBAAmB,KAAK,CAAC,EAAE,KAAK,CAAC;AAEnF,UAAM,UAAU,CAAE;AAClB,SAAK,kBAAkB,IAAI,MAAM,eAAgB;AAEjD,SAAK,gBAAgB,aAAa,YAAY,IAAI,MAAM,uBAAuB,UAAU,CAAC,CAAC;AAE3F,SAAK,uBAAuB,mBAAmB;AAG/C,SAAK,IAAI,GAAG,IAAI,eAAe,KAAK;AAClC,WAAK,IAAI,GAAG,IAAI,mBAAmB,GAAG,KAAK;AACzC,gBAAQ,KAAK,IAAI,mBAAmB,GAAG,IAAI,mBAAmB,IAAI,IAAI,IAAI,KAAK,mBAAmB,CAAC;AACnG,gBAAQ,KAAK,IAAI,mBAAmB,IAAI,IAAI,IAAI,KAAK,mBAAmB,IAAI,IAAI,IAAI,KAAK,mBAAmB,CAAC;AAAA,MAC9G;AAED,cAAQ,MAAM,IAAI,KAAK,mBAAmB,GAAG,IAAI,mBAAmB,IAAI,KAAK,mBAAmB,CAAC;AACjG,cAAQ,KAAK,IAAI,mBAAmB,IAAI,KAAK,mBAAmB,IAAI,KAAK,mBAAmB,CAAC;AAAA,IAC9F;AAGD,UAAM,mBAAmB,oBAAoB,IAAI;AACjD,UAAM,kBAAkB,oBAAoB,IAAI,iBAAiB;AAEjE,SAAK,IAAI,GAAG,IAAI,mBAAmB,GAAG,KAAK;AACzC,cAAQ,KAAK,kBAAkB,IAAI,GAAG,CAAC;AACvC,cAAQ,KAAK,iBAAiB,IAAI,mBAAmB,eAAe,IAAI,mBAAmB,gBAAgB,CAAC;AAAA,IAC7G;AAED,YAAQ,KAAK,kBAAkB,GAAG,mBAAmB,CAAC;AACtD,YAAQ,KAAK,iBAAiB,oBAAoB,gBAAgB,KAAK,GAAG,mBAAmB,aAAa;AAE1G,UAAM,WAAW,IAAI,MAAM,kBAAmB;AAC9C,aAAS,cAAc;AACvB,aAAS,UAAU;AAEnB,SAAK,gBAAgB,SAAS,OAAO;AAErC,SAAK,cAAc,IAAI,MAAM,KAAK,KAAK,iBAAiB,QAAQ;AAEhE,SAAK,YAAY,SAAS,IAAI,GAAG,GAAG,KAAK,mBAAmB;AAC5D,SAAK,gBAAgB,IAAI,MAAM,SAAU;AACzC,SAAK,cAAc,IAAI,KAAK,WAAW;AAEvC,SAAK,YAAY,IAAI,MAAM,UAAW;AAGtC,UAAM,iBAAiB,IAAI,MAAM,eAAe,eAAe,IAAI,EAAE;AACrE,UAAM,iBAAiB,IAAI,MAAM,kBAAmB;AACpD,mBAAe,cAAc;AAC7B,mBAAe,UAAU;AAEzB,SAAK,eAAe,IAAI,MAAM,KAAK,gBAAgB,cAAc;AACjE,SAAK,cAAc,IAAI,KAAK,YAAY;AAExC,SAAK,IAAI,KAAK,aAAa;AAAA,EAC5B;AAAA,EAED,mBAAmB;AACjB,QAAI,KAAK,WAAW;AAClB,YAAM,gBAAgB,KAAK,cAAc;AACzC,YAAM,aAAa,IAAI,MAAM,QAAS;AACtC,iBAAW,SAAQ,EAAG,gBAAgB,aAAa;AACnD,WAAK,UAAU,IAAI,OAAO,sBAAsB,aAAa;AAC7D,WAAK,UAAU,IAAI,UAAU,IAAI,GAAG,GAAG,EAAE,EAAE,aAAa,UAAU;AAAA,IACnE;AAAA,EACF;AAAA,EAED,iBAAiB;AACf,SAAK,cAAc,UAAU,KAAK,WAAW;AAC7C,UAAM,WAAW,KAAK,KAAK,OAAO,kBAAkB;AACpD,UAAM,WAAW,KAAK,KAAK,OAAO,WAAW;AAC7C,UAAM,WAAW,SAAS,SAAS,WAAW,SAAS,QAAQ;AAC/D,UAAM,WAAW,SAAS,SAAS,MAAO,EAAC,IAAI,SAAS,QAAQ,EAAE,eAAe,GAAG;AACpF,SAAK,cAAc,SAAS,KAAK,QAAQ;AACzC,SAAK,cAAc,WAAW,KAAK,KAAK,WAAW,UAAU;AAE7D,SAAK,UAAU,YAAY;AAE3B,UAAM,cAAc,WAAW,cAAc,YAAY;AACzD,UAAM,cAAc,WAAW,cAAc,kBAAkB;AAC/D,QAAI,aAAa,GAAG;AAClB,WAAK,uBAAuB,mBAAmB;AAC/C,WAAK,YAAY,SAAS,IAAI,GAAG,GAAG,KAAK,mBAAmB;AAC5D,WAAK,YAAY,SAAS,UAAU;AAAA,IAC1C,WAAe,aAAa,GAAG;AACzB,YAAM,cAAc,sBAAsB,2BAA2B,aAAa;AAClF,WAAK,uBAAuB,UAAU;AACtC,UAAI,aAAa,GAAG;AAClB,aAAK,YAAY,SAAS,IAAI,GAAG,GAAG,KAAK,cAAc,IAAI,cAAc,mBAAmB;AAC5F,aAAK,YAAY,SAAS,UACxB,uBAAuB,IAAI,eAAe,sBAAsB;AAAA,MAC1E,OAAa;AACL,aAAK,YAAY,SAAS,IAAI,GAAG,GAAG,KAAK,UAAU;AACnD,aAAK,YAAY,SAAS,UAAU;AAAA,MACrC;AAAA,IACP,OAAW;AACL,WAAK,uBAAuB,uBAAuB;AACnD,WAAK,YAAY,SAAS,IAAI,GAAG,GAAG,KAAK,0BAA0B,mBAAmB;AACtF,WAAK,YAAY,SAAS,UAAU;AAAA,IACrC;AAED,SAAK,aAAa,SAAS,UAAU,KAAK,YAAY,SAAS;AAAA,EAChE;AAAA,EAED,kBAAkB,OAAO;AACvB,UAAM,kBAAkB,KAAK;AAC7B,QAAI,KAAK,iBAAiB;AACxB,WAAK,eAAgB;AACrB,WAAK,iBAAkB;AAAA,IACxB;AAAA,EACF;AAAA,EAED,YAAY;AACV,WAAO,KAAK;AAAA,EACb;AAAA,EAED,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EACjB;AAAA,EAED,aAAa;AACX,WAAO,KAAK;AAAA,EACb;AAAA,EAED,gBAAgB,QAAQ,YAAY,MAAM;AACxC,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,UAAU,gBAAgB,QAAQ,SAAS;AAAA,IACxD;AAAA,EACF;AAAA,EAED,iBAAiB,SAAS,YAAY,MAAM;AAC1C,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,UAAU,iBAAiB,SAAS,SAAS;AAAA,IAC1D;AAAA,EACF;AAAA,EAED,mBAAmB,SAAS,YAAY,OAAO;AAC7C,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU;AACpC,YAAM,gBAAgB,KAAK,UAAU,iBAAiB,SAAS,SAAS;AACxE,YAAM,YAAY,IAAI,MAAM,QAAQ,GAAG,GAAG,EAAE;AAC5C,UAAI,cAAc,SAAS,GAAG;AAC5B,cAAM,eAAe,cAAc,CAAC;AACpC,cAAM,WAAW,aAAa;AAC9B,aAAK,aAAa,SAAS,KAAK,UAAU,eAAe,QAAQ,CAAC;AAAA,MAC1E,OAAa;AACL,aAAK,aAAa,SAAS,KAAK,UAAU,eAAe,mBAAmB,CAAC;AAAA,MAC9E;AAAA,IACF;AAAA,EACF;AAAA,EAED,UAAU,UAAU;AAClB,UAAM,YAAY,IAAI,MAAM,QAAQ,GAAG,GAAG,EAAE;AAC5C,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU;AACpC,WAAK,aAAa,SAAS,KAAK,UAAU,eAAe,QAAQ,CAAC;AAAA,IACnE;AAAA,EACF;AAAA,EAED,UAAU;AACR,SAAK,gBAAiB;AACtB,SAAK,KAAK,oBAAoB,aAAa,KAAK,YAAY;AAC5D,SAAK,KAAK,oBAAoB,gBAAgB,KAAK,eAAe;AAAA,EACnE;AACH;"}