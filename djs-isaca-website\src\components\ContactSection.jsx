import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

function ContactSection() {
  const sectionRef = useRef();
  const cardsRef = useRef();

  useEffect(() => {
    // Animate contact cards on scroll
    const cards = cardsRef.current.children;
    gsap.fromTo(cards,
      { opacity: 0, y: 30, scale: 0.9 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.6,
        stagger: 0.1,
        ease: "back.out(1.7)",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );
  }, []);

  const contactInfo = [
    {
      icon: "📧",
      title: "Email",
      content: "<EMAIL>",
      link: "mailto:<EMAIL>"
    },
    {
      icon: "🌐",
      title: "Website",
      content: "www.djsce.ac.in",
      link: "https://www.djsce.ac.in"
    },
    {
      icon: "📱",
      title: "Social Media",
      content: "Follow us on LinkedIn",
      link: "#"
    },
    {
      icon: "📍",
      title: "Location",
      content: "DJSCE, Vile Parle (W), Mumbai",
      link: "#"
    }
  ];

  return (
    <section className="contact-section" ref={sectionRef}>
      <div className="contact-container">
        <h2>Get In Touch</h2>
        <p>Ready to join the cybersecurity revolution? Connect with us!</p>
        
        <div className="contact-grid" ref={cardsRef}>
          {contactInfo.map((item, index) => (
            <div key={index} className="contact-card">
              <div className="contact-icon">{item.icon}</div>
              <h3>{item.title}</h3>
              <p>{item.content}</p>
              {item.link !== "#" && (
                <a 
                  href={item.link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="contact-link"
                >
                  Connect
                </a>
              )}
            </div>
          ))}
        </div>

        <div className="cta-section">
          <h3>Join Our Community</h3>
          <p>Be part of the next generation of cybersecurity professionals</p>
          <button className="cta-button">
            <span>Join DJS ISACA</span>
            <div className="button-glow"></div>
          </button>
        </div>
      </div>
    </section>
  );
}

export default ContactSection;
