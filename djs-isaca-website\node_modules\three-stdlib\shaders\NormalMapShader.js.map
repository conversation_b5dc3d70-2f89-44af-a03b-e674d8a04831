{"version": 3, "file": "NormalMapShader.js", "sources": ["../../src/shaders/NormalMapShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * Normal map shader\n * - compute normals from heightmap\n */\n\nexport const NormalMapShader = {\n  uniforms: {\n    heightMap: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2(512, 512) },\n    scale: { value: /* @__PURE__ */ new Vector2(1, 1) },\n    height: { value: 0.05 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float height;\n    uniform vec2 resolution;\n    uniform sampler2D heightMap;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tfloat val = texture2D( heightMap, vUv ).x;\n\n    \tfloat valU = texture2D( heightMap, vUv + vec2( 1.0 / resolution.x, 0.0 ) ).x;\n    \tfloat valV = texture2D( heightMap, vUv + vec2( 0.0, 1.0 / resolution.y ) ).x;\n\n    \tgl_FragColor = vec4( ( 0.5 * normalize( vec3( val - valU, val - valV, height  ) ) + 0.5 ), 1.0 );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";AAOO,MAAM,kBAAkB;AAAA,EAC7B,UAAU;AAAA,IACR,WAAW,EAAE,OAAO,KAAK;AAAA,IACzB,YAAY,EAAE,2BAA2B,QAAQ,KAAK,GAAG,EAAE;AAAA,IAC3D,OAAO,EAAE,2BAA2B,QAAQ,GAAG,CAAC,EAAE;AAAA,IAClD,QAAQ,EAAE,OAAO,KAAK;AAAA,EACxB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkB7B;"}