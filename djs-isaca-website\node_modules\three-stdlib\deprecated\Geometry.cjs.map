{"version": 3, "file": "Geometry.cjs", "sources": ["../../src/deprecated/Geometry.js"], "sourcesContent": ["import {\n  Box3,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>ribute,\n  BufferGeometry,\n  Color,\n  EventDispatcher,\n  Float32BufferAttribute,\n  Matrix3,\n  Matrix4,\n  MathUtils,\n  Object3D,\n  Sphere,\n  Vector2,\n  Vector3,\n} from 'three'\n\nconst _m1 = /* @__PURE__ */ new Matrix4()\nconst _obj = /* @__PURE__ */ new Object3D()\nconst _offset = /* @__PURE__ */ new Vector3()\n\nconst Geometry = /* @__PURE__ */ (() => {\n  class Geometry extends EventDispatcher {\n    static createBufferGeometryFromObject(object) {\n      let buffergeometry = new BufferGeometry()\n\n      const geometry = object.geometry\n\n      if (object.isPoints || object.isLine) {\n        const positions = new Float32BufferAttribute(geometry.vertices.length * 3, 3)\n        const colors = new Float32BufferAttribute(geometry.colors.length * 3, 3)\n\n        buffergeometry.setAttribute('position', positions.copyVector3sArray(geometry.vertices))\n        buffergeometry.setAttribute('color', colors.copyColorsArray(geometry.colors))\n\n        if (geometry.lineDistances && geometry.lineDistances.length === geometry.vertices.length) {\n          const lineDistances = new Float32BufferAttribute(geometry.lineDistances.length, 1)\n\n          buffergeometry.setAttribute('lineDistance', lineDistances.copyArray(geometry.lineDistances))\n        }\n\n        if (geometry.boundingSphere !== null) {\n          buffergeometry.boundingSphere = geometry.boundingSphere.clone()\n        }\n\n        if (geometry.boundingBox !== null) {\n          buffergeometry.boundingBox = geometry.boundingBox.clone()\n        }\n      } else if (object.isMesh) {\n        buffergeometry = geometry.toBufferGeometry()\n      }\n\n      return buffergeometry\n    }\n\n    constructor() {\n      super()\n      this.isGeometry = true\n      this.uuid = MathUtils.generateUUID()\n\n      this.name = ''\n      this.type = 'Geometry'\n\n      this.vertices = []\n      this.colors = []\n      this.faces = []\n      this.faceVertexUvs = [[]]\n\n      this.morphTargets = []\n      this.morphNormals = []\n\n      this.skinWeights = []\n      this.skinIndices = []\n\n      this.lineDistances = []\n\n      this.boundingBox = null\n      this.boundingSphere = null\n\n      // update flags\n\n      this.elementsNeedUpdate = false\n      this.verticesNeedUpdate = false\n      this.uvsNeedUpdate = false\n      this.normalsNeedUpdate = false\n      this.colorsNeedUpdate = false\n      this.lineDistancesNeedUpdate = false\n      this.groupsNeedUpdate = false\n    }\n\n    applyMatrix4(matrix) {\n      const normalMatrix = new Matrix3().getNormalMatrix(matrix)\n\n      for (let i = 0, il = this.vertices.length; i < il; i++) {\n        const vertex = this.vertices[i]\n        vertex.applyMatrix4(matrix)\n      }\n\n      for (let i = 0, il = this.faces.length; i < il; i++) {\n        const face = this.faces[i]\n        face.normal.applyMatrix3(normalMatrix).normalize()\n\n        for (let j = 0, jl = face.vertexNormals.length; j < jl; j++) {\n          face.vertexNormals[j].applyMatrix3(normalMatrix).normalize()\n        }\n      }\n\n      if (this.boundingBox !== null) {\n        this.computeBoundingBox()\n      }\n\n      if (this.boundingSphere !== null) {\n        this.computeBoundingSphere()\n      }\n\n      this.verticesNeedUpdate = true\n      this.normalsNeedUpdate = true\n\n      return this\n    }\n\n    rotateX(angle) {\n      // rotate geometry around world x-axis\n\n      _m1.makeRotationX(angle)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    rotateY(angle) {\n      // rotate geometry around world y-axis\n\n      _m1.makeRotationY(angle)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    rotateZ(angle) {\n      // rotate geometry around world z-axis\n\n      _m1.makeRotationZ(angle)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    translate(x, y, z) {\n      // translate geometry\n\n      _m1.makeTranslation(x, y, z)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    scale(x, y, z) {\n      // scale geometry\n\n      _m1.makeScale(x, y, z)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    lookAt(vector) {\n      _obj.lookAt(vector)\n\n      _obj.updateMatrix()\n\n      this.applyMatrix4(_obj.matrix)\n\n      return this\n    }\n\n    fromBufferGeometry(geometry) {\n      const scope = this\n\n      const index = geometry.index !== null ? geometry.index : undefined\n      const attributes = geometry.attributes\n\n      if (attributes.position === undefined) {\n        console.error('THREE.Geometry.fromBufferGeometry(): Position attribute required for conversion.')\n        return this\n      }\n\n      const position = attributes.position\n      const normal = attributes.normal\n      const color = attributes.color\n      const uv = attributes.uv\n      const uv2 = attributes.uv2\n\n      if (uv2 !== undefined) this.faceVertexUvs[1] = []\n\n      for (let i = 0; i < position.count; i++) {\n        scope.vertices.push(new Vector3().fromBufferAttribute(position, i))\n\n        if (color !== undefined) {\n          scope.colors.push(new Color().fromBufferAttribute(color, i))\n        }\n      }\n\n      function addFace(a, b, c, materialIndex) {\n        const vertexColors =\n          color === undefined ? [] : [scope.colors[a].clone(), scope.colors[b].clone(), scope.colors[c].clone()]\n\n        const vertexNormals =\n          normal === undefined\n            ? []\n            : [\n                new Vector3().fromBufferAttribute(normal, a),\n                new Vector3().fromBufferAttribute(normal, b),\n                new Vector3().fromBufferAttribute(normal, c),\n              ]\n\n        const face = new Face3(a, b, c, vertexNormals, vertexColors, materialIndex)\n\n        scope.faces.push(face)\n\n        if (uv !== undefined) {\n          scope.faceVertexUvs[0].push([\n            new Vector2().fromBufferAttribute(uv, a),\n            new Vector2().fromBufferAttribute(uv, b),\n            new Vector2().fromBufferAttribute(uv, c),\n          ])\n        }\n\n        if (uv2 !== undefined) {\n          scope.faceVertexUvs[1].push([\n            new Vector2().fromBufferAttribute(uv2, a),\n            new Vector2().fromBufferAttribute(uv2, b),\n            new Vector2().fromBufferAttribute(uv2, c),\n          ])\n        }\n      }\n\n      const groups = geometry.groups\n\n      if (groups.length > 0) {\n        for (let i = 0; i < groups.length; i++) {\n          const group = groups[i]\n\n          const start = group.start\n          const count = group.count\n\n          for (let j = start, jl = start + count; j < jl; j += 3) {\n            if (index !== undefined) {\n              addFace(index.getX(j), index.getX(j + 1), index.getX(j + 2), group.materialIndex)\n            } else {\n              addFace(j, j + 1, j + 2, group.materialIndex)\n            }\n          }\n        }\n      } else {\n        if (index !== undefined) {\n          for (let i = 0; i < index.count; i += 3) {\n            addFace(index.getX(i), index.getX(i + 1), index.getX(i + 2))\n          }\n        } else {\n          for (let i = 0; i < position.count; i += 3) {\n            addFace(i, i + 1, i + 2)\n          }\n        }\n      }\n\n      this.computeFaceNormals()\n\n      if (geometry.boundingBox !== null) {\n        this.boundingBox = geometry.boundingBox.clone()\n      }\n\n      if (geometry.boundingSphere !== null) {\n        this.boundingSphere = geometry.boundingSphere.clone()\n      }\n\n      return this\n    }\n\n    center() {\n      this.computeBoundingBox()\n\n      this.boundingBox.getCenter(_offset).negate()\n\n      this.translate(_offset.x, _offset.y, _offset.z)\n\n      return this\n    }\n\n    normalize() {\n      this.computeBoundingSphere()\n\n      const center = this.boundingSphere.center\n      const radius = this.boundingSphere.radius\n\n      const s = radius === 0 ? 1 : 1.0 / radius\n\n      const matrix = new Matrix4()\n      matrix.set(s, 0, 0, -s * center.x, 0, s, 0, -s * center.y, 0, 0, s, -s * center.z, 0, 0, 0, 1)\n\n      this.applyMatrix4(matrix)\n\n      return this\n    }\n\n    computeFaceNormals() {\n      const cb = new Vector3(),\n        ab = new Vector3()\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        const vA = this.vertices[face.a]\n        const vB = this.vertices[face.b]\n        const vC = this.vertices[face.c]\n\n        cb.subVectors(vC, vB)\n        ab.subVectors(vA, vB)\n        cb.cross(ab)\n\n        cb.normalize()\n\n        face.normal.copy(cb)\n      }\n    }\n\n    computeVertexNormals(areaWeighted = true) {\n      const vertices = new Array(this.vertices.length)\n\n      for (let v = 0, vl = this.vertices.length; v < vl; v++) {\n        vertices[v] = new Vector3()\n      }\n\n      if (areaWeighted) {\n        // vertex normals weighted by triangle areas\n        // http://www.iquilezles.org/www/articles/normals/normals.htm\n\n        const cb = new Vector3(),\n          ab = new Vector3()\n\n        for (let f = 0, fl = this.faces.length; f < fl; f++) {\n          const face = this.faces[f]\n\n          const vA = this.vertices[face.a]\n          const vB = this.vertices[face.b]\n          const vC = this.vertices[face.c]\n\n          cb.subVectors(vC, vB)\n          ab.subVectors(vA, vB)\n          cb.cross(ab)\n\n          vertices[face.a].add(cb)\n          vertices[face.b].add(cb)\n          vertices[face.c].add(cb)\n        }\n      } else {\n        this.computeFaceNormals()\n\n        for (let f = 0, fl = this.faces.length; f < fl; f++) {\n          const face = this.faces[f]\n\n          vertices[face.a].add(face.normal)\n          vertices[face.b].add(face.normal)\n          vertices[face.c].add(face.normal)\n        }\n      }\n\n      for (let v = 0, vl = this.vertices.length; v < vl; v++) {\n        vertices[v].normalize()\n      }\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        const vertexNormals = face.vertexNormals\n\n        if (vertexNormals.length === 3) {\n          vertexNormals[0].copy(vertices[face.a])\n          vertexNormals[1].copy(vertices[face.b])\n          vertexNormals[2].copy(vertices[face.c])\n        } else {\n          vertexNormals[0] = vertices[face.a].clone()\n          vertexNormals[1] = vertices[face.b].clone()\n          vertexNormals[2] = vertices[face.c].clone()\n        }\n      }\n\n      if (this.faces.length > 0) {\n        this.normalsNeedUpdate = true\n      }\n    }\n\n    computeFlatVertexNormals() {\n      this.computeFaceNormals()\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        const vertexNormals = face.vertexNormals\n\n        if (vertexNormals.length === 3) {\n          vertexNormals[0].copy(face.normal)\n          vertexNormals[1].copy(face.normal)\n          vertexNormals[2].copy(face.normal)\n        } else {\n          vertexNormals[0] = face.normal.clone()\n          vertexNormals[1] = face.normal.clone()\n          vertexNormals[2] = face.normal.clone()\n        }\n      }\n\n      if (this.faces.length > 0) {\n        this.normalsNeedUpdate = true\n      }\n    }\n\n    computeMorphNormals() {\n      // save original normals\n      // - create temp variables on first access\n      //   otherwise just copy (for faster repeated calls)\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        if (!face.__originalFaceNormal) {\n          face.__originalFaceNormal = face.normal.clone()\n        } else {\n          face.__originalFaceNormal.copy(face.normal)\n        }\n\n        if (!face.__originalVertexNormals) face.__originalVertexNormals = []\n\n        for (let i = 0, il = face.vertexNormals.length; i < il; i++) {\n          if (!face.__originalVertexNormals[i]) {\n            face.__originalVertexNormals[i] = face.vertexNormals[i].clone()\n          } else {\n            face.__originalVertexNormals[i].copy(face.vertexNormals[i])\n          }\n        }\n      }\n\n      // use temp geometry to compute face and vertex normals for each morph\n\n      const tmpGeo = new Geometry()\n      tmpGeo.faces = this.faces\n\n      for (let i = 0, il = this.morphTargets.length; i < il; i++) {\n        // create on first access\n\n        if (!this.morphNormals[i]) {\n          this.morphNormals[i] = {}\n          this.morphNormals[i].faceNormals = []\n          this.morphNormals[i].vertexNormals = []\n\n          const dstNormalsFace = this.morphNormals[i].faceNormals\n          const dstNormalsVertex = this.morphNormals[i].vertexNormals\n\n          for (let f = 0, fl = this.faces.length; f < fl; f++) {\n            const faceNormal = new Vector3()\n            const vertexNormals = {\n              a: new Vector3(),\n              b: new Vector3(),\n              c: new Vector3(),\n            }\n\n            dstNormalsFace.push(faceNormal)\n            dstNormalsVertex.push(vertexNormals)\n          }\n        }\n\n        const morphNormals = this.morphNormals[i]\n\n        // set vertices to morph target\n\n        tmpGeo.vertices = this.morphTargets[i].vertices\n\n        // compute morph normals\n\n        tmpGeo.computeFaceNormals()\n        tmpGeo.computeVertexNormals()\n\n        // store morph normals\n\n        for (let f = 0, fl = this.faces.length; f < fl; f++) {\n          const face = this.faces[f]\n\n          const faceNormal = morphNormals.faceNormals[f]\n          const vertexNormals = morphNormals.vertexNormals[f]\n\n          faceNormal.copy(face.normal)\n\n          vertexNormals.a.copy(face.vertexNormals[0])\n          vertexNormals.b.copy(face.vertexNormals[1])\n          vertexNormals.c.copy(face.vertexNormals[2])\n        }\n      }\n\n      // restore original normals\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        face.normal = face.__originalFaceNormal\n        face.vertexNormals = face.__originalVertexNormals\n      }\n    }\n\n    computeBoundingBox() {\n      if (this.boundingBox === null) {\n        this.boundingBox = new Box3()\n      }\n\n      this.boundingBox.setFromPoints(this.vertices)\n    }\n\n    computeBoundingSphere() {\n      if (this.boundingSphere === null) {\n        this.boundingSphere = new Sphere()\n      }\n\n      this.boundingSphere.setFromPoints(this.vertices)\n    }\n\n    merge(geometry, matrix, materialIndexOffset = 0) {\n      if (!(geometry && geometry.isGeometry)) {\n        console.error('THREE.Geometry.merge(): geometry not an instance of THREE.Geometry.', geometry)\n        return\n      }\n\n      let normalMatrix\n      const vertexOffset = this.vertices.length,\n        vertices1 = this.vertices,\n        vertices2 = geometry.vertices,\n        faces1 = this.faces,\n        faces2 = geometry.faces,\n        colors1 = this.colors,\n        colors2 = geometry.colors\n\n      if (matrix !== undefined) {\n        normalMatrix = new Matrix3().getNormalMatrix(matrix)\n      }\n\n      // vertices\n\n      for (let i = 0, il = vertices2.length; i < il; i++) {\n        const vertex = vertices2[i]\n\n        const vertexCopy = vertex.clone()\n\n        if (matrix !== undefined) vertexCopy.applyMatrix4(matrix)\n\n        vertices1.push(vertexCopy)\n      }\n\n      // colors\n\n      for (let i = 0, il = colors2.length; i < il; i++) {\n        colors1.push(colors2[i].clone())\n      }\n\n      // faces\n\n      for (let i = 0, il = faces2.length; i < il; i++) {\n        const face = faces2[i]\n        let normal, color\n        const faceVertexNormals = face.vertexNormals,\n          faceVertexColors = face.vertexColors\n\n        const faceCopy = new Face3(face.a + vertexOffset, face.b + vertexOffset, face.c + vertexOffset)\n        faceCopy.normal.copy(face.normal)\n\n        if (normalMatrix !== undefined) {\n          faceCopy.normal.applyMatrix3(normalMatrix).normalize()\n        }\n\n        for (let j = 0, jl = faceVertexNormals.length; j < jl; j++) {\n          normal = faceVertexNormals[j].clone()\n\n          if (normalMatrix !== undefined) {\n            normal.applyMatrix3(normalMatrix).normalize()\n          }\n\n          faceCopy.vertexNormals.push(normal)\n        }\n\n        faceCopy.color.copy(face.color)\n\n        for (let j = 0, jl = faceVertexColors.length; j < jl; j++) {\n          color = faceVertexColors[j]\n          faceCopy.vertexColors.push(color.clone())\n        }\n\n        faceCopy.materialIndex = face.materialIndex + materialIndexOffset\n\n        faces1.push(faceCopy)\n      }\n\n      // uvs\n\n      for (let i = 0, il = geometry.faceVertexUvs.length; i < il; i++) {\n        const faceVertexUvs2 = geometry.faceVertexUvs[i]\n\n        if (this.faceVertexUvs[i] === undefined) this.faceVertexUvs[i] = []\n\n        for (let j = 0, jl = faceVertexUvs2.length; j < jl; j++) {\n          const uvs2 = faceVertexUvs2[j],\n            uvsCopy = []\n\n          for (let k = 0, kl = uvs2.length; k < kl; k++) {\n            uvsCopy.push(uvs2[k].clone())\n          }\n\n          this.faceVertexUvs[i].push(uvsCopy)\n        }\n      }\n    }\n\n    mergeMesh(mesh) {\n      if (!(mesh && mesh.isMesh)) {\n        console.error('THREE.Geometry.mergeMesh(): mesh not an instance of THREE.Mesh.', mesh)\n        return\n      }\n\n      if (mesh.matrixAutoUpdate) mesh.updateMatrix()\n\n      this.merge(mesh.geometry, mesh.matrix)\n    }\n\n    /*\n     * Checks for duplicate vertices with hashmap.\n     * Duplicated vertices are removed\n     * and faces' vertices are updated.\n     */\n\n    mergeVertices(precisionPoints = 4) {\n      const verticesMap = {} // Hashmap for looking up vertices by position coordinates (and making sure they are unique)\n      const unique = [],\n        changes = []\n\n      const precision = Math.pow(10, precisionPoints)\n\n      for (let i = 0, il = this.vertices.length; i < il; i++) {\n        const v = this.vertices[i]\n        const key = `${Math.round(v.x * precision)}_${Math.round(v.y * precision)}_${Math.round(v.z * precision)}`\n\n        if (verticesMap[key] === undefined) {\n          verticesMap[key] = i\n          unique.push(this.vertices[i])\n          changes[i] = unique.length - 1\n        } else {\n          //console.log('Duplicate vertex found. ', i, ' could be using ', verticesMap[key]);\n          changes[i] = changes[verticesMap[key]]\n        }\n      }\n\n      // if faces are completely degenerate after merging vertices, we\n      // have to remove them from the geometry.\n      const faceIndicesToRemove = []\n\n      for (let i = 0, il = this.faces.length; i < il; i++) {\n        const face = this.faces[i]\n\n        face.a = changes[face.a]\n        face.b = changes[face.b]\n        face.c = changes[face.c]\n\n        const indices = [face.a, face.b, face.c]\n\n        // if any duplicate vertices are found in a Face3\n        // we have to remove the face as nothing can be saved\n        for (let n = 0; n < 3; n++) {\n          if (indices[n] === indices[(n + 1) % 3]) {\n            faceIndicesToRemove.push(i)\n            break\n          }\n        }\n      }\n\n      for (let i = faceIndicesToRemove.length - 1; i >= 0; i--) {\n        const idx = faceIndicesToRemove[i]\n\n        this.faces.splice(idx, 1)\n\n        for (let j = 0, jl = this.faceVertexUvs.length; j < jl; j++) {\n          this.faceVertexUvs[j].splice(idx, 1)\n        }\n      }\n\n      // Use unique set of vertices\n\n      const diff = this.vertices.length - unique.length\n      this.vertices = unique\n      return diff\n    }\n\n    setFromPoints(points) {\n      this.vertices = []\n\n      for (let i = 0, l = points.length; i < l; i++) {\n        const point = points[i]\n        this.vertices.push(new Vector3(point.x, point.y, point.z || 0))\n      }\n\n      return this\n    }\n\n    sortFacesByMaterialIndex() {\n      const faces = this.faces\n      const length = faces.length\n\n      // tag faces\n\n      for (let i = 0; i < length; i++) {\n        faces[i]._id = i\n      }\n\n      // sort faces\n\n      function materialIndexSort(a, b) {\n        return a.materialIndex - b.materialIndex\n      }\n\n      faces.sort(materialIndexSort)\n\n      // sort uvs\n\n      const uvs1 = this.faceVertexUvs[0]\n      const uvs2 = this.faceVertexUvs[1]\n\n      let newUvs1, newUvs2\n\n      if (uvs1 && uvs1.length === length) newUvs1 = []\n      if (uvs2 && uvs2.length === length) newUvs2 = []\n\n      for (let i = 0; i < length; i++) {\n        const id = faces[i]._id\n\n        if (newUvs1) newUvs1.push(uvs1[id])\n        if (newUvs2) newUvs2.push(uvs2[id])\n      }\n\n      if (newUvs1) this.faceVertexUvs[0] = newUvs1\n      if (newUvs2) this.faceVertexUvs[1] = newUvs2\n    }\n\n    toJSON() {\n      const data = {\n        metadata: {\n          version: 4.5,\n          type: 'Geometry',\n          generator: 'Geometry.toJSON',\n        },\n      }\n\n      // standard Geometry serialization\n\n      data.uuid = this.uuid\n      data.type = this.type\n      if (this.name !== '') data.name = this.name\n\n      if (this.parameters !== undefined) {\n        const parameters = this.parameters\n\n        for (let key in parameters) {\n          if (parameters[key] !== undefined) data[key] = parameters[key]\n        }\n\n        return data\n      }\n\n      const vertices = []\n\n      for (let i = 0; i < this.vertices.length; i++) {\n        const vertex = this.vertices[i]\n        vertices.push(vertex.x, vertex.y, vertex.z)\n      }\n\n      const faces = []\n      const normals = []\n      const normalsHash = {}\n      const colors = []\n      const colorsHash = {}\n      const uvs = []\n      const uvsHash = {}\n\n      for (let i = 0; i < this.faces.length; i++) {\n        const face = this.faces[i]\n\n        const hasMaterial = true\n        const hasFaceUv = false // deprecated\n        const hasFaceVertexUv = this.faceVertexUvs[0][i] !== undefined\n        const hasFaceNormal = face.normal.length() > 0\n        const hasFaceVertexNormal = face.vertexNormals.length > 0\n        const hasFaceColor = face.color.r !== 1 || face.color.g !== 1 || face.color.b !== 1\n        const hasFaceVertexColor = face.vertexColors.length > 0\n\n        let faceType = 0\n\n        faceType = setBit(faceType, 0, 0) // isQuad\n        faceType = setBit(faceType, 1, hasMaterial)\n        faceType = setBit(faceType, 2, hasFaceUv)\n        faceType = setBit(faceType, 3, hasFaceVertexUv)\n        faceType = setBit(faceType, 4, hasFaceNormal)\n        faceType = setBit(faceType, 5, hasFaceVertexNormal)\n        faceType = setBit(faceType, 6, hasFaceColor)\n        faceType = setBit(faceType, 7, hasFaceVertexColor)\n\n        faces.push(faceType)\n        faces.push(face.a, face.b, face.c)\n        faces.push(face.materialIndex)\n\n        if (hasFaceVertexUv) {\n          const faceVertexUvs = this.faceVertexUvs[0][i]\n\n          faces.push(getUvIndex(faceVertexUvs[0]), getUvIndex(faceVertexUvs[1]), getUvIndex(faceVertexUvs[2]))\n        }\n\n        if (hasFaceNormal) {\n          faces.push(getNormalIndex(face.normal))\n        }\n\n        if (hasFaceVertexNormal) {\n          const vertexNormals = face.vertexNormals\n\n          faces.push(\n            getNormalIndex(vertexNormals[0]),\n            getNormalIndex(vertexNormals[1]),\n            getNormalIndex(vertexNormals[2]),\n          )\n        }\n\n        if (hasFaceColor) {\n          faces.push(getColorIndex(face.color))\n        }\n\n        if (hasFaceVertexColor) {\n          const vertexColors = face.vertexColors\n\n          faces.push(getColorIndex(vertexColors[0]), getColorIndex(vertexColors[1]), getColorIndex(vertexColors[2]))\n        }\n      }\n\n      function setBit(value, position, enabled) {\n        return enabled ? value | (1 << position) : value & ~(1 << position)\n      }\n\n      function getNormalIndex(normal) {\n        const hash = normal.x.toString() + normal.y.toString() + normal.z.toString()\n\n        if (normalsHash[hash] !== undefined) {\n          return normalsHash[hash]\n        }\n\n        normalsHash[hash] = normals.length / 3\n        normals.push(normal.x, normal.y, normal.z)\n\n        return normalsHash[hash]\n      }\n\n      function getColorIndex(color) {\n        const hash = color.r.toString() + color.g.toString() + color.b.toString()\n\n        if (colorsHash[hash] !== undefined) {\n          return colorsHash[hash]\n        }\n\n        colorsHash[hash] = colors.length\n        colors.push(color.getHex())\n\n        return colorsHash[hash]\n      }\n\n      function getUvIndex(uv) {\n        const hash = uv.x.toString() + uv.y.toString()\n\n        if (uvsHash[hash] !== undefined) {\n          return uvsHash[hash]\n        }\n\n        uvsHash[hash] = uvs.length / 2\n        uvs.push(uv.x, uv.y)\n\n        return uvsHash[hash]\n      }\n\n      data.data = {}\n\n      data.data.vertices = vertices\n      data.data.normals = normals\n      if (colors.length > 0) data.data.colors = colors\n      if (uvs.length > 0) data.data.uvs = [uvs] // temporal backward compatibility\n      data.data.faces = faces\n\n      return data\n    }\n\n    clone() {\n      /*\n\t\t // Handle primitives\n\n\t\t const parameters = this.parameters;\n\n\t\t if ( parameters !== undefined ) {\n\n\t\t const values = [];\n\n\t\t for ( const key in parameters ) {\n\n\t\t values.push( parameters[ key ] );\n\n\t\t }\n\n\t\t const geometry = Object.create( this.constructor.prototype );\n\t\t this.constructor.apply( geometry, values );\n\t\t return geometry;\n\n\t\t }\n\n\t\t return new this.constructor().copy( this );\n\t\t */\n\n      return new Geometry().copy(this)\n    }\n\n    copy(source) {\n      // reset\n\n      this.vertices = []\n      this.colors = []\n      this.faces = []\n      this.faceVertexUvs = [[]]\n      this.morphTargets = []\n      this.morphNormals = []\n      this.skinWeights = []\n      this.skinIndices = []\n      this.lineDistances = []\n      this.boundingBox = null\n      this.boundingSphere = null\n\n      // name\n\n      this.name = source.name\n\n      // vertices\n\n      const vertices = source.vertices\n\n      for (let i = 0, il = vertices.length; i < il; i++) {\n        this.vertices.push(vertices[i].clone())\n      }\n\n      // colors\n\n      const colors = source.colors\n\n      for (let i = 0, il = colors.length; i < il; i++) {\n        this.colors.push(colors[i].clone())\n      }\n\n      // faces\n\n      const faces = source.faces\n\n      for (let i = 0, il = faces.length; i < il; i++) {\n        this.faces.push(faces[i].clone())\n      }\n\n      // face vertex uvs\n\n      for (let i = 0, il = source.faceVertexUvs.length; i < il; i++) {\n        const faceVertexUvs = source.faceVertexUvs[i]\n\n        if (this.faceVertexUvs[i] === undefined) {\n          this.faceVertexUvs[i] = []\n        }\n\n        for (let j = 0, jl = faceVertexUvs.length; j < jl; j++) {\n          const uvs = faceVertexUvs[j],\n            uvsCopy = []\n\n          for (let k = 0, kl = uvs.length; k < kl; k++) {\n            const uv = uvs[k]\n\n            uvsCopy.push(uv.clone())\n          }\n\n          this.faceVertexUvs[i].push(uvsCopy)\n        }\n      }\n\n      // morph targets\n\n      const morphTargets = source.morphTargets\n\n      for (let i = 0, il = morphTargets.length; i < il; i++) {\n        const morphTarget = {}\n        morphTarget.name = morphTargets[i].name\n\n        // vertices\n\n        if (morphTargets[i].vertices !== undefined) {\n          morphTarget.vertices = []\n\n          for (let j = 0, jl = morphTargets[i].vertices.length; j < jl; j++) {\n            morphTarget.vertices.push(morphTargets[i].vertices[j].clone())\n          }\n        }\n\n        // normals\n\n        if (morphTargets[i].normals !== undefined) {\n          morphTarget.normals = []\n\n          for (let j = 0, jl = morphTargets[i].normals.length; j < jl; j++) {\n            morphTarget.normals.push(morphTargets[i].normals[j].clone())\n          }\n        }\n\n        this.morphTargets.push(morphTarget)\n      }\n\n      // morph normals\n\n      const morphNormals = source.morphNormals\n\n      for (let i = 0, il = morphNormals.length; i < il; i++) {\n        const morphNormal = {}\n\n        // vertex normals\n\n        if (morphNormals[i].vertexNormals !== undefined) {\n          morphNormal.vertexNormals = []\n\n          for (let j = 0, jl = morphNormals[i].vertexNormals.length; j < jl; j++) {\n            const srcVertexNormal = morphNormals[i].vertexNormals[j]\n            const destVertexNormal = {}\n\n            destVertexNormal.a = srcVertexNormal.a.clone()\n            destVertexNormal.b = srcVertexNormal.b.clone()\n            destVertexNormal.c = srcVertexNormal.c.clone()\n\n            morphNormal.vertexNormals.push(destVertexNormal)\n          }\n        }\n\n        // face normals\n\n        if (morphNormals[i].faceNormals !== undefined) {\n          morphNormal.faceNormals = []\n\n          for (let j = 0, jl = morphNormals[i].faceNormals.length; j < jl; j++) {\n            morphNormal.faceNormals.push(morphNormals[i].faceNormals[j].clone())\n          }\n        }\n\n        this.morphNormals.push(morphNormal)\n      }\n\n      // skin weights\n\n      const skinWeights = source.skinWeights\n\n      for (let i = 0, il = skinWeights.length; i < il; i++) {\n        this.skinWeights.push(skinWeights[i].clone())\n      }\n\n      // skin indices\n\n      const skinIndices = source.skinIndices\n\n      for (let i = 0, il = skinIndices.length; i < il; i++) {\n        this.skinIndices.push(skinIndices[i].clone())\n      }\n\n      // line distances\n\n      const lineDistances = source.lineDistances\n\n      for (let i = 0, il = lineDistances.length; i < il; i++) {\n        this.lineDistances.push(lineDistances[i])\n      }\n\n      // bounding box\n\n      const boundingBox = source.boundingBox\n\n      if (boundingBox !== null) {\n        this.boundingBox = boundingBox.clone()\n      }\n\n      // bounding sphere\n\n      const boundingSphere = source.boundingSphere\n\n      if (boundingSphere !== null) {\n        this.boundingSphere = boundingSphere.clone()\n      }\n\n      // update flags\n\n      this.elementsNeedUpdate = source.elementsNeedUpdate\n      this.verticesNeedUpdate = source.verticesNeedUpdate\n      this.uvsNeedUpdate = source.uvsNeedUpdate\n      this.normalsNeedUpdate = source.normalsNeedUpdate\n      this.colorsNeedUpdate = source.colorsNeedUpdate\n      this.lineDistancesNeedUpdate = source.lineDistancesNeedUpdate\n      this.groupsNeedUpdate = source.groupsNeedUpdate\n\n      return this\n    }\n\n    toBufferGeometry() {\n      const geometry = new DirectGeometry().fromGeometry(this)\n\n      const buffergeometry = new BufferGeometry()\n\n      const positions = new Float32Array(geometry.vertices.length * 3)\n      buffergeometry.setAttribute('position', new BufferAttribute(positions, 3).copyVector3sArray(geometry.vertices))\n\n      if (geometry.normals.length > 0) {\n        const normals = new Float32Array(geometry.normals.length * 3)\n        buffergeometry.setAttribute('normal', new BufferAttribute(normals, 3).copyVector3sArray(geometry.normals))\n      }\n\n      if (geometry.colors.length > 0) {\n        const colors = new Float32Array(geometry.colors.length * 3)\n        buffergeometry.setAttribute('color', new BufferAttribute(colors, 3).copyColorsArray(geometry.colors))\n      }\n\n      if (geometry.uvs.length > 0) {\n        const uvs = new Float32Array(geometry.uvs.length * 2)\n        buffergeometry.setAttribute('uv', new BufferAttribute(uvs, 2).copyVector2sArray(geometry.uvs))\n      }\n\n      if (geometry.uvs2.length > 0) {\n        const uvs2 = new Float32Array(geometry.uvs2.length * 2)\n        buffergeometry.setAttribute('uv2', new BufferAttribute(uvs2, 2).copyVector2sArray(geometry.uvs2))\n      }\n\n      // groups\n\n      buffergeometry.groups = geometry.groups\n\n      // morphs\n\n      for (let name in geometry.morphTargets) {\n        const array = []\n        const morphTargets = geometry.morphTargets[name]\n\n        for (let i = 0, l = morphTargets.length; i < l; i++) {\n          const morphTarget = morphTargets[i]\n\n          const attribute = new Float32BufferAttribute(morphTarget.data.length * 3, 3)\n          attribute.name = morphTarget.name\n\n          array.push(attribute.copyVector3sArray(morphTarget.data))\n        }\n\n        buffergeometry.morphAttributes[name] = array\n      }\n\n      // skinning\n\n      if (geometry.skinIndices.length > 0) {\n        const skinIndices = new Float32BufferAttribute(geometry.skinIndices.length * 4, 4)\n        buffergeometry.setAttribute('skinIndex', skinIndices.copyVector4sArray(geometry.skinIndices))\n      }\n\n      if (geometry.skinWeights.length > 0) {\n        const skinWeights = new Float32BufferAttribute(geometry.skinWeights.length * 4, 4)\n        buffergeometry.setAttribute('skinWeight', skinWeights.copyVector4sArray(geometry.skinWeights))\n      }\n\n      //\n\n      if (geometry.boundingSphere !== null) {\n        buffergeometry.boundingSphere = geometry.boundingSphere.clone()\n      }\n\n      if (geometry.boundingBox !== null) {\n        buffergeometry.boundingBox = geometry.boundingBox.clone()\n      }\n\n      return buffergeometry\n    }\n\n    computeTangents() {\n      console.error('THREE.Geometry: .computeTangents() has been removed.')\n    }\n\n    computeLineDistances() {\n      console.error(\n        'THREE.Geometry: .computeLineDistances() has been removed. Use THREE.Line.computeLineDistances() instead.',\n      )\n    }\n\n    applyMatrix(matrix) {\n      console.warn('THREE.Geometry: .applyMatrix() has been renamed to .applyMatrix4().')\n      return this.applyMatrix4(matrix)\n    }\n\n    dispose() {\n      this.dispatchEvent({ type: 'dispose' })\n    }\n  }\n\n  return Geometry\n})()\n\nclass DirectGeometry {\n  constructor() {\n    this.vertices = []\n    this.normals = []\n    this.colors = []\n    this.uvs = []\n    this.uvs2 = []\n\n    this.groups = []\n\n    this.morphTargets = {}\n\n    this.skinWeights = []\n    this.skinIndices = []\n\n    // this.lineDistances = [];\n\n    this.boundingBox = null\n    this.boundingSphere = null\n\n    // update flags\n\n    this.verticesNeedUpdate = false\n    this.normalsNeedUpdate = false\n    this.colorsNeedUpdate = false\n    this.uvsNeedUpdate = false\n    this.groupsNeedUpdate = false\n  }\n\n  computeGroups(geometry) {\n    const groups = []\n\n    let group, i\n    let materialIndex = undefined\n\n    const faces = geometry.faces\n\n    for (i = 0; i < faces.length; i++) {\n      const face = faces[i]\n\n      // materials\n\n      if (face.materialIndex !== materialIndex) {\n        materialIndex = face.materialIndex\n\n        if (group !== undefined) {\n          group.count = i * 3 - group.start\n          groups.push(group)\n        }\n\n        group = {\n          start: i * 3,\n          materialIndex,\n        }\n      }\n    }\n\n    if (group !== undefined) {\n      group.count = i * 3 - group.start\n      groups.push(group)\n    }\n\n    this.groups = groups\n  }\n\n  fromGeometry(geometry) {\n    const faces = geometry.faces\n    const vertices = geometry.vertices\n    const faceVertexUvs = geometry.faceVertexUvs\n\n    const hasFaceVertexUv = faceVertexUvs[0] && faceVertexUvs[0].length > 0\n    const hasFaceVertexUv2 = faceVertexUvs[1] && faceVertexUvs[1].length > 0\n\n    // morphs\n\n    const morphTargets = geometry.morphTargets\n    const morphTargetsLength = morphTargets.length\n\n    let morphTargetsPosition\n\n    if (morphTargetsLength > 0) {\n      morphTargetsPosition = []\n\n      for (let i = 0; i < morphTargetsLength; i++) {\n        morphTargetsPosition[i] = {\n          name: morphTargets[i].name,\n          data: [],\n        }\n      }\n\n      this.morphTargets.position = morphTargetsPosition\n    }\n\n    const morphNormals = geometry.morphNormals\n    const morphNormalsLength = morphNormals.length\n\n    let morphTargetsNormal\n\n    if (morphNormalsLength > 0) {\n      morphTargetsNormal = []\n\n      for (let i = 0; i < morphNormalsLength; i++) {\n        morphTargetsNormal[i] = {\n          name: morphNormals[i].name,\n          data: [],\n        }\n      }\n\n      this.morphTargets.normal = morphTargetsNormal\n    }\n\n    // skins\n\n    const skinIndices = geometry.skinIndices\n    const skinWeights = geometry.skinWeights\n\n    const hasSkinIndices = skinIndices.length === vertices.length\n    const hasSkinWeights = skinWeights.length === vertices.length\n\n    //\n\n    if (vertices.length > 0 && faces.length === 0) {\n      console.error('THREE.DirectGeometry: Faceless geometries are not supported.')\n    }\n\n    for (let i = 0; i < faces.length; i++) {\n      const face = faces[i]\n\n      this.vertices.push(vertices[face.a], vertices[face.b], vertices[face.c])\n\n      const vertexNormals = face.vertexNormals\n\n      if (vertexNormals.length === 3) {\n        this.normals.push(vertexNormals[0], vertexNormals[1], vertexNormals[2])\n      } else {\n        const normal = face.normal\n\n        this.normals.push(normal, normal, normal)\n      }\n\n      const vertexColors = face.vertexColors\n\n      if (vertexColors.length === 3) {\n        this.colors.push(vertexColors[0], vertexColors[1], vertexColors[2])\n      } else {\n        const color = face.color\n\n        this.colors.push(color, color, color)\n      }\n\n      if (hasFaceVertexUv === true) {\n        const vertexUvs = faceVertexUvs[0][i]\n\n        if (vertexUvs !== undefined) {\n          this.uvs.push(vertexUvs[0], vertexUvs[1], vertexUvs[2])\n        } else {\n          console.warn('THREE.DirectGeometry.fromGeometry(): Undefined vertexUv ', i)\n\n          this.uvs.push(new Vector2(), new Vector2(), new Vector2())\n        }\n      }\n\n      if (hasFaceVertexUv2 === true) {\n        const vertexUvs = faceVertexUvs[1][i]\n\n        if (vertexUvs !== undefined) {\n          this.uvs2.push(vertexUvs[0], vertexUvs[1], vertexUvs[2])\n        } else {\n          console.warn('THREE.DirectGeometry.fromGeometry(): Undefined vertexUv2 ', i)\n\n          this.uvs2.push(new Vector2(), new Vector2(), new Vector2())\n        }\n      }\n\n      // morphs\n\n      for (let j = 0; j < morphTargetsLength; j++) {\n        const morphTarget = morphTargets[j].vertices\n\n        morphTargetsPosition[j].data.push(morphTarget[face.a], morphTarget[face.b], morphTarget[face.c])\n      }\n\n      for (let j = 0; j < morphNormalsLength; j++) {\n        const morphNormal = morphNormals[j].vertexNormals[i]\n\n        morphTargetsNormal[j].data.push(morphNormal.a, morphNormal.b, morphNormal.c)\n      }\n\n      // skins\n\n      if (hasSkinIndices) {\n        this.skinIndices.push(skinIndices[face.a], skinIndices[face.b], skinIndices[face.c])\n      }\n\n      if (hasSkinWeights) {\n        this.skinWeights.push(skinWeights[face.a], skinWeights[face.b], skinWeights[face.c])\n      }\n    }\n\n    this.computeGroups(geometry)\n\n    this.verticesNeedUpdate = geometry.verticesNeedUpdate\n    this.normalsNeedUpdate = geometry.normalsNeedUpdate\n    this.colorsNeedUpdate = geometry.colorsNeedUpdate\n    this.uvsNeedUpdate = geometry.uvsNeedUpdate\n    this.groupsNeedUpdate = geometry.groupsNeedUpdate\n\n    if (geometry.boundingSphere !== null) {\n      this.boundingSphere = geometry.boundingSphere.clone()\n    }\n\n    if (geometry.boundingBox !== null) {\n      this.boundingBox = geometry.boundingBox.clone()\n    }\n\n    return this\n  }\n}\n\nclass Face3 {\n  constructor(a, b, c, normal, color, materialIndex = 0) {\n    this.a = a\n    this.b = b\n    this.c = c\n\n    this.normal = normal && normal.isVector3 ? normal : new Vector3()\n    this.vertexNormals = Array.isArray(normal) ? normal : []\n\n    this.color = color && color.isColor ? color : new Color()\n    this.vertexColors = Array.isArray(color) ? color : []\n\n    this.materialIndex = materialIndex\n  }\n\n  clone() {\n    return new this.constructor().copy(this)\n  }\n\n  copy(source) {\n    this.a = source.a\n    this.b = source.b\n    this.c = source.c\n\n    this.normal.copy(source.normal)\n    this.color.copy(source.color)\n\n    this.materialIndex = source.materialIndex\n\n    for (let i = 0, il = source.vertexNormals.length; i < il; i++) {\n      this.vertexNormals[i] = source.vertexNormals[i].clone()\n    }\n\n    for (let i = 0, il = source.vertexColors.length; i < il; i++) {\n      this.vertexColors[i] = source.vertexColors[i].clone()\n    }\n\n    return this\n  }\n}\n\nexport { Face3, Geometry }\n"], "names": ["Matrix4", "Object3D", "Vector3", "Geometry", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BufferGeometry", "Float32BufferAttribute", "MathUtils", "Matrix3", "Color", "Vector2", "Box3", "Sphere", "BufferAttribute"], "mappings": ";;;AAgBA,MAAM,MAAsB,oBAAIA,MAAAA,QAAS;AACzC,MAAM,OAAuB,oBAAIC,MAAAA,SAAU;AAC3C,MAAM,UAA0B,oBAAIC,MAAAA,QAAS;AAExC,MAAC,WAA4B,uBAAM;AACtC,QAAMC,kBAAiBC,MAAAA,gBAAgB;AAAA,IACrC,OAAO,+BAA+B,QAAQ;AAC5C,UAAI,iBAAiB,IAAIC,qBAAgB;AAEzC,YAAM,WAAW,OAAO;AAExB,UAAI,OAAO,YAAY,OAAO,QAAQ;AACpC,cAAM,YAAY,IAAIC,6BAAuB,SAAS,SAAS,SAAS,GAAG,CAAC;AAC5E,cAAM,SAAS,IAAIA,6BAAuB,SAAS,OAAO,SAAS,GAAG,CAAC;AAEvE,uBAAe,aAAa,YAAY,UAAU,kBAAkB,SAAS,QAAQ,CAAC;AACtF,uBAAe,aAAa,SAAS,OAAO,gBAAgB,SAAS,MAAM,CAAC;AAE5E,YAAI,SAAS,iBAAiB,SAAS,cAAc,WAAW,SAAS,SAAS,QAAQ;AACxF,gBAAM,gBAAgB,IAAIA,MAAsB,uBAAC,SAAS,cAAc,QAAQ,CAAC;AAEjF,yBAAe,aAAa,gBAAgB,cAAc,UAAU,SAAS,aAAa,CAAC;AAAA,QAC5F;AAED,YAAI,SAAS,mBAAmB,MAAM;AACpC,yBAAe,iBAAiB,SAAS,eAAe,MAAO;AAAA,QAChE;AAED,YAAI,SAAS,gBAAgB,MAAM;AACjC,yBAAe,cAAc,SAAS,YAAY,MAAO;AAAA,QAC1D;AAAA,MACT,WAAiB,OAAO,QAAQ;AACxB,yBAAiB,SAAS,iBAAkB;AAAA,MAC7C;AAED,aAAO;AAAA,IACR;AAAA,IAED,cAAc;AACZ,YAAO;AACP,WAAK,aAAa;AAClB,WAAK,OAAOC,MAAS,UAAC,aAAc;AAEpC,WAAK,OAAO;AACZ,WAAK,OAAO;AAEZ,WAAK,WAAW,CAAE;AAClB,WAAK,SAAS,CAAE;AAChB,WAAK,QAAQ,CAAE;AACf,WAAK,gBAAgB,CAAC,EAAE;AAExB,WAAK,eAAe,CAAE;AACtB,WAAK,eAAe,CAAE;AAEtB,WAAK,cAAc,CAAE;AACrB,WAAK,cAAc,CAAE;AAErB,WAAK,gBAAgB,CAAE;AAEvB,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAItB,WAAK,qBAAqB;AAC1B,WAAK,qBAAqB;AAC1B,WAAK,gBAAgB;AACrB,WAAK,oBAAoB;AACzB,WAAK,mBAAmB;AACxB,WAAK,0BAA0B;AAC/B,WAAK,mBAAmB;AAAA,IACzB;AAAA,IAED,aAAa,QAAQ;AACnB,YAAM,eAAe,IAAIC,MAAAA,UAAU,gBAAgB,MAAM;AAEzD,eAAS,IAAI,GAAG,KAAK,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AACtD,cAAM,SAAS,KAAK,SAAS,CAAC;AAC9B,eAAO,aAAa,MAAM;AAAA,MAC3B;AAED,eAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,OAAO,KAAK,MAAM,CAAC;AACzB,aAAK,OAAO,aAAa,YAAY,EAAE,UAAW;AAElD,iBAAS,IAAI,GAAG,KAAK,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAK;AAC3D,eAAK,cAAc,CAAC,EAAE,aAAa,YAAY,EAAE,UAAW;AAAA,QAC7D;AAAA,MACF;AAED,UAAI,KAAK,gBAAgB,MAAM;AAC7B,aAAK,mBAAoB;AAAA,MAC1B;AAED,UAAI,KAAK,mBAAmB,MAAM;AAChC,aAAK,sBAAuB;AAAA,MAC7B;AAED,WAAK,qBAAqB;AAC1B,WAAK,oBAAoB;AAEzB,aAAO;AAAA,IACR;AAAA,IAED,QAAQ,OAAO;AAGb,UAAI,cAAc,KAAK;AAEvB,WAAK,aAAa,GAAG;AAErB,aAAO;AAAA,IACR;AAAA,IAED,QAAQ,OAAO;AAGb,UAAI,cAAc,KAAK;AAEvB,WAAK,aAAa,GAAG;AAErB,aAAO;AAAA,IACR;AAAA,IAED,QAAQ,OAAO;AAGb,UAAI,cAAc,KAAK;AAEvB,WAAK,aAAa,GAAG;AAErB,aAAO;AAAA,IACR;AAAA,IAED,UAAU,GAAG,GAAG,GAAG;AAGjB,UAAI,gBAAgB,GAAG,GAAG,CAAC;AAE3B,WAAK,aAAa,GAAG;AAErB,aAAO;AAAA,IACR;AAAA,IAED,MAAM,GAAG,GAAG,GAAG;AAGb,UAAI,UAAU,GAAG,GAAG,CAAC;AAErB,WAAK,aAAa,GAAG;AAErB,aAAO;AAAA,IACR;AAAA,IAED,OAAO,QAAQ;AACb,WAAK,OAAO,MAAM;AAElB,WAAK,aAAc;AAEnB,WAAK,aAAa,KAAK,MAAM;AAE7B,aAAO;AAAA,IACR;AAAA,IAED,mBAAmB,UAAU;AAC3B,YAAM,QAAQ;AAEd,YAAM,QAAQ,SAAS,UAAU,OAAO,SAAS,QAAQ;AACzD,YAAM,aAAa,SAAS;AAE5B,UAAI,WAAW,aAAa,QAAW;AACrC,gBAAQ,MAAM,kFAAkF;AAChG,eAAO;AAAA,MACR;AAED,YAAM,WAAW,WAAW;AAC5B,YAAM,SAAS,WAAW;AAC1B,YAAM,QAAQ,WAAW;AACzB,YAAM,KAAK,WAAW;AACtB,YAAM,MAAM,WAAW;AAEvB,UAAI,QAAQ;AAAW,aAAK,cAAc,CAAC,IAAI,CAAE;AAEjD,eAAS,IAAI,GAAG,IAAI,SAAS,OAAO,KAAK;AACvC,cAAM,SAAS,KAAK,IAAIN,MAAAA,QAAS,EAAC,oBAAoB,UAAU,CAAC,CAAC;AAElE,YAAI,UAAU,QAAW;AACvB,gBAAM,OAAO,KAAK,IAAIO,MAAAA,MAAO,EAAC,oBAAoB,OAAO,CAAC,CAAC;AAAA,QAC5D;AAAA,MACF;AAED,eAAS,QAAQ,GAAG,GAAG,GAAG,eAAe;AACvC,cAAM,eACJ,UAAU,SAAY,CAAA,IAAK,CAAC,MAAM,OAAO,CAAC,EAAE,SAAS,MAAM,OAAO,CAAC,EAAE,SAAS,MAAM,OAAO,CAAC,EAAE,OAAO;AAEvG,cAAM,gBACJ,WAAW,SACP,CAAE,IACF;AAAA,UACE,IAAIP,cAAS,EAAC,oBAAoB,QAAQ,CAAC;AAAA,UAC3C,IAAIA,cAAS,EAAC,oBAAoB,QAAQ,CAAC;AAAA,UAC3C,IAAIA,cAAS,EAAC,oBAAoB,QAAQ,CAAC;AAAA,QAC5C;AAEP,cAAM,OAAO,IAAI,MAAM,GAAG,GAAG,GAAG,eAAe,cAAc,aAAa;AAE1E,cAAM,MAAM,KAAK,IAAI;AAErB,YAAI,OAAO,QAAW;AACpB,gBAAM,cAAc,CAAC,EAAE,KAAK;AAAA,YAC1B,IAAIQ,cAAS,EAAC,oBAAoB,IAAI,CAAC;AAAA,YACvC,IAAIA,cAAS,EAAC,oBAAoB,IAAI,CAAC;AAAA,YACvC,IAAIA,cAAS,EAAC,oBAAoB,IAAI,CAAC;AAAA,UACnD,CAAW;AAAA,QACF;AAED,YAAI,QAAQ,QAAW;AACrB,gBAAM,cAAc,CAAC,EAAE,KAAK;AAAA,YAC1B,IAAIA,cAAS,EAAC,oBAAoB,KAAK,CAAC;AAAA,YACxC,IAAIA,cAAS,EAAC,oBAAoB,KAAK,CAAC;AAAA,YACxC,IAAIA,cAAS,EAAC,oBAAoB,KAAK,CAAC;AAAA,UACpD,CAAW;AAAA,QACF;AAAA,MACF;AAED,YAAM,SAAS,SAAS;AAExB,UAAI,OAAO,SAAS,GAAG;AACrB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAM,QAAQ,OAAO,CAAC;AAEtB,gBAAM,QAAQ,MAAM;AACpB,gBAAM,QAAQ,MAAM;AAEpB,mBAAS,IAAI,OAAO,KAAK,QAAQ,OAAO,IAAI,IAAI,KAAK,GAAG;AACtD,gBAAI,UAAU,QAAW;AACvB,sBAAQ,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,IAAI,CAAC,GAAG,MAAM,KAAK,IAAI,CAAC,GAAG,MAAM,aAAa;AAAA,YAC9F,OAAmB;AACL,sBAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM,aAAa;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA,MACT,OAAa;AACL,YAAI,UAAU,QAAW;AACvB,mBAAS,IAAI,GAAG,IAAI,MAAM,OAAO,KAAK,GAAG;AACvC,oBAAQ,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,IAAI,CAAC,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,UAC5D;AAAA,QACX,OAAe;AACL,mBAAS,IAAI,GAAG,IAAI,SAAS,OAAO,KAAK,GAAG;AAC1C,oBAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AAED,WAAK,mBAAoB;AAEzB,UAAI,SAAS,gBAAgB,MAAM;AACjC,aAAK,cAAc,SAAS,YAAY,MAAO;AAAA,MAChD;AAED,UAAI,SAAS,mBAAmB,MAAM;AACpC,aAAK,iBAAiB,SAAS,eAAe,MAAO;AAAA,MACtD;AAED,aAAO;AAAA,IACR;AAAA,IAED,SAAS;AACP,WAAK,mBAAoB;AAEzB,WAAK,YAAY,UAAU,OAAO,EAAE,OAAQ;AAE5C,WAAK,UAAU,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAE9C,aAAO;AAAA,IACR;AAAA,IAED,YAAY;AACV,WAAK,sBAAuB;AAE5B,YAAM,SAAS,KAAK,eAAe;AACnC,YAAM,SAAS,KAAK,eAAe;AAEnC,YAAM,IAAI,WAAW,IAAI,IAAI,IAAM;AAEnC,YAAM,SAAS,IAAIV,cAAS;AAC5B,aAAO,IAAI,GAAG,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC;AAE7F,WAAK,aAAa,MAAM;AAExB,aAAO;AAAA,IACR;AAAA,IAED,qBAAqB;AACnB,YAAM,KAAK,IAAIE,cAAS,GACtB,KAAK,IAAIA,MAAAA,QAAS;AAEpB,eAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,cAAM,KAAK,KAAK,SAAS,KAAK,CAAC;AAC/B,cAAM,KAAK,KAAK,SAAS,KAAK,CAAC;AAC/B,cAAM,KAAK,KAAK,SAAS,KAAK,CAAC;AAE/B,WAAG,WAAW,IAAI,EAAE;AACpB,WAAG,WAAW,IAAI,EAAE;AACpB,WAAG,MAAM,EAAE;AAEX,WAAG,UAAW;AAEd,aAAK,OAAO,KAAK,EAAE;AAAA,MACpB;AAAA,IACF;AAAA,IAED,qBAAqB,eAAe,MAAM;AACxC,YAAM,WAAW,IAAI,MAAM,KAAK,SAAS,MAAM;AAE/C,eAAS,IAAI,GAAG,KAAK,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AACtD,iBAAS,CAAC,IAAI,IAAIA,cAAS;AAAA,MAC5B;AAED,UAAI,cAAc;AAIhB,cAAM,KAAK,IAAIA,cAAS,GACtB,KAAK,IAAIA,MAAAA,QAAS;AAEpB,iBAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,gBAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,gBAAM,KAAK,KAAK,SAAS,KAAK,CAAC;AAC/B,gBAAM,KAAK,KAAK,SAAS,KAAK,CAAC;AAC/B,gBAAM,KAAK,KAAK,SAAS,KAAK,CAAC;AAE/B,aAAG,WAAW,IAAI,EAAE;AACpB,aAAG,WAAW,IAAI,EAAE;AACpB,aAAG,MAAM,EAAE;AAEX,mBAAS,KAAK,CAAC,EAAE,IAAI,EAAE;AACvB,mBAAS,KAAK,CAAC,EAAE,IAAI,EAAE;AACvB,mBAAS,KAAK,CAAC,EAAE,IAAI,EAAE;AAAA,QACxB;AAAA,MACT,OAAa;AACL,aAAK,mBAAoB;AAEzB,iBAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,gBAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,mBAAS,KAAK,CAAC,EAAE,IAAI,KAAK,MAAM;AAChC,mBAAS,KAAK,CAAC,EAAE,IAAI,KAAK,MAAM;AAChC,mBAAS,KAAK,CAAC,EAAE,IAAI,KAAK,MAAM;AAAA,QACjC;AAAA,MACF;AAED,eAAS,IAAI,GAAG,KAAK,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AACtD,iBAAS,CAAC,EAAE,UAAW;AAAA,MACxB;AAED,eAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,cAAM,gBAAgB,KAAK;AAE3B,YAAI,cAAc,WAAW,GAAG;AAC9B,wBAAc,CAAC,EAAE,KAAK,SAAS,KAAK,CAAC,CAAC;AACtC,wBAAc,CAAC,EAAE,KAAK,SAAS,KAAK,CAAC,CAAC;AACtC,wBAAc,CAAC,EAAE,KAAK,SAAS,KAAK,CAAC,CAAC;AAAA,QAChD,OAAe;AACL,wBAAc,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE,MAAO;AAC3C,wBAAc,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE,MAAO;AAC3C,wBAAc,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE,MAAO;AAAA,QAC5C;AAAA,MACF;AAED,UAAI,KAAK,MAAM,SAAS,GAAG;AACzB,aAAK,oBAAoB;AAAA,MAC1B;AAAA,IACF;AAAA,IAED,2BAA2B;AACzB,WAAK,mBAAoB;AAEzB,eAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,cAAM,gBAAgB,KAAK;AAE3B,YAAI,cAAc,WAAW,GAAG;AAC9B,wBAAc,CAAC,EAAE,KAAK,KAAK,MAAM;AACjC,wBAAc,CAAC,EAAE,KAAK,KAAK,MAAM;AACjC,wBAAc,CAAC,EAAE,KAAK,KAAK,MAAM;AAAA,QAC3C,OAAe;AACL,wBAAc,CAAC,IAAI,KAAK,OAAO,MAAO;AACtC,wBAAc,CAAC,IAAI,KAAK,OAAO,MAAO;AACtC,wBAAc,CAAC,IAAI,KAAK,OAAO,MAAO;AAAA,QACvC;AAAA,MACF;AAED,UAAI,KAAK,MAAM,SAAS,GAAG;AACzB,aAAK,oBAAoB;AAAA,MAC1B;AAAA,IACF;AAAA,IAED,sBAAsB;AAKpB,eAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,YAAI,CAAC,KAAK,sBAAsB;AAC9B,eAAK,uBAAuB,KAAK,OAAO,MAAO;AAAA,QACzD,OAAe;AACL,eAAK,qBAAqB,KAAK,KAAK,MAAM;AAAA,QAC3C;AAED,YAAI,CAAC,KAAK;AAAyB,eAAK,0BAA0B,CAAE;AAEpE,iBAAS,IAAI,GAAG,KAAK,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAK;AAC3D,cAAI,CAAC,KAAK,wBAAwB,CAAC,GAAG;AACpC,iBAAK,wBAAwB,CAAC,IAAI,KAAK,cAAc,CAAC,EAAE,MAAO;AAAA,UAC3E,OAAiB;AACL,iBAAK,wBAAwB,CAAC,EAAE,KAAK,KAAK,cAAc,CAAC,CAAC;AAAA,UAC3D;AAAA,QACF;AAAA,MACF;AAID,YAAM,SAAS,IAAIC,UAAU;AAC7B,aAAO,QAAQ,KAAK;AAEpB,eAAS,IAAI,GAAG,KAAK,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAK;AAG1D,YAAI,CAAC,KAAK,aAAa,CAAC,GAAG;AACzB,eAAK,aAAa,CAAC,IAAI,CAAE;AACzB,eAAK,aAAa,CAAC,EAAE,cAAc,CAAE;AACrC,eAAK,aAAa,CAAC,EAAE,gBAAgB,CAAE;AAEvC,gBAAM,iBAAiB,KAAK,aAAa,CAAC,EAAE;AAC5C,gBAAM,mBAAmB,KAAK,aAAa,CAAC,EAAE;AAE9C,mBAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,kBAAM,aAAa,IAAID,cAAS;AAChC,kBAAM,gBAAgB;AAAA,cACpB,GAAG,IAAIA,MAAAA,QAAS;AAAA,cAChB,GAAG,IAAIA,MAAAA,QAAS;AAAA,cAChB,GAAG,IAAIA,MAAAA,QAAS;AAAA,YACjB;AAED,2BAAe,KAAK,UAAU;AAC9B,6BAAiB,KAAK,aAAa;AAAA,UACpC;AAAA,QACF;AAED,cAAM,eAAe,KAAK,aAAa,CAAC;AAIxC,eAAO,WAAW,KAAK,aAAa,CAAC,EAAE;AAIvC,eAAO,mBAAoB;AAC3B,eAAO,qBAAsB;AAI7B,iBAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,gBAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,gBAAM,aAAa,aAAa,YAAY,CAAC;AAC7C,gBAAM,gBAAgB,aAAa,cAAc,CAAC;AAElD,qBAAW,KAAK,KAAK,MAAM;AAE3B,wBAAc,EAAE,KAAK,KAAK,cAAc,CAAC,CAAC;AAC1C,wBAAc,EAAE,KAAK,KAAK,cAAc,CAAC,CAAC;AAC1C,wBAAc,EAAE,KAAK,KAAK,cAAc,CAAC,CAAC;AAAA,QAC3C;AAAA,MACF;AAID,eAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,aAAK,SAAS,KAAK;AACnB,aAAK,gBAAgB,KAAK;AAAA,MAC3B;AAAA,IACF;AAAA,IAED,qBAAqB;AACnB,UAAI,KAAK,gBAAgB,MAAM;AAC7B,aAAK,cAAc,IAAIS,WAAM;AAAA,MAC9B;AAED,WAAK,YAAY,cAAc,KAAK,QAAQ;AAAA,IAC7C;AAAA,IAED,wBAAwB;AACtB,UAAI,KAAK,mBAAmB,MAAM;AAChC,aAAK,iBAAiB,IAAIC,aAAQ;AAAA,MACnC;AAED,WAAK,eAAe,cAAc,KAAK,QAAQ;AAAA,IAChD;AAAA,IAED,MAAM,UAAU,QAAQ,sBAAsB,GAAG;AAC/C,UAAI,EAAE,YAAY,SAAS,aAAa;AACtC,gBAAQ,MAAM,uEAAuE,QAAQ;AAC7F;AAAA,MACD;AAED,UAAI;AACJ,YAAM,eAAe,KAAK,SAAS,QACjC,YAAY,KAAK,UACjB,YAAY,SAAS,UACrB,SAAS,KAAK,OACd,SAAS,SAAS,OAClB,UAAU,KAAK,QACf,UAAU,SAAS;AAErB,UAAI,WAAW,QAAW;AACxB,uBAAe,IAAIJ,MAAAA,UAAU,gBAAgB,MAAM;AAAA,MACpD;AAID,eAAS,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,KAAK;AAClD,cAAM,SAAS,UAAU,CAAC;AAE1B,cAAM,aAAa,OAAO,MAAO;AAEjC,YAAI,WAAW;AAAW,qBAAW,aAAa,MAAM;AAExD,kBAAU,KAAK,UAAU;AAAA,MAC1B;AAID,eAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,gBAAQ,KAAK,QAAQ,CAAC,EAAE,MAAK,CAAE;AAAA,MAChC;AAID,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,cAAM,OAAO,OAAO,CAAC;AACrB,YAAI,QAAQ;AACZ,cAAM,oBAAoB,KAAK,eAC7B,mBAAmB,KAAK;AAE1B,cAAM,WAAW,IAAI,MAAM,KAAK,IAAI,cAAc,KAAK,IAAI,cAAc,KAAK,IAAI,YAAY;AAC9F,iBAAS,OAAO,KAAK,KAAK,MAAM;AAEhC,YAAI,iBAAiB,QAAW;AAC9B,mBAAS,OAAO,aAAa,YAAY,EAAE,UAAW;AAAA,QACvD;AAED,iBAAS,IAAI,GAAG,KAAK,kBAAkB,QAAQ,IAAI,IAAI,KAAK;AAC1D,mBAAS,kBAAkB,CAAC,EAAE,MAAO;AAErC,cAAI,iBAAiB,QAAW;AAC9B,mBAAO,aAAa,YAAY,EAAE,UAAW;AAAA,UAC9C;AAED,mBAAS,cAAc,KAAK,MAAM;AAAA,QACnC;AAED,iBAAS,MAAM,KAAK,KAAK,KAAK;AAE9B,iBAAS,IAAI,GAAG,KAAK,iBAAiB,QAAQ,IAAI,IAAI,KAAK;AACzD,kBAAQ,iBAAiB,CAAC;AAC1B,mBAAS,aAAa,KAAK,MAAM,MAAK,CAAE;AAAA,QACzC;AAED,iBAAS,gBAAgB,KAAK,gBAAgB;AAE9C,eAAO,KAAK,QAAQ;AAAA,MACrB;AAID,eAAS,IAAI,GAAG,KAAK,SAAS,cAAc,QAAQ,IAAI,IAAI,KAAK;AAC/D,cAAM,iBAAiB,SAAS,cAAc,CAAC;AAE/C,YAAI,KAAK,cAAc,CAAC,MAAM;AAAW,eAAK,cAAc,CAAC,IAAI,CAAE;AAEnE,iBAAS,IAAI,GAAG,KAAK,eAAe,QAAQ,IAAI,IAAI,KAAK;AACvD,gBAAM,OAAO,eAAe,CAAC,GAC3B,UAAU,CAAE;AAEd,mBAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,oBAAQ,KAAK,KAAK,CAAC,EAAE,MAAK,CAAE;AAAA,UAC7B;AAED,eAAK,cAAc,CAAC,EAAE,KAAK,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,IAED,UAAU,MAAM;AACd,UAAI,EAAE,QAAQ,KAAK,SAAS;AAC1B,gBAAQ,MAAM,mEAAmE,IAAI;AACrF;AAAA,MACD;AAED,UAAI,KAAK;AAAkB,aAAK,aAAc;AAE9C,WAAK,MAAM,KAAK,UAAU,KAAK,MAAM;AAAA,IACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQD,cAAc,kBAAkB,GAAG;AACjC,YAAM,cAAc,CAAE;AACtB,YAAM,SAAS,CAAE,GACf,UAAU,CAAE;AAEd,YAAM,YAAY,KAAK,IAAI,IAAI,eAAe;AAE9C,eAAS,IAAI,GAAG,KAAK,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AACtD,cAAM,IAAI,KAAK,SAAS,CAAC;AACzB,cAAM,MAAM,GAAG,KAAK,MAAM,EAAE,IAAI,SAAS,KAAK,KAAK,MAAM,EAAE,IAAI,SAAS,KAAK,KAAK,MAAM,EAAE,IAAI,SAAS;AAEvG,YAAI,YAAY,GAAG,MAAM,QAAW;AAClC,sBAAY,GAAG,IAAI;AACnB,iBAAO,KAAK,KAAK,SAAS,CAAC,CAAC;AAC5B,kBAAQ,CAAC,IAAI,OAAO,SAAS;AAAA,QACvC,OAAe;AAEL,kBAAQ,CAAC,IAAI,QAAQ,YAAY,GAAG,CAAC;AAAA,QACtC;AAAA,MACF;AAID,YAAM,sBAAsB,CAAE;AAE9B,eAAS,IAAI,GAAG,KAAK,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,aAAK,IAAI,QAAQ,KAAK,CAAC;AACvB,aAAK,IAAI,QAAQ,KAAK,CAAC;AACvB,aAAK,IAAI,QAAQ,KAAK,CAAC;AAEvB,cAAM,UAAU,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAIvC,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,QAAQ,CAAC,MAAM,SAAS,IAAI,KAAK,CAAC,GAAG;AACvC,gCAAoB,KAAK,CAAC;AAC1B;AAAA,UACD;AAAA,QACF;AAAA,MACF;AAED,eAAS,IAAI,oBAAoB,SAAS,GAAG,KAAK,GAAG,KAAK;AACxD,cAAM,MAAM,oBAAoB,CAAC;AAEjC,aAAK,MAAM,OAAO,KAAK,CAAC;AAExB,iBAAS,IAAI,GAAG,KAAK,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAK;AAC3D,eAAK,cAAc,CAAC,EAAE,OAAO,KAAK,CAAC;AAAA,QACpC;AAAA,MACF;AAID,YAAM,OAAO,KAAK,SAAS,SAAS,OAAO;AAC3C,WAAK,WAAW;AAChB,aAAO;AAAA,IACR;AAAA,IAED,cAAc,QAAQ;AACpB,WAAK,WAAW,CAAE;AAElB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,aAAK,SAAS,KAAK,IAAIN,MAAO,QAAC,MAAM,GAAG,MAAM,GAAG,MAAM,KAAK,CAAC,CAAC;AAAA,MAC/D;AAED,aAAO;AAAA,IACR;AAAA,IAED,2BAA2B;AACzB,YAAM,QAAQ,KAAK;AACnB,YAAM,SAAS,MAAM;AAIrB,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAM,CAAC,EAAE,MAAM;AAAA,MAChB;AAID,eAAS,kBAAkB,GAAG,GAAG;AAC/B,eAAO,EAAE,gBAAgB,EAAE;AAAA,MAC5B;AAED,YAAM,KAAK,iBAAiB;AAI5B,YAAM,OAAO,KAAK,cAAc,CAAC;AACjC,YAAM,OAAO,KAAK,cAAc,CAAC;AAEjC,UAAI,SAAS;AAEb,UAAI,QAAQ,KAAK,WAAW;AAAQ,kBAAU,CAAE;AAChD,UAAI,QAAQ,KAAK,WAAW;AAAQ,kBAAU,CAAE;AAEhD,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAM,KAAK,MAAM,CAAC,EAAE;AAEpB,YAAI;AAAS,kBAAQ,KAAK,KAAK,EAAE,CAAC;AAClC,YAAI;AAAS,kBAAQ,KAAK,KAAK,EAAE,CAAC;AAAA,MACnC;AAED,UAAI;AAAS,aAAK,cAAc,CAAC,IAAI;AACrC,UAAI;AAAS,aAAK,cAAc,CAAC,IAAI;AAAA,IACtC;AAAA,IAED,SAAS;AACP,YAAM,OAAO;AAAA,QACX,UAAU;AAAA,UACR,SAAS;AAAA,UACT,MAAM;AAAA,UACN,WAAW;AAAA,QACZ;AAAA,MACF;AAID,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO,KAAK;AACjB,UAAI,KAAK,SAAS;AAAI,aAAK,OAAO,KAAK;AAEvC,UAAI,KAAK,eAAe,QAAW;AACjC,cAAM,aAAa,KAAK;AAExB,iBAAS,OAAO,YAAY;AAC1B,cAAI,WAAW,GAAG,MAAM;AAAW,iBAAK,GAAG,IAAI,WAAW,GAAG;AAAA,QAC9D;AAED,eAAO;AAAA,MACR;AAED,YAAM,WAAW,CAAE;AAEnB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,cAAM,SAAS,KAAK,SAAS,CAAC;AAC9B,iBAAS,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,MAC3C;AAED,YAAM,QAAQ,CAAE;AAChB,YAAM,UAAU,CAAE;AAClB,YAAM,cAAc,CAAE;AACtB,YAAM,SAAS,CAAE;AACjB,YAAM,aAAa,CAAE;AACrB,YAAM,MAAM,CAAE;AACd,YAAM,UAAU,CAAE;AAElB,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,cAAM,OAAO,KAAK,MAAM,CAAC;AAEzB,cAAM,cAAc;AACpB,cAAM,YAAY;AAClB,cAAM,kBAAkB,KAAK,cAAc,CAAC,EAAE,CAAC,MAAM;AACrD,cAAM,gBAAgB,KAAK,OAAO,OAAQ,IAAG;AAC7C,cAAM,sBAAsB,KAAK,cAAc,SAAS;AACxD,cAAM,eAAe,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM;AAClF,cAAM,qBAAqB,KAAK,aAAa,SAAS;AAEtD,YAAI,WAAW;AAEf,mBAAW,OAAO,UAAU,GAAG,CAAC;AAChC,mBAAW,OAAO,UAAU,GAAG,WAAW;AAC1C,mBAAW,OAAO,UAAU,GAAG,SAAS;AACxC,mBAAW,OAAO,UAAU,GAAG,eAAe;AAC9C,mBAAW,OAAO,UAAU,GAAG,aAAa;AAC5C,mBAAW,OAAO,UAAU,GAAG,mBAAmB;AAClD,mBAAW,OAAO,UAAU,GAAG,YAAY;AAC3C,mBAAW,OAAO,UAAU,GAAG,kBAAkB;AAEjD,cAAM,KAAK,QAAQ;AACnB,cAAM,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACjC,cAAM,KAAK,KAAK,aAAa;AAE7B,YAAI,iBAAiB;AACnB,gBAAM,gBAAgB,KAAK,cAAc,CAAC,EAAE,CAAC;AAE7C,gBAAM,KAAK,WAAW,cAAc,CAAC,CAAC,GAAG,WAAW,cAAc,CAAC,CAAC,GAAG,WAAW,cAAc,CAAC,CAAC,CAAC;AAAA,QACpG;AAED,YAAI,eAAe;AACjB,gBAAM,KAAK,eAAe,KAAK,MAAM,CAAC;AAAA,QACvC;AAED,YAAI,qBAAqB;AACvB,gBAAM,gBAAgB,KAAK;AAE3B,gBAAM;AAAA,YACJ,eAAe,cAAc,CAAC,CAAC;AAAA,YAC/B,eAAe,cAAc,CAAC,CAAC;AAAA,YAC/B,eAAe,cAAc,CAAC,CAAC;AAAA,UAChC;AAAA,QACF;AAED,YAAI,cAAc;AAChB,gBAAM,KAAK,cAAc,KAAK,KAAK,CAAC;AAAA,QACrC;AAED,YAAI,oBAAoB;AACtB,gBAAM,eAAe,KAAK;AAE1B,gBAAM,KAAK,cAAc,aAAa,CAAC,CAAC,GAAG,cAAc,aAAa,CAAC,CAAC,GAAG,cAAc,aAAa,CAAC,CAAC,CAAC;AAAA,QAC1G;AAAA,MACF;AAED,eAAS,OAAO,OAAO,UAAU,SAAS;AACxC,eAAO,UAAU,QAAS,KAAK,WAAY,QAAQ,EAAE,KAAK;AAAA,MAC3D;AAED,eAAS,eAAe,QAAQ;AAC9B,cAAM,OAAO,OAAO,EAAE,SAAQ,IAAK,OAAO,EAAE,SAAU,IAAG,OAAO,EAAE,SAAU;AAE5E,YAAI,YAAY,IAAI,MAAM,QAAW;AACnC,iBAAO,YAAY,IAAI;AAAA,QACxB;AAED,oBAAY,IAAI,IAAI,QAAQ,SAAS;AACrC,gBAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAEzC,eAAO,YAAY,IAAI;AAAA,MACxB;AAED,eAAS,cAAc,OAAO;AAC5B,cAAM,OAAO,MAAM,EAAE,SAAQ,IAAK,MAAM,EAAE,SAAU,IAAG,MAAM,EAAE,SAAU;AAEzE,YAAI,WAAW,IAAI,MAAM,QAAW;AAClC,iBAAO,WAAW,IAAI;AAAA,QACvB;AAED,mBAAW,IAAI,IAAI,OAAO;AAC1B,eAAO,KAAK,MAAM,QAAQ;AAE1B,eAAO,WAAW,IAAI;AAAA,MACvB;AAED,eAAS,WAAW,IAAI;AACtB,cAAM,OAAO,GAAG,EAAE,SAAQ,IAAK,GAAG,EAAE,SAAU;AAE9C,YAAI,QAAQ,IAAI,MAAM,QAAW;AAC/B,iBAAO,QAAQ,IAAI;AAAA,QACpB;AAED,gBAAQ,IAAI,IAAI,IAAI,SAAS;AAC7B,YAAI,KAAK,GAAG,GAAG,GAAG,CAAC;AAEnB,eAAO,QAAQ,IAAI;AAAA,MACpB;AAED,WAAK,OAAO,CAAE;AAEd,WAAK,KAAK,WAAW;AACrB,WAAK,KAAK,UAAU;AACpB,UAAI,OAAO,SAAS;AAAG,aAAK,KAAK,SAAS;AAC1C,UAAI,IAAI,SAAS;AAAG,aAAK,KAAK,MAAM,CAAC,GAAG;AACxC,WAAK,KAAK,QAAQ;AAElB,aAAO;AAAA,IACR;AAAA,IAED,QAAQ;AAyBN,aAAO,IAAIC,UAAQ,EAAG,KAAK,IAAI;AAAA,IAChC;AAAA,IAED,KAAK,QAAQ;AAGX,WAAK,WAAW,CAAE;AAClB,WAAK,SAAS,CAAE;AAChB,WAAK,QAAQ,CAAE;AACf,WAAK,gBAAgB,CAAC,EAAE;AACxB,WAAK,eAAe,CAAE;AACtB,WAAK,eAAe,CAAE;AACtB,WAAK,cAAc,CAAE;AACrB,WAAK,cAAc,CAAE;AACrB,WAAK,gBAAgB,CAAE;AACvB,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAItB,WAAK,OAAO,OAAO;AAInB,YAAM,WAAW,OAAO;AAExB,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AACjD,aAAK,SAAS,KAAK,SAAS,CAAC,EAAE,OAAO;AAAA,MACvC;AAID,YAAM,SAAS,OAAO;AAEtB,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,aAAK,OAAO,KAAK,OAAO,CAAC,EAAE,OAAO;AAAA,MACnC;AAID,YAAM,QAAQ,OAAO;AAErB,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAC9C,aAAK,MAAM,KAAK,MAAM,CAAC,EAAE,OAAO;AAAA,MACjC;AAID,eAAS,IAAI,GAAG,KAAK,OAAO,cAAc,QAAQ,IAAI,IAAI,KAAK;AAC7D,cAAM,gBAAgB,OAAO,cAAc,CAAC;AAE5C,YAAI,KAAK,cAAc,CAAC,MAAM,QAAW;AACvC,eAAK,cAAc,CAAC,IAAI,CAAE;AAAA,QAC3B;AAED,iBAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAK;AACtD,gBAAM,MAAM,cAAc,CAAC,GACzB,UAAU,CAAE;AAEd,mBAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAC5C,kBAAM,KAAK,IAAI,CAAC;AAEhB,oBAAQ,KAAK,GAAG,OAAO;AAAA,UACxB;AAED,eAAK,cAAc,CAAC,EAAE,KAAK,OAAO;AAAA,QACnC;AAAA,MACF;AAID,YAAM,eAAe,OAAO;AAE5B,eAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAK;AACrD,cAAM,cAAc,CAAE;AACtB,oBAAY,OAAO,aAAa,CAAC,EAAE;AAInC,YAAI,aAAa,CAAC,EAAE,aAAa,QAAW;AAC1C,sBAAY,WAAW,CAAE;AAEzB,mBAAS,IAAI,GAAG,KAAK,aAAa,CAAC,EAAE,SAAS,QAAQ,IAAI,IAAI,KAAK;AACjE,wBAAY,SAAS,KAAK,aAAa,CAAC,EAAE,SAAS,CAAC,EAAE,OAAO;AAAA,UAC9D;AAAA,QACF;AAID,YAAI,aAAa,CAAC,EAAE,YAAY,QAAW;AACzC,sBAAY,UAAU,CAAE;AAExB,mBAAS,IAAI,GAAG,KAAK,aAAa,CAAC,EAAE,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChE,wBAAY,QAAQ,KAAK,aAAa,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO;AAAA,UAC5D;AAAA,QACF;AAED,aAAK,aAAa,KAAK,WAAW;AAAA,MACnC;AAID,YAAM,eAAe,OAAO;AAE5B,eAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAK;AACrD,cAAM,cAAc,CAAE;AAItB,YAAI,aAAa,CAAC,EAAE,kBAAkB,QAAW;AAC/C,sBAAY,gBAAgB,CAAE;AAE9B,mBAAS,IAAI,GAAG,KAAK,aAAa,CAAC,EAAE,cAAc,QAAQ,IAAI,IAAI,KAAK;AACtE,kBAAM,kBAAkB,aAAa,CAAC,EAAE,cAAc,CAAC;AACvD,kBAAM,mBAAmB,CAAE;AAE3B,6BAAiB,IAAI,gBAAgB,EAAE,MAAO;AAC9C,6BAAiB,IAAI,gBAAgB,EAAE,MAAO;AAC9C,6BAAiB,IAAI,gBAAgB,EAAE,MAAO;AAE9C,wBAAY,cAAc,KAAK,gBAAgB;AAAA,UAChD;AAAA,QACF;AAID,YAAI,aAAa,CAAC,EAAE,gBAAgB,QAAW;AAC7C,sBAAY,cAAc,CAAE;AAE5B,mBAAS,IAAI,GAAG,KAAK,aAAa,CAAC,EAAE,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpE,wBAAY,YAAY,KAAK,aAAa,CAAC,EAAE,YAAY,CAAC,EAAE,OAAO;AAAA,UACpE;AAAA,QACF;AAED,aAAK,aAAa,KAAK,WAAW;AAAA,MACnC;AAID,YAAM,cAAc,OAAO;AAE3B,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,aAAK,YAAY,KAAK,YAAY,CAAC,EAAE,OAAO;AAAA,MAC7C;AAID,YAAM,cAAc,OAAO;AAE3B,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,aAAK,YAAY,KAAK,YAAY,CAAC,EAAE,OAAO;AAAA,MAC7C;AAID,YAAM,gBAAgB,OAAO;AAE7B,eAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,KAAK;AACtD,aAAK,cAAc,KAAK,cAAc,CAAC,CAAC;AAAA,MACzC;AAID,YAAM,cAAc,OAAO;AAE3B,UAAI,gBAAgB,MAAM;AACxB,aAAK,cAAc,YAAY,MAAO;AAAA,MACvC;AAID,YAAM,iBAAiB,OAAO;AAE9B,UAAI,mBAAmB,MAAM;AAC3B,aAAK,iBAAiB,eAAe,MAAO;AAAA,MAC7C;AAID,WAAK,qBAAqB,OAAO;AACjC,WAAK,qBAAqB,OAAO;AACjC,WAAK,gBAAgB,OAAO;AAC5B,WAAK,oBAAoB,OAAO;AAChC,WAAK,mBAAmB,OAAO;AAC/B,WAAK,0BAA0B,OAAO;AACtC,WAAK,mBAAmB,OAAO;AAE/B,aAAO;AAAA,IACR;AAAA,IAED,mBAAmB;AACjB,YAAM,WAAW,IAAI,iBAAiB,aAAa,IAAI;AAEvD,YAAM,iBAAiB,IAAIE,qBAAgB;AAE3C,YAAM,YAAY,IAAI,aAAa,SAAS,SAAS,SAAS,CAAC;AAC/D,qBAAe,aAAa,YAAY,IAAIQ,sBAAgB,WAAW,CAAC,EAAE,kBAAkB,SAAS,QAAQ,CAAC;AAE9G,UAAI,SAAS,QAAQ,SAAS,GAAG;AAC/B,cAAM,UAAU,IAAI,aAAa,SAAS,QAAQ,SAAS,CAAC;AAC5D,uBAAe,aAAa,UAAU,IAAIA,sBAAgB,SAAS,CAAC,EAAE,kBAAkB,SAAS,OAAO,CAAC;AAAA,MAC1G;AAED,UAAI,SAAS,OAAO,SAAS,GAAG;AAC9B,cAAM,SAAS,IAAI,aAAa,SAAS,OAAO,SAAS,CAAC;AAC1D,uBAAe,aAAa,SAAS,IAAIA,sBAAgB,QAAQ,CAAC,EAAE,gBAAgB,SAAS,MAAM,CAAC;AAAA,MACrG;AAED,UAAI,SAAS,IAAI,SAAS,GAAG;AAC3B,cAAM,MAAM,IAAI,aAAa,SAAS,IAAI,SAAS,CAAC;AACpD,uBAAe,aAAa,MAAM,IAAIA,sBAAgB,KAAK,CAAC,EAAE,kBAAkB,SAAS,GAAG,CAAC;AAAA,MAC9F;AAED,UAAI,SAAS,KAAK,SAAS,GAAG;AAC5B,cAAM,OAAO,IAAI,aAAa,SAAS,KAAK,SAAS,CAAC;AACtD,uBAAe,aAAa,OAAO,IAAIA,sBAAgB,MAAM,CAAC,EAAE,kBAAkB,SAAS,IAAI,CAAC;AAAA,MACjG;AAID,qBAAe,SAAS,SAAS;AAIjC,eAAS,QAAQ,SAAS,cAAc;AACtC,cAAM,QAAQ,CAAE;AAChB,cAAM,eAAe,SAAS,aAAa,IAAI;AAE/C,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,IAAI,GAAG,KAAK;AACnD,gBAAM,cAAc,aAAa,CAAC;AAElC,gBAAM,YAAY,IAAIP,6BAAuB,YAAY,KAAK,SAAS,GAAG,CAAC;AAC3E,oBAAU,OAAO,YAAY;AAE7B,gBAAM,KAAK,UAAU,kBAAkB,YAAY,IAAI,CAAC;AAAA,QACzD;AAED,uBAAe,gBAAgB,IAAI,IAAI;AAAA,MACxC;AAID,UAAI,SAAS,YAAY,SAAS,GAAG;AACnC,cAAM,cAAc,IAAIA,6BAAuB,SAAS,YAAY,SAAS,GAAG,CAAC;AACjF,uBAAe,aAAa,aAAa,YAAY,kBAAkB,SAAS,WAAW,CAAC;AAAA,MAC7F;AAED,UAAI,SAAS,YAAY,SAAS,GAAG;AACnC,cAAM,cAAc,IAAIA,6BAAuB,SAAS,YAAY,SAAS,GAAG,CAAC;AACjF,uBAAe,aAAa,cAAc,YAAY,kBAAkB,SAAS,WAAW,CAAC;AAAA,MAC9F;AAID,UAAI,SAAS,mBAAmB,MAAM;AACpC,uBAAe,iBAAiB,SAAS,eAAe,MAAO;AAAA,MAChE;AAED,UAAI,SAAS,gBAAgB,MAAM;AACjC,uBAAe,cAAc,SAAS,YAAY,MAAO;AAAA,MAC1D;AAED,aAAO;AAAA,IACR;AAAA,IAED,kBAAkB;AAChB,cAAQ,MAAM,sDAAsD;AAAA,IACrE;AAAA,IAED,uBAAuB;AACrB,cAAQ;AAAA,QACN;AAAA,MACD;AAAA,IACF;AAAA,IAED,YAAY,QAAQ;AAClB,cAAQ,KAAK,qEAAqE;AAClF,aAAO,KAAK,aAAa,MAAM;AAAA,IAChC;AAAA,IAED,UAAU;AACR,WAAK,cAAc,EAAE,MAAM,UAAS,CAAE;AAAA,IACvC;AAAA,EACF;AAED,SAAOH;AACT,GAAI;AAEJ,MAAM,eAAe;AAAA,EACnB,cAAc;AACZ,SAAK,WAAW,CAAE;AAClB,SAAK,UAAU,CAAE;AACjB,SAAK,SAAS,CAAE;AAChB,SAAK,MAAM,CAAE;AACb,SAAK,OAAO,CAAE;AAEd,SAAK,SAAS,CAAE;AAEhB,SAAK,eAAe,CAAE;AAEtB,SAAK,cAAc,CAAE;AACrB,SAAK,cAAc,CAAE;AAIrB,SAAK,cAAc;AACnB,SAAK,iBAAiB;AAItB,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AAAA,EACzB;AAAA,EAED,cAAc,UAAU;AACtB,UAAM,SAAS,CAAE;AAEjB,QAAI,OAAO;AACX,QAAI,gBAAgB;AAEpB,UAAM,QAAQ,SAAS;AAEvB,SAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,YAAM,OAAO,MAAM,CAAC;AAIpB,UAAI,KAAK,kBAAkB,eAAe;AACxC,wBAAgB,KAAK;AAErB,YAAI,UAAU,QAAW;AACvB,gBAAM,QAAQ,IAAI,IAAI,MAAM;AAC5B,iBAAO,KAAK,KAAK;AAAA,QAClB;AAED,gBAAQ;AAAA,UACN,OAAO,IAAI;AAAA,UACX;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAED,QAAI,UAAU,QAAW;AACvB,YAAM,QAAQ,IAAI,IAAI,MAAM;AAC5B,aAAO,KAAK,KAAK;AAAA,IAClB;AAED,SAAK,SAAS;AAAA,EACf;AAAA,EAED,aAAa,UAAU;AACrB,UAAM,QAAQ,SAAS;AACvB,UAAM,WAAW,SAAS;AAC1B,UAAM,gBAAgB,SAAS;AAE/B,UAAM,kBAAkB,cAAc,CAAC,KAAK,cAAc,CAAC,EAAE,SAAS;AACtE,UAAM,mBAAmB,cAAc,CAAC,KAAK,cAAc,CAAC,EAAE,SAAS;AAIvE,UAAM,eAAe,SAAS;AAC9B,UAAM,qBAAqB,aAAa;AAExC,QAAI;AAEJ,QAAI,qBAAqB,GAAG;AAC1B,6BAAuB,CAAE;AAEzB,eAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AAC3C,6BAAqB,CAAC,IAAI;AAAA,UACxB,MAAM,aAAa,CAAC,EAAE;AAAA,UACtB,MAAM,CAAE;AAAA,QACT;AAAA,MACF;AAED,WAAK,aAAa,WAAW;AAAA,IAC9B;AAED,UAAM,eAAe,SAAS;AAC9B,UAAM,qBAAqB,aAAa;AAExC,QAAI;AAEJ,QAAI,qBAAqB,GAAG;AAC1B,2BAAqB,CAAE;AAEvB,eAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AAC3C,2BAAmB,CAAC,IAAI;AAAA,UACtB,MAAM,aAAa,CAAC,EAAE;AAAA,UACtB,MAAM,CAAE;AAAA,QACT;AAAA,MACF;AAED,WAAK,aAAa,SAAS;AAAA,IAC5B;AAID,UAAM,cAAc,SAAS;AAC7B,UAAM,cAAc,SAAS;AAE7B,UAAM,iBAAiB,YAAY,WAAW,SAAS;AACvD,UAAM,iBAAiB,YAAY,WAAW,SAAS;AAIvD,QAAI,SAAS,SAAS,KAAK,MAAM,WAAW,GAAG;AAC7C,cAAQ,MAAM,8DAA8D;AAAA,IAC7E;AAED,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,OAAO,MAAM,CAAC;AAEpB,WAAK,SAAS,KAAK,SAAS,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,CAAC;AAEvE,YAAM,gBAAgB,KAAK;AAE3B,UAAI,cAAc,WAAW,GAAG;AAC9B,aAAK,QAAQ,KAAK,cAAc,CAAC,GAAG,cAAc,CAAC,GAAG,cAAc,CAAC,CAAC;AAAA,MAC9E,OAAa;AACL,cAAM,SAAS,KAAK;AAEpB,aAAK,QAAQ,KAAK,QAAQ,QAAQ,MAAM;AAAA,MACzC;AAED,YAAM,eAAe,KAAK;AAE1B,UAAI,aAAa,WAAW,GAAG;AAC7B,aAAK,OAAO,KAAK,aAAa,CAAC,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC;AAAA,MAC1E,OAAa;AACL,cAAM,QAAQ,KAAK;AAEnB,aAAK,OAAO,KAAK,OAAO,OAAO,KAAK;AAAA,MACrC;AAED,UAAI,oBAAoB,MAAM;AAC5B,cAAM,YAAY,cAAc,CAAC,EAAE,CAAC;AAEpC,YAAI,cAAc,QAAW;AAC3B,eAAK,IAAI,KAAK,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AAAA,QAChE,OAAe;AACL,kBAAQ,KAAK,4DAA4D,CAAC;AAE1E,eAAK,IAAI,KAAK,IAAIO,MAAAA,QAAS,GAAE,IAAIA,MAAO,QAAA,GAAI,IAAIA,MAAAA,SAAS;AAAA,QAC1D;AAAA,MACF;AAED,UAAI,qBAAqB,MAAM;AAC7B,cAAM,YAAY,cAAc,CAAC,EAAE,CAAC;AAEpC,YAAI,cAAc,QAAW;AAC3B,eAAK,KAAK,KAAK,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AAAA,QACjE,OAAe;AACL,kBAAQ,KAAK,6DAA6D,CAAC;AAE3E,eAAK,KAAK,KAAK,IAAIA,MAAAA,QAAS,GAAE,IAAIA,MAAO,QAAA,GAAI,IAAIA,MAAAA,SAAS;AAAA,QAC3D;AAAA,MACF;AAID,eAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AAC3C,cAAM,cAAc,aAAa,CAAC,EAAE;AAEpC,6BAAqB,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,CAAC,GAAG,YAAY,KAAK,CAAC,GAAG,YAAY,KAAK,CAAC,CAAC;AAAA,MAChG;AAED,eAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AAC3C,cAAM,cAAc,aAAa,CAAC,EAAE,cAAc,CAAC;AAEnD,2BAAmB,CAAC,EAAE,KAAK,KAAK,YAAY,GAAG,YAAY,GAAG,YAAY,CAAC;AAAA,MAC5E;AAID,UAAI,gBAAgB;AAClB,aAAK,YAAY,KAAK,YAAY,KAAK,CAAC,GAAG,YAAY,KAAK,CAAC,GAAG,YAAY,KAAK,CAAC,CAAC;AAAA,MACpF;AAED,UAAI,gBAAgB;AAClB,aAAK,YAAY,KAAK,YAAY,KAAK,CAAC,GAAG,YAAY,KAAK,CAAC,GAAG,YAAY,KAAK,CAAC,CAAC;AAAA,MACpF;AAAA,IACF;AAED,SAAK,cAAc,QAAQ;AAE3B,SAAK,qBAAqB,SAAS;AACnC,SAAK,oBAAoB,SAAS;AAClC,SAAK,mBAAmB,SAAS;AACjC,SAAK,gBAAgB,SAAS;AAC9B,SAAK,mBAAmB,SAAS;AAEjC,QAAI,SAAS,mBAAmB,MAAM;AACpC,WAAK,iBAAiB,SAAS,eAAe,MAAO;AAAA,IACtD;AAED,QAAI,SAAS,gBAAgB,MAAM;AACjC,WAAK,cAAc,SAAS,YAAY,MAAO;AAAA,IAChD;AAED,WAAO;AAAA,EACR;AACH;AAEA,MAAM,MAAM;AAAA,EACV,YAAY,GAAG,GAAG,GAAG,QAAQ,OAAO,gBAAgB,GAAG;AACrD,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AAET,SAAK,SAAS,UAAU,OAAO,YAAY,SAAS,IAAIR,cAAS;AACjE,SAAK,gBAAgB,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAE;AAExD,SAAK,QAAQ,SAAS,MAAM,UAAU,QAAQ,IAAIO,YAAO;AACzD,SAAK,eAAe,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAE;AAErD,SAAK,gBAAgB;AAAA,EACtB;AAAA,EAED,QAAQ;AACN,WAAO,IAAI,KAAK,cAAc,KAAK,IAAI;AAAA,EACxC;AAAA,EAED,KAAK,QAAQ;AACX,SAAK,IAAI,OAAO;AAChB,SAAK,IAAI,OAAO;AAChB,SAAK,IAAI,OAAO;AAEhB,SAAK,OAAO,KAAK,OAAO,MAAM;AAC9B,SAAK,MAAM,KAAK,OAAO,KAAK;AAE5B,SAAK,gBAAgB,OAAO;AAE5B,aAAS,IAAI,GAAG,KAAK,OAAO,cAAc,QAAQ,IAAI,IAAI,KAAK;AAC7D,WAAK,cAAc,CAAC,IAAI,OAAO,cAAc,CAAC,EAAE,MAAO;AAAA,IACxD;AAED,aAAS,IAAI,GAAG,KAAK,OAAO,aAAa,QAAQ,IAAI,IAAI,KAAK;AAC5D,WAAK,aAAa,CAAC,IAAI,OAAO,aAAa,CAAC,EAAE,MAAO;AAAA,IACtD;AAED,WAAO;AAAA,EACR;AACH;;;"}