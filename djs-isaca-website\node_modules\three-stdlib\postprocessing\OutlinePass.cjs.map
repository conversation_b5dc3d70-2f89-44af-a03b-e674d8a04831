{"version": 3, "file": "OutlinePass.cjs", "sources": ["../../src/postprocessing/OutlinePass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport {\n  AdditiveBlending,\n  Camera,\n  Color,\n  DoubleSide,\n  Line,\n  Matrix4,\n  Mesh,\n  MeshDepthMaterial,\n  NoBlending,\n  Object3D,\n  PerspectiveCamera,\n  Points,\n  RGBADepthPacking,\n  Scene,\n  ShaderMaterial,\n  Sprite,\n  Texture,\n  UniformsUtils,\n  Vector2,\n  Vector3,\n  WebG<PERSON><PERSON>er,\n  WebGLRenderTarget,\n} from 'three'\nimport { CopyShader } from '../shaders/CopyShader'\n\nclass OutlinePass extends Pass {\n  public renderScene: Scene\n  public renderCamera: Camera\n  public selectedObjects: Object3D[]\n  public visibleEdgeColor: Color\n  public hiddenEdgeColor: Color\n  public edgeGlow: number\n  public usePatternTexture: boolean\n  public edgeThickness: number\n  public edgeStrength: number\n  public downSampleRatio: number\n  public pulsePeriod: number\n  public resolution: Vector2\n  public renderTargetMaskBuffer: WebGLRenderTarget\n  public depthMaterial: MeshDepthMaterial\n  public prepareMaskMaterial: ShaderMaterial\n  public renderTargetDepthBuffer: WebGLRenderTarget\n  public renderTargetMaskDownSampleBuffer: WebGLRenderTarget\n  public renderTargetBlurBuffer1: WebGLRenderTarget\n  public renderTargetBlurBuffer2: WebGLRenderTarget\n  public edgeDetectionMaterial: ShaderMaterial\n  public renderTargetEdgeBuffer1: WebGLRenderTarget\n  public renderTargetEdgeBuffer2: WebGLRenderTarget\n  public separableBlurMaterial1: ShaderMaterial\n  public separableBlurMaterial2: ShaderMaterial\n  public overlayMaterial: ShaderMaterial\n  public materialCopy: ShaderMaterial\n  public oldClearAlpha: number\n  public fsQuad: FullScreenQuad\n  public tempPulseColor1: Color\n  public tempPulseColor2: Color\n  public textureMatrix: Matrix4\n  public patternTexture?: Texture\n\n  private _visibilityCache: Map<Object3D, boolean>\n  private _oldClearColor: Color\n\n  public copyUniforms\n\n  public BlurDirectionX = new Vector2(1.0, 0.0)\n  public BlurDirectionY = new Vector2(0.0, 1.0)\n\n  constructor(resolution: Vector2, scene: Scene, camera: Camera, selectedObjects?: Object3D[]) {\n    super()\n\n    this.renderScene = scene\n    this.renderCamera = camera\n    this.selectedObjects = selectedObjects !== undefined ? selectedObjects : []\n    this.visibleEdgeColor = new Color(1, 1, 1)\n    this.hiddenEdgeColor = new Color(0.1, 0.04, 0.02)\n    this.edgeGlow = 0.0\n    this.usePatternTexture = false\n    this.edgeThickness = 1.0\n    this.edgeStrength = 3.0\n    this.downSampleRatio = 2\n    this.pulsePeriod = 0\n\n    this._visibilityCache = new Map()\n\n    this.resolution = resolution !== undefined ? new Vector2(resolution.x, resolution.y) : new Vector2(256, 256)\n\n    const resx = Math.round(this.resolution.x / this.downSampleRatio)\n    const resy = Math.round(this.resolution.y / this.downSampleRatio)\n\n    this.renderTargetMaskBuffer = new WebGLRenderTarget(this.resolution.x, this.resolution.y)\n    this.renderTargetMaskBuffer.texture.name = 'OutlinePass.mask'\n    this.renderTargetMaskBuffer.texture.generateMipmaps = false\n\n    this.depthMaterial = new MeshDepthMaterial()\n    this.depthMaterial.side = DoubleSide\n    this.depthMaterial.depthPacking = RGBADepthPacking\n    this.depthMaterial.blending = NoBlending\n\n    this.prepareMaskMaterial = this.getPrepareMaskMaterial()\n    this.prepareMaskMaterial.side = DoubleSide\n    this.prepareMaskMaterial.fragmentShader = replaceDepthToViewZ(\n      this.prepareMaskMaterial.fragmentShader,\n      this.renderCamera as PerspectiveCamera,\n    )\n\n    this.renderTargetDepthBuffer = new WebGLRenderTarget(this.resolution.x, this.resolution.y)\n    this.renderTargetDepthBuffer.texture.name = 'OutlinePass.depth'\n    this.renderTargetDepthBuffer.texture.generateMipmaps = false\n\n    this.renderTargetMaskDownSampleBuffer = new WebGLRenderTarget(resx, resy)\n    this.renderTargetMaskDownSampleBuffer.texture.name = 'OutlinePass.depthDownSample'\n    this.renderTargetMaskDownSampleBuffer.texture.generateMipmaps = false\n\n    this.renderTargetBlurBuffer1 = new WebGLRenderTarget(resx, resy)\n    this.renderTargetBlurBuffer1.texture.name = 'OutlinePass.blur1'\n    this.renderTargetBlurBuffer1.texture.generateMipmaps = false\n    this.renderTargetBlurBuffer2 = new WebGLRenderTarget(Math.round(resx / 2), Math.round(resy / 2))\n    this.renderTargetBlurBuffer2.texture.name = 'OutlinePass.blur2'\n    this.renderTargetBlurBuffer2.texture.generateMipmaps = false\n\n    this.edgeDetectionMaterial = this.getEdgeDetectionMaterial()\n    this.renderTargetEdgeBuffer1 = new WebGLRenderTarget(resx, resy)\n    this.renderTargetEdgeBuffer1.texture.name = 'OutlinePass.edge1'\n    this.renderTargetEdgeBuffer1.texture.generateMipmaps = false\n    this.renderTargetEdgeBuffer2 = new WebGLRenderTarget(Math.round(resx / 2), Math.round(resy / 2))\n    this.renderTargetEdgeBuffer2.texture.name = 'OutlinePass.edge2'\n    this.renderTargetEdgeBuffer2.texture.generateMipmaps = false\n\n    const MAX_EDGE_THICKNESS = 4\n    const MAX_EDGE_GLOW = 4\n\n    this.separableBlurMaterial1 = this.getSeperableBlurMaterial(MAX_EDGE_THICKNESS)\n    this.separableBlurMaterial1.uniforms['texSize'].value.set(resx, resy)\n    this.separableBlurMaterial1.uniforms['kernelRadius'].value = 1\n    this.separableBlurMaterial2 = this.getSeperableBlurMaterial(MAX_EDGE_GLOW)\n    this.separableBlurMaterial2.uniforms['texSize'].value.set(Math.round(resx / 2), Math.round(resy / 2))\n    this.separableBlurMaterial2.uniforms['kernelRadius'].value = MAX_EDGE_GLOW\n\n    // Overlay material\n    this.overlayMaterial = this.getOverlayMaterial()\n\n    // copy material\n    if (CopyShader === undefined) console.error('THREE.OutlinePass relies on CopyShader')\n\n    const copyShader = CopyShader\n\n    this.copyUniforms = UniformsUtils.clone(copyShader.uniforms)\n    this.copyUniforms['opacity'].value = 1.0\n\n    this.materialCopy = new ShaderMaterial({\n      uniforms: this.copyUniforms,\n      vertexShader: copyShader.vertexShader,\n      fragmentShader: copyShader.fragmentShader,\n      blending: NoBlending,\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n\n    this.enabled = true\n    this.needsSwap = false\n\n    this._oldClearColor = new Color()\n    this.oldClearAlpha = 1\n\n    this.fsQuad = new FullScreenQuad(this.materialCopy)\n\n    this.tempPulseColor1 = new Color()\n    this.tempPulseColor2 = new Color()\n    this.textureMatrix = new Matrix4()\n\n    function replaceDepthToViewZ(string: string, camera: PerspectiveCamera): string {\n      const type = camera.isPerspectiveCamera ? 'perspective' : 'orthographic'\n\n      return string.replace(/DEPTH_TO_VIEW_Z/g, type + 'DepthToViewZ')\n    }\n  }\n\n  public dispose(): void {\n    this.renderTargetMaskBuffer.dispose()\n    this.renderTargetDepthBuffer.dispose()\n    this.renderTargetMaskDownSampleBuffer.dispose()\n    this.renderTargetBlurBuffer1.dispose()\n    this.renderTargetBlurBuffer2.dispose()\n    this.renderTargetEdgeBuffer1.dispose()\n    this.renderTargetEdgeBuffer2.dispose()\n  }\n\n  public setSize(width: number, height: number): void {\n    this.renderTargetMaskBuffer.setSize(width, height)\n    this.renderTargetDepthBuffer.setSize(width, height)\n\n    let resx = Math.round(width / this.downSampleRatio)\n    let resy = Math.round(height / this.downSampleRatio)\n    this.renderTargetMaskDownSampleBuffer.setSize(resx, resy)\n    this.renderTargetBlurBuffer1.setSize(resx, resy)\n    this.renderTargetEdgeBuffer1.setSize(resx, resy)\n    this.separableBlurMaterial1.uniforms['texSize'].value.set(resx, resy)\n\n    resx = Math.round(resx / 2)\n    resy = Math.round(resy / 2)\n\n    this.renderTargetBlurBuffer2.setSize(resx, resy)\n    this.renderTargetEdgeBuffer2.setSize(resx, resy)\n\n    this.separableBlurMaterial2.uniforms['texSize'].value.set(resx, resy)\n  }\n\n  public changeVisibilityOfSelectedObjects(bVisible: boolean): void {\n    const cache = this._visibilityCache\n\n    function gatherSelectedMeshesCallBack(object: Object3D): void {\n      if ((object as Mesh).isMesh) {\n        if (bVisible === true) {\n          object.visible = cache.get(object) as boolean\n        } else {\n          cache.set(object, object.visible)\n          object.visible = bVisible\n        }\n      }\n    }\n\n    for (let i = 0; i < this.selectedObjects.length; i++) {\n      const selectedObject = this.selectedObjects[i]\n      selectedObject.traverse(gatherSelectedMeshesCallBack)\n    }\n  }\n\n  public changeVisibilityOfNonSelectedObjects(bVisible: boolean): void {\n    const cache = this._visibilityCache\n    const selectedMeshes: Object3D[] = []\n\n    function gatherSelectedMeshesCallBack(object: Object3D): void {\n      if ((object as Mesh).isMesh) selectedMeshes.push(object)\n    }\n\n    for (let i = 0; i < this.selectedObjects.length; i++) {\n      const selectedObject = this.selectedObjects[i]\n      selectedObject.traverse(gatherSelectedMeshesCallBack)\n    }\n\n    function VisibilityChangeCallBack(object: Object3D): void {\n      if ((object as Mesh).isMesh || (object as Sprite).isSprite) {\n        // only meshes and sprites are supported by OutlinePass\n\n        let bFound = false\n\n        for (let i = 0; i < selectedMeshes.length; i++) {\n          const selectedObjectId = selectedMeshes[i].id\n\n          if (selectedObjectId === object.id) {\n            bFound = true\n            break\n          }\n        }\n\n        if (bFound === false) {\n          const visibility = object.visible\n\n          if (bVisible === false || cache.get(object) === true) {\n            object.visible = bVisible\n          }\n\n          cache.set(object, visibility)\n        }\n      } else if ((object as Points).isPoints || (object as Line).isLine) {\n        // the visibilty of points and lines is always set to false in order to\n        // not affect the outline computation\n\n        if (bVisible === true) {\n          object.visible = cache.get(object) as boolean // restore\n        } else {\n          cache.set(object, object.visible)\n          object.visible = bVisible\n        }\n      }\n    }\n\n    this.renderScene.traverse(VisibilityChangeCallBack)\n  }\n\n  public updateTextureMatrix(): void {\n    this.textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n    this.textureMatrix.multiply(this.renderCamera.projectionMatrix)\n    this.textureMatrix.multiply(this.renderCamera.matrixWorldInverse)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    deltaTime: number,\n    maskActive: boolean,\n  ): void {\n    if (this.selectedObjects.length > 0) {\n      renderer.getClearColor(this._oldClearColor)\n      this.oldClearAlpha = renderer.getClearAlpha()\n      const oldAutoClear = renderer.autoClear\n\n      renderer.autoClear = false\n\n      if (maskActive) renderer.state.buffers.stencil.setTest(false)\n\n      renderer.setClearColor(0xffffff, 1)\n\n      // Make selected objects invisible\n      this.changeVisibilityOfSelectedObjects(false)\n\n      const currentBackground = this.renderScene.background\n      this.renderScene.background = null\n\n      // 1. Draw Non Selected objects in the depth buffer\n      this.renderScene.overrideMaterial = this.depthMaterial\n      renderer.setRenderTarget(this.renderTargetDepthBuffer)\n      renderer.clear()\n      renderer.render(this.renderScene, this.renderCamera)\n\n      // Make selected objects visible\n      this.changeVisibilityOfSelectedObjects(true)\n      this._visibilityCache.clear()\n\n      // Update Texture Matrix for Depth compare\n      this.updateTextureMatrix()\n\n      // Make non selected objects invisible, and draw only the selected objects, by comparing the depth buffer of non selected objects\n      this.changeVisibilityOfNonSelectedObjects(false)\n      this.renderScene.overrideMaterial = this.prepareMaskMaterial\n      this.prepareMaskMaterial.uniforms['cameraNearFar'].value.set(\n        (this.renderCamera as PerspectiveCamera).near,\n        (this.renderCamera as PerspectiveCamera).far,\n      )\n      this.prepareMaskMaterial.uniforms['depthTexture'].value = this.renderTargetDepthBuffer.texture\n      this.prepareMaskMaterial.uniforms['textureMatrix'].value = this.textureMatrix\n      renderer.setRenderTarget(this.renderTargetMaskBuffer)\n      renderer.clear()\n      renderer.render(this.renderScene, this.renderCamera)\n      this.renderScene.overrideMaterial = null\n      this.changeVisibilityOfNonSelectedObjects(true)\n      this._visibilityCache.clear()\n\n      this.renderScene.background = currentBackground\n\n      // 2. Downsample to Half resolution\n      this.fsQuad.material = this.materialCopy\n      this.copyUniforms['tDiffuse'].value = this.renderTargetMaskBuffer.texture\n      renderer.setRenderTarget(this.renderTargetMaskDownSampleBuffer)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      this.tempPulseColor1.copy(this.visibleEdgeColor)\n      this.tempPulseColor2.copy(this.hiddenEdgeColor)\n\n      if (this.pulsePeriod > 0) {\n        const scalar = (1 + 0.25) / 2 + (Math.cos((performance.now() * 0.01) / this.pulsePeriod) * (1.0 - 0.25)) / 2\n        this.tempPulseColor1.multiplyScalar(scalar)\n        this.tempPulseColor2.multiplyScalar(scalar)\n      }\n\n      // 3. Apply Edge Detection Pass\n      this.fsQuad.material = this.edgeDetectionMaterial\n      this.edgeDetectionMaterial.uniforms['maskTexture'].value = this.renderTargetMaskDownSampleBuffer.texture\n      this.edgeDetectionMaterial.uniforms['texSize'].value.set(\n        this.renderTargetMaskDownSampleBuffer.width,\n        this.renderTargetMaskDownSampleBuffer.height,\n      )\n      this.edgeDetectionMaterial.uniforms['visibleEdgeColor'].value = this.tempPulseColor1\n      this.edgeDetectionMaterial.uniforms['hiddenEdgeColor'].value = this.tempPulseColor2\n      renderer.setRenderTarget(this.renderTargetEdgeBuffer1)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // 4. Apply Blur on Half res\n      this.fsQuad.material = this.separableBlurMaterial1\n      this.separableBlurMaterial1.uniforms['colorTexture'].value = this.renderTargetEdgeBuffer1.texture\n      this.separableBlurMaterial1.uniforms['direction'].value = this.BlurDirectionX\n      this.separableBlurMaterial1.uniforms['kernelRadius'].value = this.edgeThickness\n      renderer.setRenderTarget(this.renderTargetBlurBuffer1)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n      this.separableBlurMaterial1.uniforms['colorTexture'].value = this.renderTargetBlurBuffer1.texture\n      this.separableBlurMaterial1.uniforms['direction'].value = this.BlurDirectionY\n      renderer.setRenderTarget(this.renderTargetEdgeBuffer1)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // Apply Blur on quarter res\n      this.fsQuad.material = this.separableBlurMaterial2\n      this.separableBlurMaterial2.uniforms['colorTexture'].value = this.renderTargetEdgeBuffer1.texture\n      this.separableBlurMaterial2.uniforms['direction'].value = this.BlurDirectionX\n      renderer.setRenderTarget(this.renderTargetBlurBuffer2)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n      this.separableBlurMaterial2.uniforms['colorTexture'].value = this.renderTargetBlurBuffer2.texture\n      this.separableBlurMaterial2.uniforms['direction'].value = this.BlurDirectionY\n      renderer.setRenderTarget(this.renderTargetEdgeBuffer2)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // Blend it additively over the input texture\n      this.fsQuad.material = this.overlayMaterial\n      this.overlayMaterial.uniforms['maskTexture'].value = this.renderTargetMaskBuffer.texture\n      this.overlayMaterial.uniforms['edgeTexture1'].value = this.renderTargetEdgeBuffer1.texture\n      this.overlayMaterial.uniforms['edgeTexture2'].value = this.renderTargetEdgeBuffer2.texture\n      this.overlayMaterial.uniforms['patternTexture'].value = this.patternTexture\n      this.overlayMaterial.uniforms['edgeStrength'].value = this.edgeStrength\n      this.overlayMaterial.uniforms['edgeGlow'].value = this.edgeGlow\n      this.overlayMaterial.uniforms['usePatternTexture'].value = this.usePatternTexture\n\n      if (maskActive) renderer.state.buffers.stencil.setTest(true)\n\n      renderer.setRenderTarget(readBuffer)\n      this.fsQuad.render(renderer)\n\n      renderer.setClearColor(this._oldClearColor, this.oldClearAlpha)\n      renderer.autoClear = oldAutoClear\n    }\n\n    if (this.renderToScreen) {\n      this.fsQuad.material = this.materialCopy\n      this.copyUniforms['tDiffuse'].value = readBuffer.texture\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  public getPrepareMaskMaterial(): ShaderMaterial {\n    return new ShaderMaterial({\n      uniforms: {\n        depthTexture: { value: null },\n        cameraNearFar: { value: new Vector2(0.5, 0.5) },\n        textureMatrix: { value: null },\n      },\n\n      vertexShader: `#include <morphtarget_pars_vertex>\n\t\t\t\t#include <skinning_pars_vertex>\n\t\t\t\tvarying vec4 projTexCoord;\n\t\t\t\tvarying vec4 vPosition;\n\t\t\t\tuniform mat4 textureMatrix;\n\t\t\t\tvoid main() {\n\t\t\t\t\t#include <skinbase_vertex>\n\t\t\t\t\t#include <begin_vertex>\n\t\t\t\t\t#include <morphtarget_vertex>\n\t\t\t\t\t#include <skinning_vertex>\n\t\t\t\t\t#include <project_vertex>\n\t\t\t\t\tvPosition = mvPosition;\n\t\t\t\t\tvec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n\t\t\t\t\tprojTexCoord = textureMatrix * worldPosition;\n\t\t\t\t}`,\n\n      fragmentShader: `#include <packing>\n\t\t\t\tvarying vec4 vPosition;\n\t\t\t\tvarying vec4 projTexCoord;\n\t\t\t\tuniform sampler2D depthTexture;\n\t\t\t\tuniform vec2 cameraNearFar;\n\t\t\t\tvoid main() {\n\t\t\t\t\tfloat depth = unpackRGBAToDepth(texture2DProj( depthTexture, projTexCoord ));\n\t\t\t\t\tfloat viewZ = - DEPTH_TO_VIEW_Z( depth, cameraNearFar.x, cameraNearFar.y );\n\t\t\t\t\tfloat depthTest = (-vPosition.z > viewZ) ? 1.0 : 0.0;\n\t\t\t\t\tgl_FragColor = vec4(0.0, depthTest, 1.0, 1.0);\n\t\t\t\t}`,\n    })\n  }\n\n  public getEdgeDetectionMaterial(): ShaderMaterial {\n    return new ShaderMaterial({\n      uniforms: {\n        maskTexture: { value: null },\n        texSize: { value: new Vector2(0.5, 0.5) },\n        visibleEdgeColor: { value: new Vector3(1.0, 1.0, 1.0) },\n        hiddenEdgeColor: { value: new Vector3(1.0, 1.0, 1.0) },\n      },\n\n      vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n      fragmentShader: `varying vec2 vUv;\n\t\t\t\tuniform sampler2D maskTexture;\n\t\t\t\tuniform vec2 texSize;\n\t\t\t\tuniform vec3 visibleEdgeColor;\n\t\t\t\tuniform vec3 hiddenEdgeColor;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec2 invSize = 1.0 / texSize;\n\t\t\t\t\tvec4 uvOffset = vec4(1.0, 0.0, 0.0, 1.0) * vec4(invSize, invSize);\n\t\t\t\t\tvec4 c1 = texture2D( maskTexture, vUv + uvOffset.xy);\n\t\t\t\t\tvec4 c2 = texture2D( maskTexture, vUv - uvOffset.xy);\n\t\t\t\t\tvec4 c3 = texture2D( maskTexture, vUv + uvOffset.yw);\n\t\t\t\t\tvec4 c4 = texture2D( maskTexture, vUv - uvOffset.yw);\n\t\t\t\t\tfloat diff1 = (c1.r - c2.r)*0.5;\n\t\t\t\t\tfloat diff2 = (c3.r - c4.r)*0.5;\n\t\t\t\t\tfloat d = length( vec2(diff1, diff2) );\n\t\t\t\t\tfloat a1 = min(c1.g, c2.g);\n\t\t\t\t\tfloat a2 = min(c3.g, c4.g);\n\t\t\t\t\tfloat visibilityFactor = min(a1, a2);\n\t\t\t\t\tvec3 edgeColor = 1.0 - visibilityFactor > 0.001 ? visibleEdgeColor : hiddenEdgeColor;\n\t\t\t\t\tgl_FragColor = vec4(edgeColor, 1.0) * vec4(d);\n\t\t\t\t}`,\n    })\n  }\n\n  public getSeperableBlurMaterial(maxRadius: number): ShaderMaterial {\n    return new ShaderMaterial({\n      defines: {\n        MAX_RADIUS: maxRadius,\n      },\n\n      uniforms: {\n        colorTexture: { value: null },\n        texSize: { value: new Vector2(0.5, 0.5) },\n        direction: { value: new Vector2(0.5, 0.5) },\n        kernelRadius: { value: 1.0 },\n      },\n\n      vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n      fragmentShader: `#include <common>\n\t\t\t\tvarying vec2 vUv;\n\t\t\t\tuniform sampler2D colorTexture;\n\t\t\t\tuniform vec2 texSize;\n\t\t\t\tuniform vec2 direction;\n\t\t\t\tuniform float kernelRadius;\n\t\t\t\tfloat gaussianPdf(in float x, in float sigma) {\n\t\t\t\t\treturn 0.39894 * exp( -0.5 * x * x/( sigma * sigma))/sigma;\n\t\t\t\t}\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec2 invSize = 1.0 / texSize;\n\t\t\t\t\tfloat weightSum = gaussianPdf(0.0, kernelRadius);\n\t\t\t\t\tvec4 diffuseSum = texture2D( colorTexture, vUv) * weightSum;\n\t\t\t\t\tvec2 delta = direction * invSize * kernelRadius/float(MAX_RADIUS);\n\t\t\t\t\tvec2 uvOffset = delta;\n\t\t\t\t\tfor( int i = 1; i <= MAX_RADIUS; i ++ ) {\n\t\t\t\t\t\tfloat w = gaussianPdf(uvOffset.x, kernelRadius);\n\t\t\t\t\t\tvec4 sample1 = texture2D( colorTexture, vUv + uvOffset);\n\t\t\t\t\t\tvec4 sample2 = texture2D( colorTexture, vUv - uvOffset);\n\t\t\t\t\t\tdiffuseSum += ((sample1 + sample2) * w);\n\t\t\t\t\t\tweightSum += (2.0 * w);\n\t\t\t\t\t\tuvOffset += delta;\n\t\t\t\t\t}\n\t\t\t\t\tgl_FragColor = diffuseSum/weightSum;\n\t\t\t\t}`,\n    })\n  }\n\n  public getOverlayMaterial(): ShaderMaterial {\n    return new ShaderMaterial({\n      uniforms: {\n        maskTexture: { value: null },\n        edgeTexture1: { value: null },\n        edgeTexture2: { value: null },\n        patternTexture: { value: null },\n        edgeStrength: { value: 1.0 },\n        edgeGlow: { value: 1.0 },\n        usePatternTexture: { value: 0.0 },\n      },\n\n      vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n      fragmentShader: `varying vec2 vUv;\n\t\t\t\tuniform sampler2D maskTexture;\n\t\t\t\tuniform sampler2D edgeTexture1;\n\t\t\t\tuniform sampler2D edgeTexture2;\n\t\t\t\tuniform sampler2D patternTexture;\n\t\t\t\tuniform float edgeStrength;\n\t\t\t\tuniform float edgeGlow;\n\t\t\t\tuniform bool usePatternTexture;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec4 edgeValue1 = texture2D(edgeTexture1, vUv);\n\t\t\t\t\tvec4 edgeValue2 = texture2D(edgeTexture2, vUv);\n\t\t\t\t\tvec4 maskColor = texture2D(maskTexture, vUv);\n\t\t\t\t\tvec4 patternColor = texture2D(patternTexture, 6.0 * vUv);\n\t\t\t\t\tfloat visibilityFactor = 1.0 - maskColor.g > 0.0 ? 1.0 : 0.5;\n\t\t\t\t\tvec4 edgeValue = edgeValue1 + edgeValue2 * edgeGlow;\n\t\t\t\t\tvec4 finalColor = edgeStrength * maskColor.r * edgeValue;\n\t\t\t\t\tif(usePatternTexture)\n\t\t\t\t\t\tfinalColor += + visibilityFactor * (1.0 - maskColor.r) * (1.0 - patternColor.r);\n\t\t\t\t\tgl_FragColor = finalColor;\n\t\t\t\t}`,\n      blending: AdditiveBlending,\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n    })\n  }\n}\n\nexport { OutlinePass }\n"], "names": ["Pass", "Vector2", "Color", "WebGLRenderTarget", "MeshDepthMaterial", "DoubleSide", "RGBADepthPacking", "NoBlending", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UniformsUtils", "ShaderMaterial", "FullScreenQuad", "Matrix4", "camera", "Vector3", "AdditiveBlending"], "mappings": ";;;;;;;;;;;AA2BA,MAAM,oBAAoBA,KAAAA,KAAK;AAAA,EA0C7B,YAAY,YAAqB,OAAc,QAAgB,iBAA8B;AACrF;AA1CD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEC;AACA;AAED;AAEA,0CAAiB,IAAIC,MAAAA,QAAQ,GAAK,CAAG;AACrC,0CAAiB,IAAIA,MAAAA,QAAQ,GAAK,CAAG;AAK1C,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,kBAAkB,oBAAoB,SAAY,kBAAkB,CAAA;AACzE,SAAK,mBAAmB,IAAIC,MAAAA,MAAM,GAAG,GAAG,CAAC;AACzC,SAAK,kBAAkB,IAAIA,MAAAA,MAAM,KAAK,MAAM,IAAI;AAChD,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AAEd,SAAA,uCAAuB;AAE5B,SAAK,aAAa,eAAe,SAAY,IAAID,MAAQ,QAAA,WAAW,GAAG,WAAW,CAAC,IAAI,IAAIA,MAAA,QAAQ,KAAK,GAAG;AAE3G,UAAM,OAAO,KAAK,MAAM,KAAK,WAAW,IAAI,KAAK,eAAe;AAChE,UAAM,OAAO,KAAK,MAAM,KAAK,WAAW,IAAI,KAAK,eAAe;AAE3D,SAAA,yBAAyB,IAAIE,MAAAA,kBAAkB,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC;AACnF,SAAA,uBAAuB,QAAQ,OAAO;AACtC,SAAA,uBAAuB,QAAQ,kBAAkB;AAEjD,SAAA,gBAAgB,IAAIC,MAAAA;AACzB,SAAK,cAAc,OAAOC;AAC1B,SAAK,cAAc,eAAeC;AAClC,SAAK,cAAc,WAAWC;AAEzB,SAAA,sBAAsB,KAAK;AAChC,SAAK,oBAAoB,OAAOF;AAChC,SAAK,oBAAoB,iBAAiB;AAAA,MACxC,KAAK,oBAAoB;AAAA,MACzB,KAAK;AAAA,IAAA;AAGF,SAAA,0BAA0B,IAAIF,MAAAA,kBAAkB,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC;AACpF,SAAA,wBAAwB,QAAQ,OAAO;AACvC,SAAA,wBAAwB,QAAQ,kBAAkB;AAEvD,SAAK,mCAAmC,IAAIA,MAAkB,kBAAA,MAAM,IAAI;AACnE,SAAA,iCAAiC,QAAQ,OAAO;AAChD,SAAA,iCAAiC,QAAQ,kBAAkB;AAEhE,SAAK,0BAA0B,IAAIA,MAAkB,kBAAA,MAAM,IAAI;AAC1D,SAAA,wBAAwB,QAAQ,OAAO;AACvC,SAAA,wBAAwB,QAAQ,kBAAkB;AACvD,SAAK,0BAA0B,IAAIA,wBAAkB,KAAK,MAAM,OAAO,CAAC,GAAG,KAAK,MAAM,OAAO,CAAC,CAAC;AAC1F,SAAA,wBAAwB,QAAQ,OAAO;AACvC,SAAA,wBAAwB,QAAQ,kBAAkB;AAElD,SAAA,wBAAwB,KAAK;AAClC,SAAK,0BAA0B,IAAIA,MAAkB,kBAAA,MAAM,IAAI;AAC1D,SAAA,wBAAwB,QAAQ,OAAO;AACvC,SAAA,wBAAwB,QAAQ,kBAAkB;AACvD,SAAK,0BAA0B,IAAIA,wBAAkB,KAAK,MAAM,OAAO,CAAC,GAAG,KAAK,MAAM,OAAO,CAAC,CAAC;AAC1F,SAAA,wBAAwB,QAAQ,OAAO;AACvC,SAAA,wBAAwB,QAAQ,kBAAkB;AAEvD,UAAM,qBAAqB;AAC3B,UAAM,gBAAgB;AAEjB,SAAA,yBAAyB,KAAK,yBAAyB,kBAAkB;AAC9E,SAAK,uBAAuB,SAAS,SAAS,EAAE,MAAM,IAAI,MAAM,IAAI;AACpE,SAAK,uBAAuB,SAAS,cAAc,EAAE,QAAQ;AACxD,SAAA,yBAAyB,KAAK,yBAAyB,aAAa;AACzE,SAAK,uBAAuB,SAAS,SAAS,EAAE,MAAM,IAAI,KAAK,MAAM,OAAO,CAAC,GAAG,KAAK,MAAM,OAAO,CAAC,CAAC;AACpG,SAAK,uBAAuB,SAAS,cAAc,EAAE,QAAQ;AAGxD,SAAA,kBAAkB,KAAK;AAG5B,QAAIK,WAAAA,eAAe;AAAW,cAAQ,MAAM,wCAAwC;AAEpF,UAAM,aAAaA,WAAAA;AAEnB,SAAK,eAAeC,MAAA,cAAc,MAAM,WAAW,QAAQ;AACtD,SAAA,aAAa,SAAS,EAAE,QAAQ;AAEhC,SAAA,eAAe,IAAIC,qBAAe;AAAA,MACrC,UAAU,KAAK;AAAA,MACf,cAAc,WAAW;AAAA,MACzB,gBAAgB,WAAW;AAAA,MAC3B,UAAUH,MAAA;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IAAA,CACd;AAED,SAAK,UAAU;AACf,SAAK,YAAY;AAEZ,SAAA,iBAAiB,IAAIL,MAAAA;AAC1B,SAAK,gBAAgB;AAErB,SAAK,SAAS,IAAIS,KAAe,eAAA,KAAK,YAAY;AAE7C,SAAA,kBAAkB,IAAIT,MAAAA;AACtB,SAAA,kBAAkB,IAAIA,MAAAA;AACtB,SAAA,gBAAgB,IAAIU,MAAAA;AAEhB,aAAA,oBAAoB,QAAgBC,SAAmC;AACxE,YAAA,OAAOA,QAAO,sBAAsB,gBAAgB;AAE1D,aAAO,OAAO,QAAQ,oBAAoB,OAAO,cAAc;AAAA,IACjE;AAAA,EACF;AAAA,EAEO,UAAgB;AACrB,SAAK,uBAAuB;AAC5B,SAAK,wBAAwB;AAC7B,SAAK,iCAAiC;AACtC,SAAK,wBAAwB;AAC7B,SAAK,wBAAwB;AAC7B,SAAK,wBAAwB;AAC7B,SAAK,wBAAwB;EAC/B;AAAA,EAEO,QAAQ,OAAe,QAAsB;AAC7C,SAAA,uBAAuB,QAAQ,OAAO,MAAM;AAC5C,SAAA,wBAAwB,QAAQ,OAAO,MAAM;AAElD,QAAI,OAAO,KAAK,MAAM,QAAQ,KAAK,eAAe;AAClD,QAAI,OAAO,KAAK,MAAM,SAAS,KAAK,eAAe;AAC9C,SAAA,iCAAiC,QAAQ,MAAM,IAAI;AACnD,SAAA,wBAAwB,QAAQ,MAAM,IAAI;AAC1C,SAAA,wBAAwB,QAAQ,MAAM,IAAI;AAC/C,SAAK,uBAAuB,SAAS,SAAS,EAAE,MAAM,IAAI,MAAM,IAAI;AAE7D,WAAA,KAAK,MAAM,OAAO,CAAC;AACnB,WAAA,KAAK,MAAM,OAAO,CAAC;AAErB,SAAA,wBAAwB,QAAQ,MAAM,IAAI;AAC1C,SAAA,wBAAwB,QAAQ,MAAM,IAAI;AAE/C,SAAK,uBAAuB,SAAS,SAAS,EAAE,MAAM,IAAI,MAAM,IAAI;AAAA,EACtE;AAAA,EAEO,kCAAkC,UAAyB;AAChE,UAAM,QAAQ,KAAK;AAEnB,aAAS,6BAA6B,QAAwB;AAC5D,UAAK,OAAgB,QAAQ;AAC3B,YAAI,aAAa,MAAM;AACd,iBAAA,UAAU,MAAM,IAAI,MAAM;AAAA,QAAA,OAC5B;AACC,gBAAA,IAAI,QAAQ,OAAO,OAAO;AAChC,iBAAO,UAAU;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,gBAAgB,QAAQ,KAAK;AAC9C,YAAA,iBAAiB,KAAK,gBAAgB,CAAC;AAC7C,qBAAe,SAAS,4BAA4B;AAAA,IACtD;AAAA,EACF;AAAA,EAEO,qCAAqC,UAAyB;AACnE,UAAM,QAAQ,KAAK;AACnB,UAAM,iBAA6B,CAAA;AAEnC,aAAS,6BAA6B,QAAwB;AAC5D,UAAK,OAAgB;AAAQ,uBAAe,KAAK,MAAM;AAAA,IACzD;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,gBAAgB,QAAQ,KAAK;AAC9C,YAAA,iBAAiB,KAAK,gBAAgB,CAAC;AAC7C,qBAAe,SAAS,4BAA4B;AAAA,IACtD;AAEA,aAAS,yBAAyB,QAAwB;AACnD,UAAA,OAAgB,UAAW,OAAkB,UAAU;AAG1D,YAAI,SAAS;AAEb,iBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AACxC,gBAAA,mBAAmB,eAAe,CAAC,EAAE;AAEvC,cAAA,qBAAqB,OAAO,IAAI;AACzB,qBAAA;AACT;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW,OAAO;AACpB,gBAAM,aAAa,OAAO;AAE1B,cAAI,aAAa,SAAS,MAAM,IAAI,MAAM,MAAM,MAAM;AACpD,mBAAO,UAAU;AAAA,UACnB;AAEM,gBAAA,IAAI,QAAQ,UAAU;AAAA,QAC9B;AAAA,MACU,WAAA,OAAkB,YAAa,OAAgB,QAAQ;AAIjE,YAAI,aAAa,MAAM;AACd,iBAAA,UAAU,MAAM,IAAI,MAAM;AAAA,QAAA,OAC5B;AACC,gBAAA,IAAI,QAAQ,OAAO,OAAO;AAChC,iBAAO,UAAU;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAEK,SAAA,YAAY,SAAS,wBAAwB;AAAA,EACpD;AAAA,EAEO,sBAA4B;AACjC,SAAK,cAAc,IAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;AACrG,SAAK,cAAc,SAAS,KAAK,aAAa,gBAAgB;AAC9D,SAAK,cAAc,SAAS,KAAK,aAAa,kBAAkB;AAAA,EAClE;AAAA,EAEO,OACL,UACA,aACA,YACA,WACA,YACM;AACF,QAAA,KAAK,gBAAgB,SAAS,GAAG;AAC1B,eAAA,cAAc,KAAK,cAAc;AACrC,WAAA,gBAAgB,SAAS;AAC9B,YAAM,eAAe,SAAS;AAE9B,eAAS,YAAY;AAEjB,UAAA;AAAY,iBAAS,MAAM,QAAQ,QAAQ,QAAQ,KAAK;AAEnD,eAAA,cAAc,UAAU,CAAC;AAGlC,WAAK,kCAAkC,KAAK;AAEtC,YAAA,oBAAoB,KAAK,YAAY;AAC3C,WAAK,YAAY,aAAa;AAGzB,WAAA,YAAY,mBAAmB,KAAK;AAChC,eAAA,gBAAgB,KAAK,uBAAuB;AACrD,eAAS,MAAM;AACf,eAAS,OAAO,KAAK,aAAa,KAAK,YAAY;AAGnD,WAAK,kCAAkC,IAAI;AAC3C,WAAK,iBAAiB;AAGtB,WAAK,oBAAoB;AAGzB,WAAK,qCAAqC,KAAK;AAC1C,WAAA,YAAY,mBAAmB,KAAK;AACzC,WAAK,oBAAoB,SAAS,eAAe,EAAE,MAAM;AAAA,QACtD,KAAK,aAAmC;AAAA,QACxC,KAAK,aAAmC;AAAA,MAAA;AAE3C,WAAK,oBAAoB,SAAS,cAAc,EAAE,QAAQ,KAAK,wBAAwB;AACvF,WAAK,oBAAoB,SAAS,eAAe,EAAE,QAAQ,KAAK;AACvD,eAAA,gBAAgB,KAAK,sBAAsB;AACpD,eAAS,MAAM;AACf,eAAS,OAAO,KAAK,aAAa,KAAK,YAAY;AACnD,WAAK,YAAY,mBAAmB;AACpC,WAAK,qCAAqC,IAAI;AAC9C,WAAK,iBAAiB;AAEtB,WAAK,YAAY,aAAa;AAGzB,WAAA,OAAO,WAAW,KAAK;AAC5B,WAAK,aAAa,UAAU,EAAE,QAAQ,KAAK,uBAAuB;AACzD,eAAA,gBAAgB,KAAK,gCAAgC;AAC9D,eAAS,MAAM;AACV,WAAA,OAAO,OAAO,QAAQ;AAEtB,WAAA,gBAAgB,KAAK,KAAK,gBAAgB;AAC1C,WAAA,gBAAgB,KAAK,KAAK,eAAe;AAE1C,UAAA,KAAK,cAAc,GAAG;AACxB,cAAM,UAAU,IAAI,QAAQ,IAAK,KAAK,IAAK,YAAY,IAAI,IAAI,OAAQ,KAAK,WAAW,KAAK,IAAM,QAAS;AACtG,aAAA,gBAAgB,eAAe,MAAM;AACrC,aAAA,gBAAgB,eAAe,MAAM;AAAA,MAC5C;AAGK,WAAA,OAAO,WAAW,KAAK;AAC5B,WAAK,sBAAsB,SAAS,aAAa,EAAE,QAAQ,KAAK,iCAAiC;AACjG,WAAK,sBAAsB,SAAS,SAAS,EAAE,MAAM;AAAA,QACnD,KAAK,iCAAiC;AAAA,QACtC,KAAK,iCAAiC;AAAA,MAAA;AAExC,WAAK,sBAAsB,SAAS,kBAAkB,EAAE,QAAQ,KAAK;AACrE,WAAK,sBAAsB,SAAS,iBAAiB,EAAE,QAAQ,KAAK;AAC3D,eAAA,gBAAgB,KAAK,uBAAuB;AACrD,eAAS,MAAM;AACV,WAAA,OAAO,OAAO,QAAQ;AAGtB,WAAA,OAAO,WAAW,KAAK;AAC5B,WAAK,uBAAuB,SAAS,cAAc,EAAE,QAAQ,KAAK,wBAAwB;AAC1F,WAAK,uBAAuB,SAAS,WAAW,EAAE,QAAQ,KAAK;AAC/D,WAAK,uBAAuB,SAAS,cAAc,EAAE,QAAQ,KAAK;AACzD,eAAA,gBAAgB,KAAK,uBAAuB;AACrD,eAAS,MAAM;AACV,WAAA,OAAO,OAAO,QAAQ;AAC3B,WAAK,uBAAuB,SAAS,cAAc,EAAE,QAAQ,KAAK,wBAAwB;AAC1F,WAAK,uBAAuB,SAAS,WAAW,EAAE,QAAQ,KAAK;AACtD,eAAA,gBAAgB,KAAK,uBAAuB;AACrD,eAAS,MAAM;AACV,WAAA,OAAO,OAAO,QAAQ;AAGtB,WAAA,OAAO,WAAW,KAAK;AAC5B,WAAK,uBAAuB,SAAS,cAAc,EAAE,QAAQ,KAAK,wBAAwB;AAC1F,WAAK,uBAAuB,SAAS,WAAW,EAAE,QAAQ,KAAK;AACtD,eAAA,gBAAgB,KAAK,uBAAuB;AACrD,eAAS,MAAM;AACV,WAAA,OAAO,OAAO,QAAQ;AAC3B,WAAK,uBAAuB,SAAS,cAAc,EAAE,QAAQ,KAAK,wBAAwB;AAC1F,WAAK,uBAAuB,SAAS,WAAW,EAAE,QAAQ,KAAK;AACtD,eAAA,gBAAgB,KAAK,uBAAuB;AACrD,eAAS,MAAM;AACV,WAAA,OAAO,OAAO,QAAQ;AAGtB,WAAA,OAAO,WAAW,KAAK;AAC5B,WAAK,gBAAgB,SAAS,aAAa,EAAE,QAAQ,KAAK,uBAAuB;AACjF,WAAK,gBAAgB,SAAS,cAAc,EAAE,QAAQ,KAAK,wBAAwB;AACnF,WAAK,gBAAgB,SAAS,cAAc,EAAE,QAAQ,KAAK,wBAAwB;AACnF,WAAK,gBAAgB,SAAS,gBAAgB,EAAE,QAAQ,KAAK;AAC7D,WAAK,gBAAgB,SAAS,cAAc,EAAE,QAAQ,KAAK;AAC3D,WAAK,gBAAgB,SAAS,UAAU,EAAE,QAAQ,KAAK;AACvD,WAAK,gBAAgB,SAAS,mBAAmB,EAAE,QAAQ,KAAK;AAE5D,UAAA;AAAY,iBAAS,MAAM,QAAQ,QAAQ,QAAQ,IAAI;AAE3D,eAAS,gBAAgB,UAAU;AAC9B,WAAA,OAAO,OAAO,QAAQ;AAE3B,eAAS,cAAc,KAAK,gBAAgB,KAAK,aAAa;AAC9D,eAAS,YAAY;AAAA,IACvB;AAEA,QAAI,KAAK,gBAAgB;AAClB,WAAA,OAAO,WAAW,KAAK;AAC5B,WAAK,aAAa,UAAU,EAAE,QAAQ,WAAW;AACjD,eAAS,gBAAgB,IAAI;AACxB,WAAA,OAAO,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACF;AAAA,EAEO,yBAAyC;AAC9C,WAAO,IAAIH,MAAAA,eAAe;AAAA,MACxB,UAAU;AAAA,QACR,cAAc,EAAE,OAAO,KAAK;AAAA,QAC5B,eAAe,EAAE,OAAO,IAAIT,MAAQ,QAAA,KAAK,GAAG,EAAE;AAAA,QAC9C,eAAe,EAAE,OAAO,KAAK;AAAA,MAC/B;AAAA,MAEA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgBd,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAA,CAWjB;AAAA,EACH;AAAA,EAEO,2BAA2C;AAChD,WAAO,IAAIS,MAAAA,eAAe;AAAA,MACxB,UAAU;AAAA,QACR,aAAa,EAAE,OAAO,KAAK;AAAA,QAC3B,SAAS,EAAE,OAAO,IAAIT,MAAQ,QAAA,KAAK,GAAG,EAAE;AAAA,QACxC,kBAAkB,EAAE,OAAO,IAAIa,MAAAA,QAAQ,GAAK,GAAK,CAAG,EAAE;AAAA,QACtD,iBAAiB,EAAE,OAAO,IAAIA,MAAAA,QAAQ,GAAK,GAAK,CAAG,EAAE;AAAA,MACvD;AAAA,MAEA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAMd,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAA,CAqBjB;AAAA,EACH;AAAA,EAEO,yBAAyB,WAAmC;AACjE,WAAO,IAAIJ,MAAAA,eAAe;AAAA,MACxB,SAAS;AAAA,QACP,YAAY;AAAA,MACd;AAAA,MAEA,UAAU;AAAA,QACR,cAAc,EAAE,OAAO,KAAK;AAAA,QAC5B,SAAS,EAAE,OAAO,IAAIT,MAAQ,QAAA,KAAK,GAAG,EAAE;AAAA,QACxC,WAAW,EAAE,OAAO,IAAIA,MAAQ,QAAA,KAAK,GAAG,EAAE;AAAA,QAC1C,cAAc,EAAE,OAAO,EAAI;AAAA,MAC7B;AAAA,MAEA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAMd,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAA,CAyBjB;AAAA,EACH;AAAA,EAEO,qBAAqC;AAC1C,WAAO,IAAIS,MAAAA,eAAe;AAAA,MACxB,UAAU;AAAA,QACR,aAAa,EAAE,OAAO,KAAK;AAAA,QAC3B,cAAc,EAAE,OAAO,KAAK;AAAA,QAC5B,cAAc,EAAE,OAAO,KAAK;AAAA,QAC5B,gBAAgB,EAAE,OAAO,KAAK;AAAA,QAC9B,cAAc,EAAE,OAAO,EAAI;AAAA,QAC3B,UAAU,EAAE,OAAO,EAAI;AAAA,QACvB,mBAAmB,EAAE,OAAO,EAAI;AAAA,MAClC;AAAA,MAEA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,MAMd,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoBhB,UAAUK,MAAA;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IAAA,CACd;AAAA,EACH;AACF;;"}