{"version": 3, "file": "ClearPass.js", "sources": ["../../src/postprocessing/ClearPass.ts"], "sourcesContent": ["import { Pass } from './Pass'\nimport { Color, WebGLRenderer, WebGLRenderTarget } from 'three'\n\nclass ClearPass extends Pass {\n  public clearColor: Color | string | number\n  public clearAlpha: number\n\n  private _oldClearColor: Color\n\n  constructor(clearColor?: Color | string | number, clearAlpha?: number) {\n    super()\n    this.needsSwap = false\n    this.clearColor = clearColor !== undefined ? clearColor : 0x000000\n    this.clearAlpha = clearAlpha !== undefined ? clearAlpha : 0\n    this._oldClearColor = new Color()\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    let oldClearAlpha\n\n    if (this.clearColor) {\n      renderer.getClearColor(this._oldClearColor)\n      oldClearAlpha = renderer.getClearAlpha()\n      renderer.setClearColor(this.clearColor, this.clearAlpha)\n    }\n\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer)\n    renderer.clear()\n\n    if (this.clearColor) {\n      renderer.setClearColor(this._oldClearColor, oldClearAlpha)\n    }\n  }\n}\n\nexport { ClearPass }\n"], "names": [], "mappings": ";;;;;;;;AAGA,MAAM,kBAAkB,KAAK;AAAA,EAM3B,YAAY,YAAsC,YAAqB;AAC/D;AAND;AACA;AAEC;AAIN,SAAK,YAAY;AACZ,SAAA,aAAa,eAAe,SAAY,aAAa;AACrD,SAAA,aAAa,eAAe,SAAY,aAAa;AACrD,SAAA,iBAAiB,IAAI;EAC5B;AAAA,EAEO,OACL,UACA,aACA,YAEM;AACF,QAAA;AAEJ,QAAI,KAAK,YAAY;AACV,eAAA,cAAc,KAAK,cAAc;AAC1C,sBAAgB,SAAS;AACzB,eAAS,cAAc,KAAK,YAAY,KAAK,UAAU;AAAA,IACzD;AAEA,aAAS,gBAAgB,KAAK,iBAAiB,OAAO,UAAU;AAChE,aAAS,MAAM;AAEf,QAAI,KAAK,YAAY;AACV,eAAA,cAAc,KAAK,gBAAgB,aAAa;AAAA,IAC3D;AAAA,EACF;AACF;"}