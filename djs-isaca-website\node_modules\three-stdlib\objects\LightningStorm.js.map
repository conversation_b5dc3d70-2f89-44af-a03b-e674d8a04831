{"version": 3, "file": "LightningStorm.js", "sources": ["../../src/objects/LightningStorm.js"], "sourcesContent": ["import { MathUtils, Mesh, MeshBasicMaterial, Object3D } from 'three'\nimport { LightningStrike } from '../geometries/LightningStrike'\n\n/**\n * @fileoverview Lightning strike object generator\n *\n *\n * Usage\n *\n * const myStorm = new LightningStorm( paramsObject );\n * myStorm.position.set( ... );\n * scene.add( myStorm );\n * ...\n * myStorm.update( currentTime );\n *\n * The \"currentTime\" can only go forwards or be stopped.\n *\n *\n * LightningStorm parameters:\n *\n * @param {double} size Size of the storm. If no 'onRayPosition' parameter is defined, it means the side of the rectangle the storm covers.\n *\n * @param {double} minHeight Minimum height a ray can start at. If no 'onRayPosition' parameter is defined, it means the height above plane y = 0.\n *\n * @param {double} maxHeight Maximum height a ray can start at. If no 'onRayPosition' parameter is defined, it means the height above plane y = 0.\n *\n * @param {double} maxSlope The maximum inclination slope of a ray. If no 'onRayPosition' parameter is defined, it means the slope relative to plane y = 0.\n *\n * @param {integer} maxLightnings Greater than 0. The maximum number of simultaneous rays.\n *\n * @param {double} lightningMinPeriod minimum time between two consecutive rays.\n *\n * @param {double} lightningMaxPeriod maximum time between two consecutive rays.\n *\n * @param {double} lightningMinDuration The minimum time a ray can last.\n *\n * @param {double} lightningMaxDuration The maximum time a ray can last.\n *\n * @param {Object} lightningParameters The parameters for created rays. See LightningStrike (geometry)\n *\n * @param {Material} lightningMaterial The THREE.Material used for the created rays.\n *\n * @param {function} onRayPosition Optional callback with two Vector3 parameters (source, dest). You can set here the start and end points for each created ray, using the standard size, minHeight, etc parameters and other values in your algorithm.\n *\n * @param {function} onLightningDown This optional callback is called with one parameter (lightningStrike) when a ray ends propagating, so it has hit the ground.\n *\n *\n */\n\nclass LightningStorm extends Object3D {\n  constructor(stormParams = {}) {\n    super()\n\n    this.isLightningStorm = true\n\n    // Parameters\n\n    this.stormParams = stormParams\n\n    stormParams.size = stormParams.size !== undefined ? stormParams.size : 1000.0\n    stormParams.minHeight = stormParams.minHeight !== undefined ? stormParams.minHeight : 80.0\n    stormParams.maxHeight = stormParams.maxHeight !== undefined ? stormParams.maxHeight : 100.0\n    stormParams.maxSlope = stormParams.maxSlope !== undefined ? stormParams.maxSlope : 1.1\n\n    stormParams.maxLightnings = stormParams.maxLightnings !== undefined ? stormParams.maxLightnings : 3\n\n    stormParams.lightningMinPeriod = stormParams.lightningMinPeriod !== undefined ? stormParams.lightningMinPeriod : 3.0\n    stormParams.lightningMaxPeriod = stormParams.lightningMaxPeriod !== undefined ? stormParams.lightningMaxPeriod : 7.0\n\n    stormParams.lightningMinDuration =\n      stormParams.lightningMinDuration !== undefined ? stormParams.lightningMinDuration : 1.0\n    stormParams.lightningMaxDuration =\n      stormParams.lightningMaxDuration !== undefined ? stormParams.lightningMaxDuration : 2.5\n\n    this.lightningParameters = LightningStrike.copyParameters(\n      stormParams.lightningParameters,\n      stormParams.lightningParameters,\n    )\n\n    this.lightningParameters.isEternal = false\n\n    this.lightningMaterial =\n      stormParams.lightningMaterial !== undefined\n        ? stormParams.lightningMaterial\n        : new MeshBasicMaterial({ color: 0xb0ffff })\n\n    if (stormParams.onRayPosition !== undefined) {\n      this.onRayPosition = stormParams.onRayPosition\n    } else {\n      this.onRayPosition = function (source, dest) {\n        dest.set((Math.random() - 0.5) * stormParams.size, 0, (Math.random() - 0.5) * stormParams.size)\n\n        const height = MathUtils.lerp(stormParams.minHeight, stormParams.maxHeight, Math.random())\n\n        source\n          .set(stormParams.maxSlope * (2 * Math.random() - 1), 1, stormParams.maxSlope * (2 * Math.random() - 1))\n          .multiplyScalar(height)\n          .add(dest)\n      }\n    }\n\n    this.onLightningDown = stormParams.onLightningDown\n\n    // Internal state\n\n    this.inited = false\n    this.nextLightningTime = 0\n    this.lightningsMeshes = []\n    this.deadLightningsMeshes = []\n\n    for (let i = 0; i < this.stormParams.maxLightnings; i++) {\n      const lightning = new LightningStrike(LightningStrike.copyParameters({}, this.lightningParameters))\n      const mesh = new Mesh(lightning, this.lightningMaterial)\n      this.deadLightningsMeshes.push(mesh)\n    }\n  }\n\n  update(time) {\n    if (!this.inited) {\n      this.nextLightningTime = this.getNextLightningTime(time) * Math.random()\n      this.inited = true\n    }\n\n    if (time >= this.nextLightningTime) {\n      // Lightning creation\n\n      const lightningMesh = this.deadLightningsMeshes.pop()\n\n      if (lightningMesh) {\n        const lightningParams1 = LightningStrike.copyParameters(\n          lightningMesh.geometry.rayParameters,\n          this.lightningParameters,\n        )\n\n        lightningParams1.birthTime = time\n        lightningParams1.deathTime =\n          time +\n          MathUtils.lerp(this.stormParams.lightningMinDuration, this.stormParams.lightningMaxDuration, Math.random())\n\n        this.onRayPosition(lightningParams1.sourceOffset, lightningParams1.destOffset)\n\n        lightningParams1.noiseSeed = Math.random()\n\n        this.add(lightningMesh)\n\n        this.lightningsMeshes.push(lightningMesh)\n      }\n\n      // Schedule next lightning\n      this.nextLightningTime = this.getNextLightningTime(time)\n    }\n\n    let i = 0,\n      il = this.lightningsMeshes.length\n\n    while (i < il) {\n      const mesh = this.lightningsMeshes[i]\n\n      const lightning = mesh.geometry\n\n      const prevState = lightning.state\n\n      lightning.update(time)\n\n      if (prevState === LightningStrike.RAY_PROPAGATING && lightning.state > prevState) {\n        if (this.onLightningDown) {\n          this.onLightningDown(lightning)\n        }\n      }\n\n      if (lightning.state === LightningStrike.RAY_EXTINGUISHED) {\n        // Lightning is to be destroyed\n\n        this.lightningsMeshes.splice(this.lightningsMeshes.indexOf(mesh), 1)\n\n        this.deadLightningsMeshes.push(mesh)\n\n        this.remove(mesh)\n\n        il--\n      } else {\n        i++\n      }\n    }\n  }\n\n  getNextLightningTime(currentTime) {\n    return (\n      currentTime +\n      MathUtils.lerp(this.stormParams.lightningMinPeriod, this.stormParams.lightningMaxPeriod, Math.random()) /\n        (this.stormParams.maxLightnings + 1)\n    )\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.stormParams.size = source.stormParams.size\n    this.stormParams.minHeight = source.stormParams.minHeight\n    this.stormParams.maxHeight = source.stormParams.maxHeight\n    this.stormParams.maxSlope = source.stormParams.maxSlope\n\n    this.stormParams.maxLightnings = source.stormParams.maxLightnings\n\n    this.stormParams.lightningMinPeriod = source.stormParams.lightningMinPeriod\n    this.stormParams.lightningMaxPeriod = source.stormParams.lightningMaxPeriod\n\n    this.stormParams.lightningMinDuration = source.stormParams.lightningMinDuration\n    this.stormParams.lightningMaxDuration = source.stormParams.lightningMaxDuration\n\n    this.lightningParameters = LightningStrike.copyParameters({}, source.lightningParameters)\n\n    this.lightningMaterial = source.stormParams.lightningMaterial\n\n    this.onLightningDown = source.onLightningDown\n\n    return this\n  }\n\n  clone() {\n    return new this.constructor(this.stormParams).copy(this)\n  }\n}\n\nexport { LightningStorm }\n"], "names": [], "mappings": ";;AAiDA,MAAM,uBAAuB,SAAS;AAAA,EACpC,YAAY,cAAc,IAAI;AAC5B,UAAO;AAEP,SAAK,mBAAmB;AAIxB,SAAK,cAAc;AAEnB,gBAAY,OAAO,YAAY,SAAS,SAAY,YAAY,OAAO;AACvE,gBAAY,YAAY,YAAY,cAAc,SAAY,YAAY,YAAY;AACtF,gBAAY,YAAY,YAAY,cAAc,SAAY,YAAY,YAAY;AACtF,gBAAY,WAAW,YAAY,aAAa,SAAY,YAAY,WAAW;AAEnF,gBAAY,gBAAgB,YAAY,kBAAkB,SAAY,YAAY,gBAAgB;AAElG,gBAAY,qBAAqB,YAAY,uBAAuB,SAAY,YAAY,qBAAqB;AACjH,gBAAY,qBAAqB,YAAY,uBAAuB,SAAY,YAAY,qBAAqB;AAEjH,gBAAY,uBACV,YAAY,yBAAyB,SAAY,YAAY,uBAAuB;AACtF,gBAAY,uBACV,YAAY,yBAAyB,SAAY,YAAY,uBAAuB;AAEtF,SAAK,sBAAsB,gBAAgB;AAAA,MACzC,YAAY;AAAA,MACZ,YAAY;AAAA,IACb;AAED,SAAK,oBAAoB,YAAY;AAErC,SAAK,oBACH,YAAY,sBAAsB,SAC9B,YAAY,oBACZ,IAAI,kBAAkB,EAAE,OAAO,UAAU;AAE/C,QAAI,YAAY,kBAAkB,QAAW;AAC3C,WAAK,gBAAgB,YAAY;AAAA,IACvC,OAAW;AACL,WAAK,gBAAgB,SAAU,QAAQ,MAAM;AAC3C,aAAK,KAAK,KAAK,OAAQ,IAAG,OAAO,YAAY,MAAM,IAAI,KAAK,OAAQ,IAAG,OAAO,YAAY,IAAI;AAE9F,cAAM,SAAS,UAAU,KAAK,YAAY,WAAW,YAAY,WAAW,KAAK,QAAQ;AAEzF,eACG,IAAI,YAAY,YAAY,IAAI,KAAK,OAAM,IAAK,IAAI,GAAG,YAAY,YAAY,IAAI,KAAK,OAAM,IAAK,EAAE,EACrG,eAAe,MAAM,EACrB,IAAI,IAAI;AAAA,MACZ;AAAA,IACF;AAED,SAAK,kBAAkB,YAAY;AAInC,SAAK,SAAS;AACd,SAAK,oBAAoB;AACzB,SAAK,mBAAmB,CAAE;AAC1B,SAAK,uBAAuB,CAAE;AAE9B,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,eAAe,KAAK;AACvD,YAAM,YAAY,IAAI,gBAAgB,gBAAgB,eAAe,IAAI,KAAK,mBAAmB,CAAC;AAClG,YAAM,OAAO,IAAI,KAAK,WAAW,KAAK,iBAAiB;AACvD,WAAK,qBAAqB,KAAK,IAAI;AAAA,IACpC;AAAA,EACF;AAAA,EAED,OAAO,MAAM;AACX,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,oBAAoB,KAAK,qBAAqB,IAAI,IAAI,KAAK,OAAQ;AACxE,WAAK,SAAS;AAAA,IACf;AAED,QAAI,QAAQ,KAAK,mBAAmB;AAGlC,YAAM,gBAAgB,KAAK,qBAAqB,IAAK;AAErD,UAAI,eAAe;AACjB,cAAM,mBAAmB,gBAAgB;AAAA,UACvC,cAAc,SAAS;AAAA,UACvB,KAAK;AAAA,QACN;AAED,yBAAiB,YAAY;AAC7B,yBAAiB,YACf,OACA,UAAU,KAAK,KAAK,YAAY,sBAAsB,KAAK,YAAY,sBAAsB,KAAK,QAAQ;AAE5G,aAAK,cAAc,iBAAiB,cAAc,iBAAiB,UAAU;AAE7E,yBAAiB,YAAY,KAAK,OAAQ;AAE1C,aAAK,IAAI,aAAa;AAEtB,aAAK,iBAAiB,KAAK,aAAa;AAAA,MACzC;AAGD,WAAK,oBAAoB,KAAK,qBAAqB,IAAI;AAAA,IACxD;AAED,QAAI,IAAI,GACN,KAAK,KAAK,iBAAiB;AAE7B,WAAO,IAAI,IAAI;AACb,YAAM,OAAO,KAAK,iBAAiB,CAAC;AAEpC,YAAM,YAAY,KAAK;AAEvB,YAAM,YAAY,UAAU;AAE5B,gBAAU,OAAO,IAAI;AAErB,UAAI,cAAc,gBAAgB,mBAAmB,UAAU,QAAQ,WAAW;AAChF,YAAI,KAAK,iBAAiB;AACxB,eAAK,gBAAgB,SAAS;AAAA,QAC/B;AAAA,MACF;AAED,UAAI,UAAU,UAAU,gBAAgB,kBAAkB;AAGxD,aAAK,iBAAiB,OAAO,KAAK,iBAAiB,QAAQ,IAAI,GAAG,CAAC;AAEnE,aAAK,qBAAqB,KAAK,IAAI;AAEnC,aAAK,OAAO,IAAI;AAEhB;AAAA,MACR,OAAa;AACL;AAAA,MACD;AAAA,IACF;AAAA,EACF;AAAA,EAED,qBAAqB,aAAa;AAChC,WACE,cACA,UAAU,KAAK,KAAK,YAAY,oBAAoB,KAAK,YAAY,oBAAoB,KAAK,QAAQ,KACnG,KAAK,YAAY,gBAAgB;AAAA,EAEvC;AAAA,EAED,KAAK,QAAQ,WAAW;AACtB,UAAM,KAAK,QAAQ,SAAS;AAE5B,SAAK,YAAY,OAAO,OAAO,YAAY;AAC3C,SAAK,YAAY,YAAY,OAAO,YAAY;AAChD,SAAK,YAAY,YAAY,OAAO,YAAY;AAChD,SAAK,YAAY,WAAW,OAAO,YAAY;AAE/C,SAAK,YAAY,gBAAgB,OAAO,YAAY;AAEpD,SAAK,YAAY,qBAAqB,OAAO,YAAY;AACzD,SAAK,YAAY,qBAAqB,OAAO,YAAY;AAEzD,SAAK,YAAY,uBAAuB,OAAO,YAAY;AAC3D,SAAK,YAAY,uBAAuB,OAAO,YAAY;AAE3D,SAAK,sBAAsB,gBAAgB,eAAe,CAAE,GAAE,OAAO,mBAAmB;AAExF,SAAK,oBAAoB,OAAO,YAAY;AAE5C,SAAK,kBAAkB,OAAO;AAE9B,WAAO;AAAA,EACR;AAAA,EAED,QAAQ;AACN,WAAO,IAAI,KAAK,YAAY,KAAK,WAAW,EAAE,KAAK,IAAI;AAAA,EACxD;AACH;"}