{"version": 3, "file": "Pass.js", "sources": ["../../src/postprocessing/Pass.ts"], "sourcesContent": ["import { OrthographicCamera, PlaneGeometry, Mesh, Material, WebGLRenderer, WebGLRenderTarget } from 'three'\n\nclass Pass {\n  // if set to true, the pass is processed by the composer\n  public enabled = true\n\n  // if set to true, the pass indicates to swap read and write buffer after rendering\n  public needsSwap = true\n\n  // if set to true, the pass clears its buffer before rendering\n  public clear = false\n\n  // if set to true, the result of the pass is rendered to screen. This is set automatically by EffectComposer.\n  public renderToScreen = false\n\n  public setSize(width: number, height: number): void {}\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    deltaTime: number,\n    maskActive?: unknown,\n  ): void {\n    console.error('THREE.Pass: .render() must be implemented in derived pass.')\n  }\n\n  public dispose() {}\n}\n\n// Helper for passes that need to fill the viewport with a single quad.\nclass FullScreenQuad<TMaterial extends Material = Material> {\n  public camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1)\n  public geometry = new PlaneGeometry(2, 2)\n  private mesh: Mesh<PlaneGeometry, TMaterial>\n\n  constructor(material: TMaterial) {\n    this.mesh = new Mesh(this.geometry, material)\n  }\n\n  public get material(): TMaterial {\n    return this.mesh.material\n  }\n\n  public set material(value: TMaterial) {\n    this.mesh.material = value\n  }\n\n  public dispose(): void {\n    this.mesh.geometry.dispose()\n  }\n\n  public render(renderer: WebGLRenderer): void {\n    renderer.render(this.mesh, this.camera)\n  }\n}\n\nexport { Pass, FullScreenQuad }\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,KAAK;AAAA,EAAX;AAES;AAAA,mCAAU;AAGV;AAAA,qCAAY;AAGZ;AAAA,iCAAQ;AAGR;AAAA,0CAAiB;AAAA;AAAA,EAEjB,QAAQ,OAAe,QAAsB;AAAA,EAAC;AAAA,EAE9C,OACL,UACA,aACA,YACA,WACA,YACM;AACN,YAAQ,MAAM,4DAA4D;AAAA,EAC5E;AAAA,EAEO,UAAU;AAAA,EAAC;AACpB;AAGA,MAAM,eAAsD;AAAA,EAK1D,YAAY,UAAqB;AAJ1B,kCAAS,IAAI,mBAAmB,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;AAClD,oCAAW,IAAI,cAAc,GAAG,CAAC;AAChC;AAGN,SAAK,OAAO,IAAI,KAAK,KAAK,UAAU,QAAQ;AAAA,EAC9C;AAAA,EAEA,IAAW,WAAsB;AAC/B,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EAEA,IAAW,SAAS,OAAkB;AACpC,SAAK,KAAK,WAAW;AAAA,EACvB;AAAA,EAEO,UAAgB;AAChB,SAAA,KAAK,SAAS;EACrB;AAAA,EAEO,OAAO,UAA+B;AAC3C,aAAS,OAAO,KAAK,MAAM,KAAK,MAAM;AAAA,EACxC;AACF;"}