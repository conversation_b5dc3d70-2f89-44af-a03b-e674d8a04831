* {
  box-sizing: border-box;
}

body, html, #root {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #0f2027, #2c5364, #203a43, #0f2027);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  color: #e0e0e0;
  overflow-x: hidden;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.app-bg {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.app-bg::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(0, 255, 231, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 0, 200, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.header {
  text-align: center;
  margin-top: 2rem;
  padding: 0 1rem;
  max-width: 100%;
}

.header h1 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  letter-spacing: 2px;
  color: #00ffe7;
  text-shadow: 0 0 10px #00ffe7aa, 0 0 20px #00ffe755, 0 0 30px #00ffe733;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #00ffe7, #ff00c8, #00ffe7);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes textGlow {
  from { background-position: 0% 50%; }
  to { background-position: 100% 50%; }
}

.header p {
  font-size: clamp(1rem, 2.5vw, 1.3rem);
  margin: 0.5rem 0;
  opacity: 0.9;
}

.header .tagline {
  font-size: clamp(0.9rem, 2vw, 1.2rem);
  color: #ff00c8;
  margin-top: 0.5rem;
  font-weight: 600;
  text-shadow: 0 0 5px #ff00c8aa;
}

.about {
  margin: 2rem 1rem;
  background: rgba(20, 20, 40, 0.8);
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 0 30px rgba(0, 255, 231, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 255, 231, 0.2);
  max-width: 800px;
  width: 100%;
}

.about h2 {
  color: #00ffe7;
  font-size: clamp(1.5rem, 3vw, 2rem);
  margin-bottom: 1rem;
  text-align: center;
}

.about p {
  line-height: 1.6;
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  text-align: center;
}

.features {
  margin: 3rem 1rem;
  max-width: 1200px;
  width: 100%;
}

.features h2 {
  color: #00ffe7;
  font-size: clamp(1.5rem, 3vw, 2rem);
  text-align: center;
  margin-bottom: 2rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  padding: 0 1rem;
}

.feature-card {
  background: rgba(20, 20, 40, 0.8);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  border: 1px solid rgba(0, 255, 231, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 231, 0.1), transparent);
  transition: left 0.5s ease;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 40px rgba(0, 255, 231, 0.4);
  border-color: #00ffe7;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 10px #00ffe7aa);
}

.feature-card h3 {
  color: #00ffe7;
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.feature-card p {
  line-height: 1.6;
  opacity: 0.9;
}

.footer {
  margin-top: auto;
  padding: 2rem 1rem;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  width: 100%;
  border-top: 1px solid rgba(0, 255, 231, 0.2);
}

.footer p {
  margin: 0;
  font-size: clamp(0.8rem, 1.5vw, 1rem);
  opacity: 0.8;
}

.loading-screen {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #0f2027, #2c5364);
  position: relative;
}

.loading-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(0, 255, 231, 0.1) 0%, transparent 70%);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

.loading-screen span {
  font-size: clamp(1rem, 2.5vw, 1.3rem);
  color: #00ffe7;
  text-shadow: 0 0 10px #00ffe7aa;
  z-index: 1;
}

.loader {
  width: clamp(50px, 8vw, 80px);
  height: clamp(50px, 8vw, 80px);
  border: 6px solid rgba(0, 255, 231, 0.3);
  border-top: 6px solid #00ffe7;
  border-right: 6px solid #ff00c8;
  border-radius: 50%;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.cube-container {
  perspective: clamp(400px, 80vw, 800px);
  margin: 3rem auto;
  height: clamp(100px, 20vw, 150px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.cube {
  width: clamp(80px, 15vw, 120px);
  height: clamp(80px, 15vw, 120px);
  position: relative;
  transform-style: preserve-3d;
  transform: rotateX(-30deg) rotateY(30deg);
  margin: auto;
  filter: drop-shadow(0 0 20px rgba(0, 255, 231, 0.5));
}

.face {
  position: absolute;
  width: clamp(80px, 15vw, 120px);
  height: clamp(80px, 15vw, 120px);
  background: linear-gradient(135deg, rgba(44, 83, 100, 0.9), rgba(15, 32, 39, 0.9));
  border: 2px solid #00ffe7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  color: #fff;
  box-shadow: 0 0 20px rgba(0, 255, 231, 0.4), inset 0 0 20px rgba(0, 255, 231, 0.1);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.face:hover {
  background: linear-gradient(135deg, rgba(0, 255, 231, 0.3), rgba(255, 0, 200, 0.3));
  box-shadow: 0 0 30px rgba(0, 255, 231, 0.8), inset 0 0 30px rgba(0, 255, 231, 0.2);
}

.front  { transform: rotateY(0deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.back   { transform: rotateY(180deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.right  { transform: rotateY(90deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.left   { transform: rotateY(-90deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.top    { transform: rotateX(90deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }
.bottom { transform: rotateX(-90deg) translateZ(calc(clamp(80px, 15vw, 120px) / 2)); }

/* Floating particles effect */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #00ffe7;
  border-radius: 50%;
  animation: float 6s linear infinite;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-10vh) rotate(360deg);
    opacity: 0;
  }
}

/* Navigation Styles */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(15, 32, 39, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 255, 231, 0.2);
  padding: 1rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.nav-logo span {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00ffe7;
  text-shadow: 0 0 10px #00ffe7aa;
}

.desktop-menu {
  display: flex;
  gap: 2rem;
}

.desktop-menu a {
  color: #e0e0e0;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.desktop-menu a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #00ffe7;
  transition: width 0.3s ease;
}

.desktop-menu a:hover {
  color: #00ffe7;
}

.desktop-menu a:hover::after {
  width: 100%;
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu-btn span {
  width: 25px;
  height: 3px;
  background: #00ffe7;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobile-menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1001;
  display: none;
  opacity: 0;
}

.mobile-menu {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: linear-gradient(135deg, #0f2027, #2c5364);
  transform: translateX(100%);
  display: flex;
  flex-direction: column;
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid rgba(0, 255, 231, 0.2);
}

.mobile-menu-header span {
  font-size: 1.3rem;
  color: #00ffe7;
  font-weight: bold;
}

.mobile-menu-header button {
  background: none;
  border: none;
  color: #00ffe7;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-menu-items {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.mobile-menu-items a {
  color: #e0e0e0;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.mobile-menu-items a:hover {
  color: #00ffe7;
  background: rgba(0, 255, 231, 0.1);
  border-color: rgba(0, 255, 231, 0.3);
}

/* Contact Section Styles */
.contact-section {
  margin: 4rem 1rem;
  max-width: 1200px;
  width: 100%;
}

.contact-container {
  text-align: center;
}

.contact-section h2 {
  color: #00ffe7;
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  margin-bottom: 1rem;
  text-shadow: 0 0 10px #00ffe7aa;
}

.contact-section > .contact-container > p {
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  margin-bottom: 3rem;
  opacity: 0.9;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.contact-card {
  background: rgba(20, 20, 40, 0.8);
  padding: 2rem;
  border-radius: 15px;
  border: 1px solid rgba(0, 255, 231, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 231, 0.1), transparent);
  transition: left 0.5s ease;
}

.contact-card:hover::before {
  left: 100%;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 255, 231, 0.3);
  border-color: #00ffe7;
}

.contact-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 10px #00ffe7aa);
}

.contact-card h3 {
  color: #00ffe7;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.contact-card p {
  margin-bottom: 1rem;
  opacity: 0.9;
}

.contact-link {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: linear-gradient(45deg, #00ffe7, #ff00c8);
  color: #000;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-link:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 255, 231, 0.4);
}

.cta-section {
  background: rgba(20, 20, 40, 0.9);
  padding: 3rem 2rem;
  border-radius: 20px;
  border: 1px solid rgba(0, 255, 231, 0.3);
  backdrop-filter: blur(15px);
  position: relative;
}

.cta-section h3 {
  color: #00ffe7;
  font-size: clamp(1.5rem, 3vw, 2rem);
  margin-bottom: 1rem;
}

.cta-section p {
  font-size: clamp(1rem, 2vw, 1.1rem);
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta-button {
  background: linear-gradient(45deg, #00ffe7, #ff00c8, #00ffe7);
  background-size: 200% 200%;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  color: #000;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: gradientShift 3s ease infinite;
}

.cta-button:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 255, 231, 0.5);
}

.button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.cta-button:hover .button-glow {
  left: 100%;
}

/* Adjust header margin for fixed nav */
.header {
  margin-top: 6rem;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  .desktop-menu {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .nav-container {
    padding: 0 1rem;
  }

  .header {
    margin-top: 5rem;
  }

  .about, .features {
    margin: 1.5rem 0.5rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 0.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .cube-container {
    margin: 2rem auto;
  }

  .mobile-menu {
    width: 280px;
  }
}

@media (max-width: 480px) {
  .header h1 {
    letter-spacing: 1px;
  }

  .header {
    margin-top: 4.5rem;
  }

  .about, .features {
    margin: 1rem 0.25rem;
  }

  .about {
    padding: 1.5rem;
  }

  .feature-card {
    padding: 1.25rem;
  }

  .footer {
    padding: 1.5rem 0.5rem;
  }

  .mobile-menu {
    width: 100%;
  }

  .nav-logo span {
    font-size: 1.3rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .contact-card {
    padding: 1.5rem;
  }

  .cta-section {
    padding: 2rem 1rem;
  }

  .cta-button {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}