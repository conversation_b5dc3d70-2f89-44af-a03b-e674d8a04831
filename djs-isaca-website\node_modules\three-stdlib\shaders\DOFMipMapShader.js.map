{"version": 3, "file": "DOFMipMapShader.js", "sources": ["../../src/shaders/DOFMipMapShader.ts"], "sourcesContent": ["/**\n * Depth-of-field shader using mipmaps\n * - from <PERSON> @applmak\n * - requires power-of-2 sized render target with enabled mipmaps\n */\n\nexport const DOFMipMapShader = {\n  uniforms: {\n    tColor: { value: null },\n    tDepth: { value: null },\n    focus: { value: 1.0 },\n    maxblur: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float focus;\n    uniform float maxblur;\n\n    uniform sampler2D tColor;\n    uniform sampler2D tDepth;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 depth = texture2D( tDepth, vUv );\n\n    \tfloat factor = depth.x - focus;\n\n    \tvec4 col = texture2D( tColor, vUv, 2.0 * maxblur * abs( focus - depth.x ) );\n\n    \tgl_FragColor = col;\n    \tgl_FragColor.a = 1.0;\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AAMO,MAAM,kBAAkB;AAAA,EAC7B,UAAU;AAAA,IACR,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,OAAO,EAAE,OAAO,EAAI;AAAA,IACpB,SAAS,EAAE,OAAO,EAAI;AAAA,EACxB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsB7B;"}